import com.android.build.gradle.tasks.ProcessApplicationManifest
import com.howbuy.activity.guard.asm.Params
import com.howbuy.android.activity.guard.ActivityGuardAsm
import com.howbuy.android.activity.guard.plugin.GuardTransform
import com.howbuy.android.asm.ActivityConfig
import com.howbuy.android.asm.ActivityResultDispatcher
import com.howbuy.android.asm.ClearLogcat
import com.howbuy.android.plugin.AsmImplConfigLoader
import com.howbuy.android.plugin.UnionPluginConfig
import com.howbuy.android.trace.TransformStatic
import com.howbuy.android.transform.ClassTransform
import com.howbuy.asm.Log
import org.jetbrains.annotations.NotNull

plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'

    id 'com.alibaba.arouter' //arouter
    id "com.howbuy.android.unionplugin" //好买统一Gradle插件
}

apply plugin: "com.howbuy.android.devos"

def clearLogcat = false
def isNotDebug = true
gradle.startParameter.taskNames.each({
    String taskNameL = it.toLowerCase()
    if (taskNameL.contains("debug") || taskNameL.contains("hbTest")) {
        isNotDebug = false
    }
    //只有debug\test包才能打印logcat，其余的build type都会被清除
    println("taskNameL:" + taskNameL)
    clearLogcat = !(taskNameL.contains("debug") || taskNameL.contains("hbTest"))
})
println("清除logcat打印：" + clearLogcat)

UnionPluginConfig.getTransformImplList().clear()
//Activity生命周期保护ASM逻辑实现
UnionPluginConfig.addClassTransformImpl(new ActivityGuardAsm())
if (clearLogcat) {
    println("清除logcat打印")
    //清除logcat打印
    UnionPluginConfig.addClassTransformImpl(new ClearLogcat())
}
//分发ActivityResult
UnionPluginConfig.addClassTransformImpl(new ActivityResultDispatcher() {
    void onTransformStart() {
        //添加列表
        ActivityConfig.activityClassNameList.addAll(Params.activityList)
        super.onTransformStart()
    }
})
TransformStatic.setOff(true) //默认关闭插件统计
//Activity生命周期保护插件配置逻辑:获取合并后的manifest文件
UnionPluginConfig.asmImplConfigLoaderMap.put(ActivityGuardAsm.class.simpleName, new AsmImplConfigLoader() {
    @Override
    void loadConfigFor(@NotNull ClassTransform classTransform, Project project) {
        project.gradle.taskGraph.afterTask(new Action<Task>() {
            boolean found = false

            @Override
            void execute(Task task) {
                if (!(task instanceof ProcessApplicationManifest)) {
                    return
                }
                if (found) {
                    Log.i("GuardPlugin", "no need search manifest again")
                    return
                }
                if (isNotDebug) { //非Debug包才执行Activity生命周期的ASM代码插桩
                    Params.clearBundle = true
                }
                ProcessApplicationManifest processApplicationManifest = task
                String mergedManifestPath = null
                try {
                    mergedManifestPath = processApplicationManifest.manifestOutputDirectory.get().toString() + File.separator + "AndroidManifest.xml"
                    Log.i("GuardPlugin", "merged manifest file found in old version, path:${mergedManifestPath}")
                } catch (Exception ignore) {
                    /**
                     * 注意：经测试，build.gradle:4.1.0之后的版本使用上面的方式获取manifest文件路径将失败。需要使用以下方式进行获取.
                     * 因此，增加兼容获取方案.
                     */
                    try {
                        mergedManifestPath = processApplicationManifest.mergedManifest.asFile.get()
                        Log.i("GuardPlugin", "merged manifest file found in new version, path:${mergedManifestPath}")
                    } catch (Exception ignored) {
                    }
                }

                if (null != mergedManifestPath) {
                    ActivityGuardAsm.mergedManifestPath = mergedManifestPath
                    GuardTransform.mergedManifestPath = mergedManifestPath
                    found = true
                } else {
                    Log.e(TAG, "merged manifest file not found!!")
                }
            }
        })
    }
})
//<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<统一插件配置结束

android {
    namespace 'howbuy.android.global'
    compileSdkVersion rootProject.ext.commonVersions.compileSdkVersion
    flavorDimensions rootProject.ext.commonVersions.flavorDimensions

    defaultConfig {
        applicationId "howbuy.android.global"
        minSdkVersion rootProject.ext.commonVersions.minSdkVersion
        targetSdkVersion rootProject.ext.commonVersions.targetSdkVersion
        versionCode 32  //2.6.1版本对应 27
        versionName "3.0.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    signingConfigs {
        debug {
            storeFile rootProject.file("./app/keystore/debug.jks")
            storePassword "howbuydev2007"
            keyAlias "howbuy"
            keyPassword "howbuydev2007"
            v2SigningEnabled true
        }
        release {
            storeFile rootProject.file("./app/keystore/howbuyglobal.jks")
            storePassword "howbuydev2007"
            keyAlias "howbuyglobal"
            keyPassword "howbuydev2007"
            v2SigningEnabled true
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    buildFeatures {
        viewBinding true
    }
    packagingOptions {
        exclude 'META-INF/beans.xml'
    }
    sourceSets {
        debug {
            java.srcDirs = ['src/main/java', 'build/generated/data_binding_base_class_source_out/debug/out']
            manifest.srcFile 'src/main/AndroidManifest.xml'
        }
        hbTest {
            java.srcDirs = ['src/main/java', 'build/generated/data_binding_base_class_source_out/hbTest/out']
            manifest.srcFile 'src/main/AndroidManifest.xml'
        }
        pre {
            java.srcDirs = ['src/main/java', 'build/generated/data_binding_base_class_source_out/pre/out']
            manifest.srcFile 'src/main/AndroidManifest.xml'
        }
        beta {
            java.srcDirs = ['src/main/java', 'build/generated/data_binding_base_class_source_out/beta/out']
            manifest.srcFile 'src/main/AndroidManifest.xml'
        }
        release {
            java.srcDirs = ['src/main/java', 'build/generated/data_binding_base_class_source_out/release/out']
            manifest.srcFile 'src/main/AndroidManifest.xml'
        }
    }
}

apply from: "${rootProject.projectDir}/arouter-kotlin-config.gradle"
apply from: "${rootProject.projectDir}/module-config.gradle"
apply from: "${rootProject.projectDir}/xg_config.gradle"

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')

    implementation project(path: ':global-common')
    implementation project(path: ':login_impl')
    implementation project(path: ':global-user')
    implementation project(path: ':data-api')
    implementation project(path: ':login-api')
    implementation project(path: ':global-base')
    implementation project(path: ':arouter_intercept_api')
    implementation project(path: ':share_dialog')
    implementation project(path: ':share_api')
    implementation project(path: ':cmd_api')
    implementation project(path: ':arouter_intercept_impl')
    implementation project(path: ':analytics_api')
    implementation project(path: ':logupload')
    implementation project(path: ':fund-chart')


    implementation rootProject.ext.dependencies["wheelpicker"]
    rootProject.ext.depUtil.addDependency('hBComponent', dependencies)
    implementation project(path: ':hBRouter')
    rootProject.ext.depUtil.implementation("share_api", dependencies)

    implementation rootProject.ext.dependencies['hb-refresh']
    implementation rootProject.ext.dependencies['hb-scanner']
    implementation rootProject.ext.dependencies["mpandroid-chart"]
    implementation rootProject.ext.dependencies['hbBanner']
    implementation(rootProject.ext.dependencies['loginobserver'])


    //webview 相关
    rootProject.ext.depUtil.implementation("fund-agentweb", dependencies)
    rootProject.ext.depUtil.implementation("agentweb_api", dependencies)

    rootProject.ext.depUtil.implementation("net_api", dependencies)

    implementation rootProject.ext.dependencies["hb-net"]
    implementation rootProject.ext.dependencies["hb-h5zip"]
    implementation rootProject.ext.dependencies["user_data_api"]
    implementation rootProject.ext.dependencies["user_data_core"]

    implementation rootProject.ext.dependencies["hb-analysis"]
    implementation rootProject.ext.dependencies["hb-gesture"]
    implementation rootProject.ext.dependencies["hb-toast"]
    implementation rootProject.ext.dependencies['hb-utils']
    implementation(rootProject.ext.dependencies["permission"])
    implementation rootProject.ext.dependencies['hb-imgloader']
    //debug 后门修改地址
    implementation rootProject.ext.dependencies['modify-url']
    implementation rootProject.ext.dependencies['rxandroid']
    implementation rootProject.ext.dependencies['flex-box']

    //viewbinding
    implementation rootProject.ext.dependencies['bindingcollectionadapter']
    implementation rootProject.ext.dependencies['bindingcollectionadapter-recyclerview']


    //external
    implementation rootProject.ext.dependencies["support-appcompat"]
    implementation rootProject.ext.dependencies["support-design"]
    implementation rootProject.ext.dependencies["constraint-layout"]
    implementation(rootProject.ext.dependencies["recyclerView-adapter-helper"])
    //自选要用(比较日期时用到)
    implementation rootProject.ext.dependencies['joda-time']

    //视频播放(腾讯播放器)
    implementation rootProject.ext.dependencies['liteavsdk']
    implementation rootProject.ext.dependencies['hb_shortvideo']
    //vhall直播
    implementation 'com.github.vhall.android.library:vh-saas-sdk:6.19.3'
    implementation 'com.github.vhall.android.library:vh-saas-interactive:6.19.3'
    //投屏
    implementation 'com.github.vhall.android.library:vh-saas-sdk-support:2.0.1'

    debugImplementation rootProject.ext.dependencies["debugLeakCanary"]
}