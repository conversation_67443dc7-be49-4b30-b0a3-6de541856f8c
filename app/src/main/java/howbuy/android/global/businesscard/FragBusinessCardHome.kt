package howbuy.android.global.businesscard

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ObjectAnimator
import android.app.Activity
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.SpannableString
import android.text.Spanned
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AccelerateInterpolator
import android.view.animation.DecelerateInterpolator
import android.widget.Toast
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.content.ContextCompat
import androidx.core.util.Consumer
import androidx.core.widget.NestedScrollView
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.facade.annotation.Route
import com.howbuy.android.analytics.annotation.pv.PvInfo
import com.howbuy.android.arch.ResultListener
import com.howbuy.dialog.DlgHelper
import com.howbuy.fund.base.analytics.HbAnalytics
import com.howbuy.fund.base.arch.ShareViewModelProvider
import com.howbuy.fund.base.arch.ViewModelHelper
import com.howbuy.fund.base.config.ValConfig
import com.howbuy.fund.base.frag.AbsFragViewBinding
import com.howbuy.fund.base.nav.NavHelper
import com.howbuy.fund.base.router.AtyBridgeHelper
import com.howbuy.fund.base.router.RouterHelper
import com.howbuy.fund.base.utils.FundTextUtils
import com.howbuy.fund.base.utils.ImgHelper
import com.howbuy.fund.base.utils.dp2px
import com.howbuy.fund.base.utils.sp2px
import com.howbuy.fund.base.utils.span.SpannableItem
import com.howbuy.fund.base.utils.span.SpannableUtils
import com.howbuy.fund.base.widget.CenterImageSpan
import com.howbuy.lib.compont.GlobalApp
import com.howbuy.lib.interfaces.IShareActionListener
import com.howbuy.lib.interfaces.IShareHelper
import com.howbuy.lib.interfaces.IShareProvider
import com.howbuy.lib.utils.LogUtils
import com.howbuy.lib.utils.MathUtils
import com.howbuy.lib.utils.ScreenShotUtils
import com.howbuy.lib.utils.StatusBarUtil
import com.howbuy.router.proxy.Invoker
import com.howbuy.share.entity.ShareItem
import com.howbuy.share.entity.ShareMoreEntity
import com.howbuy.share.entity.WorkWeixinEntity
import howbuy.android.global.PATH_FRAG_BUSINESS_CARD_EDIT
import howbuy.android.global.PATH_FRAG_BUSINESS_CARD_HOME
import howbuy.android.global.PATH_FRAG_SEARCH_HEADLINES
import howbuy.android.global.PATH_SEARCH
import howbuy.android.global.R
import howbuy.android.global.businesscard.adapter.AdpBusinessCardModule
import howbuy.android.global.businesscard.adapter.TagAdpBusinessCard
import howbuy.android.global.businesscard.dialog.BusinessCardShareHintDlg
import howbuy.android.global.businesscard.dialog.CardStyleSelectorDlg
import howbuy.android.global.businesscard.dialog.ModuleSortDlg
import howbuy.android.global.businesscard.model.BusinessCardInfo
import howbuy.android.global.businesscard.model.BusinessCardModule
import howbuy.android.global.businesscard.model.CardStyle
import howbuy.android.global.businesscard.model.ModuleType
import howbuy.android.global.databinding.FragBusinessCardHomeBinding
import howbuy.android.global.databinding.LayoutBusinessCardContentBinding
import howbuy.android.global.utils.SmBitmapUtils
import howbuy.android.global.widgets.tag.TagLayout
import html5.screenshot.GlobalCreatePosterPreviewerUtils

/**
 * 持牌人名片主页
 */
@PvInfo(pageId = "622150", level = "3", name = "持牌人名片主页", className = "FragBusinessCardHome")
@Route(path = PATH_FRAG_BUSINESS_CARD_HOME)
class FragBusinessCardHome : AbsFragViewBinding<FragBusinessCardHomeBinding>() {

    private val TAG = "FragBusinessCardHome"
    private lateinit var layoutCardContentBinding: LayoutBusinessCardContentBinding
    private lateinit var moduleAdapter: AdpBusinessCardModule

    //主信息ViewModel
    private val vmBusinessCardHome by lazy {
        ShareViewModelProvider.get(this, VmBusinessCardHome::class.java)
    }

    // 热点相关的ViewModel
    private val hotNewsViewModel by lazy {
        ShareViewModelProvider.get(this, VmBusinessCardHotNews::class.java)
    }

    // 产品相关的ViewModel
    private val productsViewModel by lazy {
        ShareViewModelProvider.get(this, VmBusinessCardProducts::class.java)
    }

    //头像上传
    private val vmBusinessUpLoad by lazy {
        ViewModelHelper.createViewModel(this, VmBusinessCardUpImage::class.java)
    }

    private val launchResult = ResultListener { resultCode, data ->
        if (resultCode == Activity.RESULT_OK) {
            //是否有图片地址,从预览页回来的后
            val pathFromPreview = data?.getStringExtra(FragBusinessUpImage.KEY_PATH)

            if (!pathFromPreview.isNullOrEmpty()) {
                LogUtils.d(TAG, "确认更换头像,地址:$pathFromPreview")
                showLoading(true)
                vmBusinessUpLoad?.updateBusinessImage(
                    pathFromPreview,
                    vmBusinessCardHome.businessCardInfo.value
                ) { imageUrl ->
                    showLoading(false)
                    if (TextUtils.isEmpty(imageUrl)) {
                        return@updateBusinessImage
                    }
                    vmBusinessUpLoad?.imagePath = imageUrl
                    vmBusinessCardHome.updateAvatar(imageUrl)
                }
            }
        }
        true
    }

    // 底部卡片动画相关
    private var isBottomCardVisible = true
    private var isScrolling = false
    private val handler = Handler(Looper.getMainLooper())
    private val scrollIdleRunnable = Runnable {
        if (!isBottomCardVisible) {
            showBottomCard()
        }
        isScrolling = false
    }

    override fun getFragLayoutId(): Int {
        return R.layout.frag_business_card_home
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        vmBusinessUpLoad.registerActivityResultLaunchers(this)
    }

    override fun stepAllViews(root: View?, savedInstanceState: Bundle?) {
        super.stepAllViews(root, savedInstanceState)
        layoutCardContentBinding = binding.layoutCardContent
        // 设置ActionBar为白色
        val toolBarColor = ContextCompat.getColor(requireContext(), android.R.color.white)
        StatusBarUtil.setStatusBarLightMode(activity, toolBarColor, true)
        AtyBridgeHelper.getAtyEmptyApi(activity ?: return).setToolbarTitle("持牌人名片")
        AtyBridgeHelper.getAtyEmptyApi(activity ?: return).setToolbarLineVisibility(false)
        AtyBridgeHelper.getAtyEmptyApi(activity ?: return).actionBarToolBar?.setBackgroundColor(toolBarColor)

        // 初始化时隐藏主界面内容，等待数据加载完成
        hideMainContent()
        // 初始化模块列表
        initModuleList()
        initListeners()

        // 确保底部卡片初始状态可见（当显示主界面时）
        binding.layoutBottomCard.post {
            binding.layoutBottomCard.translationY = 0f
            isBottomCardVisible = true
        }
    }

    /**
     * 初始化模块列表
     */
    private fun initModuleList() {
        // 创建模块适配器
        moduleAdapter = AdpBusinessCardModule()

        // 设置模块排序监听器
        moduleAdapter.setOnModuleSortListener(object : AdpBusinessCardModule.OnModuleSortListener {
            override fun onModuleSort() {
                showModuleSortDialog()
            }
        })

        // 设置模块开关监听器
        moduleAdapter.setOnModuleSwitchListener(object : AdpBusinessCardModule.OnModuleSwitchListener {
            override fun onModuleSwitch(module: BusinessCardModule, isChecked: Boolean) {
                HbAnalytics.onClick("613690",module.title)
                vmBusinessCardHome.toggleModuleEnabled(module.type, isChecked)
            }
        })

        // 设置模块编辑监听器
        moduleAdapter.setOnModuleEditListener(object : AdpBusinessCardModule.OnModuleEditListener {
            override fun onModuleEdit(module: BusinessCardModule) {
                HbAnalytics.onClick("613710",module.title)
                when (module.type) {
                    ModuleType.HOT_NEWS -> {
                        // 编辑全球热点
                        // 使用现有搜索页面，传入编辑模式参数
                        RouterHelper.launchFrag(
                            activity,
                            PATH_FRAG_SEARCH_HEADLINES,
                            NavHelper.obtainArg("", ValConfig.IT_BOOLEAN, true)
                        )
                    }

                    ModuleType.PRODUCTS -> {
                        // 编辑推荐产品
                        RouterHelper.launchFrag(activity, PATH_SEARCH, NavHelper.obtainArg("", ValConfig.IT_BOOLEAN, true))
                    }

                    else -> {}
                }
            }
        })

        // 设置模块清空监听器
        moduleAdapter.setOnModuleClearListener(object : AdpBusinessCardModule.OnModuleClearListener {
            override fun onModuleClear(module: BusinessCardModule) {
                HbAnalytics.onClick("613700",module.title)
                when (module.type) {
                    ModuleType.HOT_NEWS -> {
                        showClearHotNewsConfirmDialog()
                    }

                    ModuleType.PRODUCTS -> {
                        showClearProductsConfirmDialog()
                    }

                    else -> {}
                }
            }
        })

        // 设置模块快捷添加监听器
        moduleAdapter.setOnModuleQuickAddListener(object : AdpBusinessCardModule.OnModuleQuickAddListener {
            override fun onModuleQuickAdd(module: BusinessCardModule) {
                when (module.type) {
                    ModuleType.HOT_NEWS -> {
                        // 快捷添加全球热点
                        vmBusinessCardHome.quickAddModuleContent(ModuleType.HOT_NEWS)
                    }

                    ModuleType.PRODUCTS -> {
                        // 快捷添加推荐产品
                        vmBusinessCardHome.quickAddModuleContent(ModuleType.PRODUCTS)
                    }

                    else -> {}
                }
            }
        })

        // 初始化RecyclerView
        binding.rvModules.layoutManager = LinearLayoutManager(context)
        binding.rvModules.adapter = moduleAdapter
    }

    override fun parseArgment(arg: Bundle?) {
        // 解析传入参数
        // 设置ViewModel的lifecycleOwner
//        vmBusinessCardHome.setLifecycleOwner(this)
        // 初始化子ViewModel
        vmBusinessCardHome.initViewModels(hotNewsViewModel, productsViewModel)
        // 初始化观察者
        initObservers()
        // 初始化数据
        vmBusinessCardHome.initData()
    }

    /**
     * 隐藏主界面内容
     */
    private fun hideMainContent() {
        // 隐藏滚动视图和底部卡片
        binding.nestedScrollView.visibility = View.GONE
        binding.layoutBottomCard.visibility = View.GONE
    }

    /**
     * 显示主界面内容
     */
    private fun showMainContent() {
        // 显示滚动视图和底部卡片
        binding.nestedScrollView.visibility = View.VISIBLE
        binding.layoutBottomCard.visibility = View.VISIBLE
    }

    private fun initObservers() {
        // 观察加载状态
        vmBusinessCardHome.loadingState.observe(this, Observer { state ->
            when (state) {
                is VmBusinessCardHome.UiState.Loading -> {
                    // 隐藏主界面内容
                    hideMainContent()
                    // 显示加载中状态
                    showLoadingView()
                }

                is VmBusinessCardHome.UiState.Success -> {
                    // 加载成功，隐藏加载中状态
                    hideLoadingView()
                    // 显示主界面内容
                    showMainContent()
                }

                is VmBusinessCardHome.UiState.Empty -> {
                    // 隐藏主界面内容
                    hideMainContent()
                    // 隐藏加载中状态
                    hideLoadingView()
                    // 显示空页面
                    showEmptyView("暂无数据", ContextCompat.getDrawable(requireContext(), R.mipmap.gh_jjda_empty_img))
                }
            }
        })

        // 观察持牌人名片信息
        vmBusinessCardHome.businessCardInfo.observe(this, Observer { info ->
            initCardInfo(info, layoutCardContentBinding, false)
            updateShareInfoText(info.sendNum, info.pvNum, info.likeNum)
        })

        // 单独观察分享次数变化，只更新分享次数相关的UI
        vmBusinessCardHome.shareCount.observe(this, Observer { shareCount ->
            // 只更新分享次数，保留其他数据
            val info = vmBusinessCardHome.businessCardInfo.value ?: return@Observer
            updateShareInfoText(shareCount, info.pvNum, info.likeNum)
            LogUtils.d(TAG, "分享次数更新UI: $shareCount")
        })

        // 观察头像变化
        vmBusinessCardHome.avatar.observe(this, Observer { avatarUrl ->
            LogUtils.d(TAG, "avatar changed: $avatarUrl")
            updateAvatar(avatarUrl)
        })

        // 更新标签列表
        vmBusinessCardHome.tagList.observe(this, Observer { info ->
            initTagList(info, layoutCardContentBinding.layoutTags)
        })

        // 观察当前名片样式
        vmBusinessCardHome.currentCardStyle.observe(this, Observer { style ->
            LogUtils.d(TAG, "currentCardStyle: $style")
            // 更新名片样式UI
            updateCardStyle(style, layoutCardContentBinding, false)
        })

        // 观察模块列表
        vmBusinessCardHome.moduleList.observe(this, Observer { modules ->
            LogUtils.d(TAG, "moduleList size: ${modules.size}")
            // 更新模块列表
            moduleAdapter.setList(modules)
        })

        // 观察单项模块更新事件
        vmBusinessCardHome.moduleItemUpdateEvent.observe(this, Observer { (index, module) ->
            LogUtils.d(TAG, "单项模块更新: 索引=$index, 类型=${module.type.name}, 状态=${module.enabled}")
            // 只更新单个项目
            moduleAdapter.data[index] = module
            moduleAdapter.notifyItemChanged(index)
        })
        vmBusinessCardHome.loading.observe(this) {
            // 只有当前可见的Fragment才处理事件
            if (isVisible && isResumed) {
                showLoading(it)
            }
        }
        hotNewsViewModel.loading.observe(this) {
            // 只有当前可见的Fragment才处理事件
            if (isVisible && isResumed) {
                showLoading(it)
            }
        }
        productsViewModel.loading.observe(this, Observer {
            if (isVisible && isResumed) {
                showLoading(it)
            }
        })
        vmBusinessUpLoad.selectImageResult = Consumer {
            if (it != null) {
                vmBusinessUpLoad.launchResult(it, this@FragBusinessCardHome, launchResult)
            }
        }
    }

    private fun showLoading(show: Boolean) {
        if (show) {
            showAlermDlg("加载中...", false, false)
        } else {
            showAlermDlg(null, 0)
        }
    }

    /**
     * 更新分享信息文本
     * 将分享信息文本的更新提取为单独的方法，便于单独更新分享次数
     */
    private fun updateShareInfoText(sendNum: String?, pvNum: String?, likeNum: String?) {
        binding.tvShareInfo.text = SpannableUtils.formatStr(
            SpannableItem("发送名片次数 "),
            SpannableItem(
                FundTextUtils.formatMaxNum("", sendNum, 999, "", "0"), 14f.sp2px(),
                ContextCompat.getColor(requireContext(), R.color.cl_333333), true
            ),
            SpannableItem("   | 名片访客数 "),
            SpannableItem(
                FundTextUtils.formatMaxNum("", pvNum, 999, "", "0"), 14f.sp2px(),
                ContextCompat.getColor(requireContext(), R.color.cl_333333), true
            ),
            SpannableItem("   | 收到点赞数 "), SpannableItem(
                FundTextUtils.formatMaxNum("", likeNum, 999, "", "0"), 14f.sp2px(),
                ContextCompat.getColor(requireContext(), R.color.cl_333333), true
            )
        )
    }

    private fun initListeners() {
        //选择图片上传头像
        layoutCardContentBinding.layAvatar.setOnClickListener {
            if (vmBusinessCardHome.avatar.value.isNullOrEmpty()) {
                HbAnalytics.onClick("613670")
                vmBusinessUpLoad?.showSelectDialog(this@FragBusinessCardHome)
            }
        }

        // 名片样式选择器点击事件
        layoutCardContentBinding.layoutCardStyleSelector.setOnClickListener {
            HbAnalytics.onClick("613680")
            showCardStyleSelector()
        }

        // 发送名片按钮点击事件
        binding.btnSendCard.setOnClickListener {
            HbAnalytics.onClick("613730")
            showShareOptions()
        }

        // 添加滚动监听
        binding.nestedScrollView.setOnScrollChangeListener(NestedScrollView.OnScrollChangeListener { v, scrollX, scrollY, oldScrollX, oldScrollY ->
            // 检测是否正在滚动
            if (scrollY != oldScrollY) {
                isScrolling = true
                // 如果底部卡片可见，则隐藏它
                if (isBottomCardVisible) {
                    hideBottomCard()
                }
                // 重置静止检测
                handler.removeCallbacks(scrollIdleRunnable)
                handler.postDelayed(scrollIdleRunnable, 500) // 500ms后检测是否静止
            }
        })
    }

    /**
     * 显示底部卡片
     */
    private fun showBottomCard() {
        val bottomCard = binding.layoutBottomCard
        val height = bottomCard.height.toFloat()

        // 如果高度为0，说明视图还没有测量完成，延迟执行
        if (height <= 0) {
            bottomCard.post {
                showBottomCard()
            }
            return
        }

        // 创建动画，从下方滑入
        val animator = ObjectAnimator.ofFloat(bottomCard, "translationY", height, 0f)
        animator.duration = 300
        animator.interpolator = DecelerateInterpolator()
        animator.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationStart(animation: Animator) {
                bottomCard.visibility = View.VISIBLE
            }

            override fun onAnimationEnd(animation: Animator) {
                isBottomCardVisible = true
            }
        })
        animator.start()
    }

    /**
     * 隐藏底部卡片
     */
    private fun hideBottomCard() {
        val bottomCard = binding.layoutBottomCard
        val height = bottomCard.height.toFloat()

        // 如果高度为0，说明视图还没有测量完成，不执行动画
        if (height <= 0) return

        // 创建动画，向下滑出
        val animator = ObjectAnimator.ofFloat(bottomCard, "translationY", 0f, height)
        animator.duration = 200
        animator.interpolator = AccelerateInterpolator()
        animator.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                isBottomCardVisible = false
            }
        })
        animator.start()
    }

    private fun initCardInfo(info: BusinessCardInfo, cardBinding: LayoutBusinessCardContentBinding, isShare: Boolean) {
        // 更新名片信息UI
        cardBinding.tvName.text = FundTextUtils.showTextEmpty(info.consName)
        cardBinding.tvCompany.text = "${info.companyName}-${info.position}"
        cardBinding.tvCertNumber.text = "执证编号：${info.certificateNumber}"

        updateCardStyle(vmBusinessCardHome.currentCardStyle.value, cardBinding, isShare)

        // 显示或隐藏电话
        if (!info.consMobile.isNullOrEmpty()) {
            cardBinding.layoutPhone.visibility = View.VISIBLE
            cardBinding.tvPhone.text = info.consMobile
        } else {
            cardBinding.layoutPhone.visibility = View.GONE
        }

        // 显示或隐藏邮箱
        if (!info.email.isNullOrEmpty()) {
            cardBinding.layoutEmail.visibility = View.VISIBLE
            cardBinding.tvEmail.text = info.email
        } else {
            cardBinding.layoutEmail.visibility = View.GONE
        }

        // 显示或隐藏一句话介绍
        if (!info.oneIntroduction.isNullOrEmpty()) {
            cardBinding.tvIntro.visibility = View.VISIBLE
            val spIntro = SpannableString(" ${FundTextUtils.showTextEmpty(info.oneIntroduction)}")
            val drawable = ContextCompat.getDrawable(GlobalApp.getApp(), R.mipmap.ic_business_card_desc)
            drawable?.let {
                it.setBounds(0, 0, 52, 44)
                spIntro.setSpan(CenterImageSpan(it, 6f.dp2px()), 0, 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            }
            cardBinding.tvIntro.text = spIntro
        } else {
            cardBinding.tvIntro.visibility = View.GONE
        }

        //二维码
        if (!info.qrCode.isNullOrEmpty()) {
            cardBinding.layoutQrcode.visibility = View.VISIBLE
            cardBinding.tvQrcodeHint.visibility = View.VISIBLE
//            cardBinding.ivQrcode.setImageBitmap(SmBitmapUtils.stringToBitmap(info.qrCode))
            ImgHelper.display(info.qrCode, cardBinding.ivQrcode)

            // 二维码显示时，space的marginTop设置为40dp
            val params = cardBinding.space.layoutParams as ConstraintLayout.LayoutParams
            params.topMargin = if (isShare) 100f.dp2px() else 40f.dp2px()
            cardBinding.space.layoutParams = params
        } else {
            cardBinding.layoutQrcode.visibility = View.INVISIBLE
            cardBinding.tvQrcodeHint.visibility = View.INVISIBLE

            // 二维码隐藏时，space的marginTop设置为0
            val params = cardBinding.space.layoutParams as ConstraintLayout.LayoutParams
            params.topMargin = 0
            cardBinding.space.layoutParams = params
        }
    }

    private fun initTagList(tagList: MutableList<String>?, tagLayout: TagLayout) {
        if (tagList.isNullOrEmpty()) {
            tagLayout.visibility = View.GONE
        } else {
            tagLayout.visibility = View.VISIBLE
            tagLayout.tagAdapter = TagAdpBusinessCard(tagList)
            tagLayout.notifyDataSetChanged()
        }
    }

    /**
     * 更新头像
     */
    private fun updateAvatar(avatarUrl: String?) {
        val info = vmBusinessCardHome.businessCardInfo.value ?: return
        val isShare = false // 非分享模式

        if (TextUtils.isEmpty(avatarUrl)) {
            val posterAvatarRes = if (info.consSex == "0") {
                // 女性海报风头像
                R.mipmap.ic_business_card_avatar_girl
            } else {
                // 男性海报风头像（包括性别为空或其他值的情况）
                R.mipmap.ic_business_card_avatar_boy
            }
            layoutCardContentBinding.ivAvatar.setImageResource(posterAvatarRes)
            if (isShare) {
                layoutCardContentBinding.ivUploadAvatar.visibility = View.GONE
            } else {
                layoutCardContentBinding.ivUploadAvatar.visibility = View.VISIBLE
            }
        } else {
            ImgHelper.display(avatarUrl, layoutCardContentBinding.ivAvatar)
            layoutCardContentBinding.ivUploadAvatar.visibility = View.GONE
        }

        // 如果当前是海报风样式，需要更新海报风头像
        val currentStyle = vmBusinessCardHome.currentCardStyle.value
        if (currentStyle == CardStyle.POSTER) {
            if (TextUtils.isEmpty(avatarUrl)) {
                // 根据性别使用默认头像
                val posterAvatarRes = if (info.consSex == "0") {
                    // 女性海报风头像
                    R.mipmap.bg_business_card_girl
                } else {
                    // 男性海报风头像（包括性别为空或其他值的情况）
                    R.mipmap.bg_business_card_boy
                }
                layoutCardContentBinding.ivPosterAvatar.setImageResource(posterAvatarRes)
            } else {
                // 使用用户头像
                ImgHelper.display(avatarUrl, layoutCardContentBinding.ivPosterAvatar)
            }
        }
    }

    private fun updateCardStyle(style: CardStyle?, cardBinding: LayoutBusinessCardContentBinding, isShare: Boolean) {
        when (style) {
            CardStyle.RED -> {
                // 设置红色背景图
                cardBinding.ivPosterMask.visibility = View.GONE
                cardBinding.ivPosterAvatar.visibility = View.GONE
                cardBinding.ivCardBg.setBackgroundResource(R.mipmap.bg_card_red)

                // 根据头像是否为空决定是否显示头像
                val avatarUrl = vmBusinessCardHome.avatar.value
                cardBinding.layAvatar.visibility = if (avatarUrl.isNullOrEmpty() && isShare) View.GONE else View.VISIBLE
            }

            CardStyle.POSTER -> {
                // 海报风样式
                // 1. 设置卡片底色为#2B4080
                cardBinding.ivCardBg.setBackgroundResource(R.drawable.bg_2b4080_r7)

                // 2. 隐藏原来的头像，使姓名和公司信息向左移动
                cardBinding.layAvatar.visibility = View.GONE

                // 3. 显示半透明头像层
                cardBinding.ivPosterAvatar.visibility = View.VISIBLE

                // 4. 显示黑色向上透明遮罩
                cardBinding.ivPosterMask.visibility = View.VISIBLE

                // 5. 设置头像图片
                val avatarUrl = vmBusinessCardHome.avatar.value
                val info = vmBusinessCardHome.businessCardInfo.value
                if (avatarUrl.isNullOrEmpty()) {
                    // 根据性别使用默认头像
                    val posterAvatarRes = if (info?.consSex == "0") {
                        // 女性海报风头像
                        R.mipmap.bg_business_card_girl
                    } else {
                        // 男性海报风头像（包括性别为空或其他值的情况）
                        R.mipmap.bg_business_card_boy
                    }
                    cardBinding.ivPosterAvatar.setImageResource(posterAvatarRes)
                } else {
                    // 使用用户头像
                    ImgHelper.display(avatarUrl, cardBinding.ivPosterAvatar)
                }
            }

            else -> {
                // 设置蓝色背景图
                cardBinding.ivPosterMask.visibility = View.GONE
                cardBinding.ivPosterAvatar.visibility = View.GONE
                cardBinding.ivCardBg.setBackgroundResource(R.mipmap.bg_card_blue)
                // 根据头像是否为空决定是否显示头像
                val avatarUrl = vmBusinessCardHome.avatar.value
                cardBinding.layAvatar.visibility = if (avatarUrl.isNullOrEmpty() && isShare) View.GONE else View.VISIBLE
            }
        }
    }

    /**
     * 显示名片样式选择器
     */
    private fun showCardStyleSelector() {
        val currentStyle = vmBusinessCardHome.currentCardStyle.value ?: CardStyle.RED
        val dialog = CardStyleSelectorDlg.getInstance(currentStyle, object : CardStyleSelectorDlg.OnStyleSelectedListener {
            override fun onStyleSelected(position: Int) {
                val style = when (position) {
                    0 -> CardStyle.RED
                    1 -> CardStyle.BLUE
                    2 -> CardStyle.POSTER
                    else -> CardStyle.RED
                }
                vmBusinessCardHome.switchCardStyle(style)
            }
        })
        dialog.show(childFragmentManager, "cardStyleSelector")
    }

    /**
     * 显示分享选项
     */
    private fun showShareOptions() {
        // 检查是否所有模块都关闭
        val modules = vmBusinessCardHome.moduleList.value ?: return
        val allModulesClosed = modules.none { it.enabled }

        if (allModulesClosed) {
            // 所有模块都关闭，显示提示对话框
            val dialog = BusinessCardShareHintDlg()
            dialog.setOnDialogActionListener(object : BusinessCardShareHintDlg.OnDialogActionListener {
                override fun onContinueShare() {
                    // 继续分享操作
                    proceedWithSharing()
                }
            })
            dialog.show(childFragmentManager, "shareHintDialog")
            // 在对话框关闭后再执行模块开启操作
            vmBusinessCardHome.toggleModuleEnabled(ModuleType.PROMOTION, true, false)
        } else {
            // 至少有一个模块开启，直接分享
            proceedWithSharing()
        }
    }

    /**
     * 继续分享操作
     */
    private fun proceedWithSharing() {
        // 获取当前名片信息
        val info = vmBusinessCardHome.businessCardInfo.value ?: return

        // 分享前先调用分享上报接口
        val templateId = info.templateId ?: return
        vmBusinessCardHome.reportShare(templateId)

        val mShareTitle = "我是${info.companyName}${info.consName}，期待为您服务"
        val mShareContent = "${mShareTitle}：${info.h5Url}"
        val shareEntity = WorkWeixinEntity(
            mShareTitle,
            "关注好买全球，享全球优选配置",
            info.h5Url,
            info.pictureUrl
        )
        shareEntity.moreShareDesc = mShareContent
        val secondShareEntity = ShareMoreEntity(
            mShareTitle,
            mShareContent,
            info.h5Url,
            info.pictureUrl
        )
        secondShareEntity.addScreenShotChannel(true)
        secondShareEntity.screenShotTxt = "生成名片"
        secondShareEntity.screenShotImg = R.drawable.ic_screenshot_card
        Invoker.getInstance().navigation(IShareProvider::class.java)
            .showShareWithSwitchDialog(
                activity, false,
                shareEntity, secondShareEntity, object : IShareActionListener {

                    override fun onSuccess(platformType: Int, shareItem: ShareItem?) {
                        if (platformType == IShareHelper.SHARE_TYPE_SCREENSHOT) {
                            // 截取名片内容生成Bitmap
                            captureCardContentAndShowPreview()
                        }
                    }

                    override fun onError(platformType: Int) {
                    }

                    override fun onCancel(platformType: Int) {
                    }
                }, "持牌人名片"
            )
    }

    /**
     * 显示模块排序对话框
     */
    private fun showModuleSortDialog() {
        val modules = vmBusinessCardHome.moduleList.value ?: return
        val dialog = ModuleSortDlg.getInstance(modules, object : ModuleSortDlg.OnModuleSortListener {
            override fun onModuleSorted(modules: List<BusinessCardModule>) {
                // 更新模块排序
                vmBusinessCardHome.updateModuleOrder(modules)
            }
        })
        dialog.show(childFragmentManager, "moduleSortDialog")
    }

    /**
     * 截取名片内容并显示预览
     * 参考VideoScreenShotHelper.shotScreenOnlyForVideo方法，使用独立布局进行截图
     */
    private fun captureCardContentAndShowPreview() {
        activity?.let { activity ->
            // 创建一个独立的布局视图，专门用于截图
            val screenshotViewBinding = LayoutBusinessCardContentBinding.inflate(LayoutInflater.from(requireContext()))

            try {
                val constraintSet = ConstraintSet()
                constraintSet.clone(screenshotViewBinding.layContent)
                constraintSet.constrainPercentWidth(R.id.layout_qrcode, 0.56f)
                constraintSet.applyTo(screenshotViewBinding.layContent)

                screenshotViewBinding.layoutCardStyleSelector.visibility = View.INVISIBLE
                // 填充数据
                val cardInfo = vmBusinessCardHome.businessCardInfo.value
                if (cardInfo != null) {
                    ImgHelper.display(cardInfo.pictureUrl, screenshotViewBinding.ivAvatar)
                    initCardInfo(cardInfo, screenshotViewBinding, true)
                }

                // 设置标签
                initTagList(vmBusinessCardHome.tagList.value, screenshotViewBinding.layoutTags)

                // 使用ScreenShotUtils进行布局和截图
                ScreenShotUtils.layoutView(activity, screenshotViewBinding.root)
                val bitmap = ScreenShotUtils.shotByTagView(screenshotViewBinding.root, null)

                // 显示预览
                GlobalCreatePosterPreviewerUtils().initPopWind(activity.window.decorView, activity, bitmap)
            } catch (e: Exception) {
                e.printStackTrace()
                LogUtils.e(TAG, "截图失败: ${e.message}")
            }
        }
    }

    /**
     * 显示清空全球热点确认对话框
     */
    private fun showClearHotNewsConfirmDialog() {
        DlgHelper { _, which ->
            if (which == DlgHelper.IDlgHelper.DLG_POSITIVE) {
                vmBusinessCardHome.clearModuleContent(ModuleType.HOT_NEWS)
            }
        }.showDialog(
            context, DlgHelper.DlgArg("取消", "确定", "", "是否清空？")
                .setBackCancelAble(true).setTouchCancelAble(true), 1
        )
    }

    /**
     * 显示清空推荐产品确认对话框
     */
    private fun showClearProductsConfirmDialog() {
        DlgHelper { _, which ->
            if (which == DlgHelper.IDlgHelper.DLG_POSITIVE) {
                vmBusinessCardHome.clearModuleContent(ModuleType.PRODUCTS)
            }
        }.showDialog(
            context, DlgHelper.DlgArg("取消", "确定", "", "是否清空？")
                .setBackCancelAble(true).setTouchCancelAble(true), 1
        )
    }

    override fun createViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragBusinessCardHomeBinding {
        return FragBusinessCardHomeBinding.inflate(inflater, container, false)
    }

    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        super.onCreateOptionsMenu(menu, inflater)
        inflater.inflate(R.menu.menu_business_card_home, menu)
        menu.findItem(R.id.action_edit).actionView?.setOnClickListener {
            HbAnalytics.onClick("613660")
            RouterHelper.launchFragWithCallback(activity, PATH_FRAG_BUSINESS_CARD_EDIT, null) { resultCode, data ->
                vmBusinessCardHome.requestBusinessCardHome(true)
                true
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        // 移除Handler回调，避免内存泄漏
        handler.removeCallbacks(scrollIdleRunnable)
    }

}