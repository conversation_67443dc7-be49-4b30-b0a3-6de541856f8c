package howbuy.android.global.businesscard.model

import android.os.Parcelable
import com.howbuy.fund.net.entity.common.normal.AbsNormalBody
import howbuy.android.global.best.SmBestItem
import kotlinx.android.parcel.Parcelize

/**
 * 持牌人名片信息
 */
@Parcelize
data class BusinessCardInfo(
    val templateId: String? = null, // 名片模板ID
    val templateName: String? = null, // 模板名称
    val styleType: String? = null, // 名片风格(1:好买红-默认、2:商务蓝、3:海报风)
    var pictureUrl: String? = null, // 头像URL
    val consCode: String? = null, // 投顾号
    val consName: String? = null, // 姓名
    val consMobile: String? = null, // 电话
    val email: String? = null, // 邮箱
    val companyName: String? = null, // 公司
    val position: String? = null, // 职位 title
    val certificateNumber: String? = null, // 执证编号
    var oneIntroduction: String? = null, // 一句话介绍
    val qrCode: String? = null, // 企业微信二维码URL
    var sendNum: String? = null, // 发送次数
    val likeNum: String? = null, // 点赞次数
    val pvNum: String? = null, // 点击量
    val h5Url: String? = null, // 名片分享二维码
    var tgTagList: MutableList<String>? = null, // 个人标签
    val consSex: String? = null, // 投顾性别（1:男性;0:女性）
) : Parcelable, AbsNormalBody()

/**
 * 持牌人主页-内容模块
 */
@Parcelize
data class BusinessCardContent(
    val consCode: String? = null, // 投顾号
    val moduleTypeSort: MutableList<String>? = null, // 模块顺序(ex:[3,2,1])
    val pxModuleDTO: PosterModule? = null, // 品宣模块
    val hotModuleDTO: HotZxModule? = null, // 全球热点模块
    val recommendProductModuleDTO: ProductModule? = null // 推荐产品模块
) : Parcelable, AbsNormalBody()

/**
 * 持牌人主页-添加内容-返回的内容详情
 */
@Parcelize
data class BusinessCardContentAddResult(
    val moduleType: String? = null,// 模块类型(2:全球热点模块、3:推荐产品模块)
    val hotModuleDataDTOList: MutableList<HwZxItem>? = null, //全球热点数据-模块类型=2使用
    val recommendProductModuleDataDTOList: MutableList<SmBestItem>? = null,//推荐产品数据-模块类型=3使用
) : Parcelable, AbsNormalBody()

/**
 * 品宣模块
 */
@Parcelize
data class PosterModule(
    val moduleType: String? = null, // 模块类型(1:关于好买香港、2:全球热点模块、3:推荐产品模块)
    val moduleName: String? = null, // 模块名称
    val pxImgUrl: String? = null, // 品宣海报URL（仅品宣模块使用）
    var openFlag: String?, // 模块是否启用 1:开，0:关
    var order: Int, // 模块排序
) : Parcelable

/**
 * 热门资讯模块
 */
@Parcelize
data class HotZxModule(
    val moduleType: String? = null, // 模块类型(1:关于好买香港、2:全球热点模块、3:推荐产品模块)
    val moduleName: String? = null, // 模块名称
    var openFlag: String?, // 模块是否启用 0: 开 1:关   1:开，0:关
    var hotModuleDataDTOList: MutableList<HwZxItem>? = null,
    var order: Int, // 模块排序
) : Parcelable

/**
 * 热门产品模块
 */
@Parcelize
data class ProductModule(
    val moduleType: String? = null, // 模块类型(1:关于好买香港、2:全球热点模块、3:推荐产品模块)
    val moduleName: String? = null, // 模块名称
    var openFlag: String?, // 模块是否启用 0: 开 1:关
    var order: Int, // 模块排序
    var recommendProductModuleDataDTOList: MutableList<SmBestItem>? = null
) : Parcelable