package howbuy.android.global.businesscard

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.viewModelScope
import com.howbuy.fund.base.arch.ClearViewModel
import com.howbuy.fund.net.entity.http.ReqNetOpt
import com.howbuy.fund.net.entity.http.ReqResult
import com.howbuy.fund.net.util.HandleErrorMgr
import com.howbuy.lib.utils.LogUtils
import howbuy.android.global.best.SmBestItem
import howbuy.android.global.businesscard.model.BusinessCardContent
import howbuy.android.global.businesscard.model.BusinessCardInfo
import howbuy.android.global.businesscard.model.BusinessCardModule
import howbuy.android.global.businesscard.model.CardStyle
import howbuy.android.global.businesscard.model.HwZxItem
import howbuy.android.global.businesscard.model.ModuleType
import howbuy.android.global.request.GlobalRequest
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 持牌人名片主页ViewModel
 */
class VmBusinessCardHome : ClearViewModel() {

    private val TAG = "VmBusinessCardHome"

    // loading
    private val _loading = MutableLiveData<Boolean>()
    val loading: LiveData<Boolean> = _loading
    private lateinit var hotNewsViewModel: VmBusinessCardHotNews
    private lateinit var productsViewModel: VmBusinessCardProducts

    // 热点数据观察者
    private val hotNewsObserver = Observer<List<HwZxItem>> { hotNewsList ->
        // 当热点数据变化时，更新模块列表
        updateModuleContent(ModuleType.HOT_NEWS, hotNewsList)
    }

    // 产品数据观察者
    private val productsObserver = Observer<List<SmBestItem>> { productList ->
        // 当产品数据变化时，更新模块列表
        updateModuleContent(ModuleType.PRODUCTS, productList)
    }

    /**
     * 通用的网络请求处理方法
     * @param request 请求Observable
     * @param showLoading 是否显示加载中状态
     * @param onSuccess 成功回调
     * @param onError 错误回调
     */
    private fun <T> makeRequest(
        request: io.reactivex.Observable<ReqResult<ReqNetOpt>>,
        showLoading: Boolean = true,
        onSuccess: (T) -> Unit,
        onError: (String?) -> Unit = { LogUtils.pop(it) }
    ) {
        if (showLoading) {
            _loading.postValue(true)
        }

        val disposable = request
            .doOnError { e ->
                if (showLoading) {
                    _loading.postValue(false)
                }
                LogUtils.e(TAG, "请求异常: ${e.message}")
                e.printStackTrace()
                onError("网络异常，请稍后再试")
            }
            .subscribe { t ->
                if (showLoading) {
                    _loading.postValue(false)
                }
                if (t.isSuccess && t.mData != null) {
                    try {
                        onSuccess(t.mData as T)
                    } catch (e: ClassCastException) {
                        LogUtils.e(TAG, "类型转换失败: ${e.message}")
                        val errorMsg = "数据类型不匹配"
                        onError(errorMsg)
                    }
                } else {
                    val errorMsg = HandleErrorMgr.handErrorMsg(t.mErr, true)
                    LogUtils.e(TAG, "请求失败: $errorMsg")
                    onError(errorMsg)
                }
            }
        accept(disposable)
    }

    /**
     * 初始化ViewModel
     */
    fun initViewModels(newsVM: VmBusinessCardHotNews, productVM: VmBusinessCardProducts) {
        hotNewsViewModel = newsVM
        productsViewModel = productVM
        // 监听热点数据变化
        hotNewsViewModel.hotNewsList.observeForever(hotNewsObserver)

        // 监听产品数据变化
        productsViewModel.productList.observeForever(productsObserver)
    }

    override fun onCleared() {
        super.onCleared()
        // 移除观察者，避免内存泄漏
        hotNewsViewModel.hotNewsList.removeObserver(hotNewsObserver)
        productsViewModel.productList.removeObserver(productsObserver)
    }

    // 持牌人名片信息
    private val _businessCardInfo = MutableLiveData<BusinessCardInfo>()
    val businessCardInfo: LiveData<BusinessCardInfo> = _businessCardInfo

    // 分享次数 - 单独处理，避免整个BusinessCardInfo对象更新
    private val _shareCount = MutableLiveData<String>()
    val shareCount: LiveData<String> = _shareCount

    //头像
    private val _avatar = MutableLiveData<String>()
    val avatar: LiveData<String> = _avatar

    //标签
    private val _tagList = MutableLiveData<MutableList<String>>()
    val tagList: MutableLiveData<MutableList<String>> = _tagList

    // 当前选择的名片样式
    private val _currentCardStyle = MutableLiveData<CardStyle>()
    val currentCardStyle: LiveData<CardStyle> = _currentCardStyle

    // 模块列表
    private val _moduleList = MutableLiveData<List<BusinessCardModule>>()
    val moduleList: LiveData<List<BusinessCardModule>> = _moduleList

    // 单项模块更新事件 - Pair<索引, 更新后的模块>
    private val _moduleItemUpdateEvent = MutableLiveData<Pair<Int, BusinessCardModule>>()
    val moduleItemUpdateEvent: LiveData<Pair<Int, BusinessCardModule>> = _moduleItemUpdateEvent

    /**
     * UI状态密封类
     */
    sealed class UiState {
        object Loading : UiState()
        data class Success(val data: Any? = null) : UiState()
        object Empty : UiState()
    }

    // 加载状态
    private val _loadingState = MutableLiveData<UiState>()
    val loadingState: LiveData<UiState> = _loadingState

    // 接口请求状态跟踪
    private var homeInfoSuccess = false
    private var contentInfoSuccess = false

    /**
     * 初始化数据
     */
    fun initData() {
        LogUtils.d(TAG, "initData")
        // 重置状态
        resetLoadingState()
        // 设置加载状态为加载中
        _loadingState.value = UiState.Loading

        // 请求持牌人名片基本信息
        requestBusinessCardHome()

        // 请求持牌人名片内容信息
        requestBusinessCardContent()
    }

    /**
     * 重置加载状态
     */
    private fun resetLoadingState() {
        homeInfoSuccess = false
        contentInfoSuccess = false
    }

    /**
     * 请求持牌人名片基本信息
     */
    fun requestBusinessCardHome(showLoading: Boolean = false) {
        makeRequest<BusinessCardInfo>(
            request = GlobalRequest.reqBusinessCardHome(),
            showLoading = showLoading,
            onSuccess = { data ->
                // 数据有效
                _businessCardInfo.value = data
                _avatar.value = data.pictureUrl
                _tagList.value = data.tgTagList
                hotNewsViewModel.initTemplateId(data.templateId)
                productsViewModel.initTemplateId(data.templateId)
                // 设置名片样式
                when (data.styleType) {
                    "1" -> _currentCardStyle.value = CardStyle.RED
                    "2" -> _currentCardStyle.value = CardStyle.BLUE
                    "3" -> _currentCardStyle.value = CardStyle.POSTER
                    else -> _currentCardStyle.value = CardStyle.RED
                }

                // 标记基本信息加载完成
                homeInfoSuccess = true
                checkLoadingComplete()
            },
            onError = { errorMsg ->
                // 接口返回错误
                homeInfoSuccess = false
                _loadingState.value = UiState.Empty
            }
        )
    }

    /**
     * 请求持牌人名片内容信息
     */
    private fun requestBusinessCardContent() {
        makeRequest<BusinessCardContent>(
            request = GlobalRequest.reqBusinessCardContent(),
            showLoading = false,
            onSuccess = { data ->
                initModuleList(data)
                // 标记内容信息加载完成
                contentInfoSuccess = true
                checkLoadingComplete()
            },
            onError = { errorMsg ->
                // 接口返回错误
                contentInfoSuccess = false
                _loadingState.value = UiState.Empty
            }
        )
    }

    /**
     * 检查所有数据是否加载完成
     */
    private fun checkLoadingComplete() {
        if (homeInfoSuccess && contentInfoSuccess) {
            _loadingState.value = UiState.Success()
        }
    }

    /**
     * 初始化模块列表
     */
    private fun initModuleList(data: BusinessCardContent) {
        // 创建品宣模块
        val promotionModule = BusinessCardModule(
            type = ModuleType.PROMOTION,
            title = data.pxModuleDTO?.moduleName ?: "关于好买香港",
            enabled = data.pxModuleDTO?.openFlag == "1", // 注意：1表示开，0表示关
            order = data.pxModuleDTO?.order ?: 0,
            promotionPosterUrl = data.pxModuleDTO?.pxImgUrl ?: ""
        )

        // 处理热点数据
        val hotNewList = data.hotModuleDTO?.hotModuleDataDTOList ?: mutableListOf()

        // 创建全球热点模块
        val hotNewsModule = BusinessCardModule(
            type = ModuleType.HOT_NEWS,
            title = data.hotModuleDTO?.moduleName ?: "全球热点",
            enabled = data.hotModuleDTO?.openFlag == "1", // 注意：1表示开，0表示关
            order = data.hotModuleDTO?.order ?: 1,
            hotNewsList = hotNewList
        )

        // 初始化热点ViewModel
        hotNewsViewModel.initHotNewsList(hotNewList)

        // 处理产品数据
        val productList = data.recommendProductModuleDTO?.recommendProductModuleDataDTOList ?: mutableListOf()

        // 创建推荐产品模块
        val productsModule = BusinessCardModule(
            type = ModuleType.PRODUCTS,
            title = data.recommendProductModuleDTO?.moduleName ?: "推荐产品",
            enabled = data.recommendProductModuleDTO?.openFlag == "1", // 注意：1表示开，0表示关
            order = data.recommendProductModuleDTO?.order ?: 2,
            productList = productList
        )

        // 初始化产品ViewModel
        productsViewModel.initProductList(productList)

        // 创建模块列表
        val moduleMap = mapOf(
            ModuleType.PROMOTION to promotionModule,
            ModuleType.HOT_NEWS to hotNewsModule,
            ModuleType.PRODUCTS to productsModule
        )

        // 创建最终模块列表
        val moduleList = mutableListOf<BusinessCardModule>()

        // 如果有moduleTypeSort，则按照它来排序
        if (!data.moduleTypeSort.isNullOrEmpty()) {
            // 根据 moduleTypeSort 排序
            data.moduleTypeSort.forEach { typeStr ->
                when (typeStr) {
                    "1" -> moduleMap[ModuleType.PROMOTION]?.let { moduleList.add(it) }
                    "2" -> moduleMap[ModuleType.HOT_NEWS]?.let { moduleList.add(it) }
                    "3" -> moduleMap[ModuleType.PRODUCTS]?.let { moduleList.add(it) }
                }
            }
        } else {
            // 如果没有moduleTypeSort，则按照order排序
            moduleList.addAll(listOf(promotionModule, hotNewsModule, productsModule))
            moduleList.sortBy { it.order }
        }

        // 设置排序后的模块列表
        _moduleList.value = moduleList
    }

    fun updateAvatar(url: String) {
        _avatar.value = url
    }

    /**
     * 切换名片样式
     */
    fun switchCardStyle(style: CardStyle) {
        val info = _businessCardInfo.value ?: return
        val templateId = info.templateId ?: return

        val styleType = when (style) {
            CardStyle.RED -> "1"
            CardStyle.BLUE -> "2"
            CardStyle.POSTER -> "3"
        }

        makeRequest<Any>(
            request = GlobalRequest.reqBusinessCardStyle(templateId, styleType),
            showLoading = true,
            onSuccess = { _ ->
                LogUtils.d(TAG, "切换名片样式成功")
                // 接口调用成功后更新本地数据
                _currentCardStyle.postValue(style)
            },
            onError = { errorMsg ->
                LogUtils.e(TAG, "切换名片样式失败: $errorMsg")
            }
        )
    }

    /**
     * 切换模块开关状态
     */
    fun toggleModuleEnabled(moduleType: ModuleType, enabled: Boolean, showLoading: Boolean = true) {
        val info = _businessCardInfo.value ?: return
        val templateId = info.templateId ?: return
        val moduleTypeStr = when (moduleType) {
            ModuleType.PROMOTION -> "1"
            ModuleType.HOT_NEWS -> "2"
            ModuleType.PRODUCTS -> "3"
        }

        makeRequest<Any>(
            request = GlobalRequest.reqBusinessCardModuleSwitch(templateId, moduleTypeStr, enabled),
            showLoading = showLoading,
            onSuccess = { _ ->
                LogUtils.d(TAG, "切换模块开关状态成功")
                // 接口调用成功后更新本地数据
                updateLocalModuleEnabled(moduleType, enabled)
            },
            onError = { errorMsg ->
                LogUtils.e(TAG, "切换模块开关状态失败: $errorMsg")
            }
        )
    }

    /**
     * 更新本地模块开关状态
     */
    private fun updateLocalModuleEnabled(moduleType: ModuleType, enabled: Boolean) {
        val currentModules = _moduleList.value?.toMutableList() ?: return
        val moduleIndex = currentModules.indexOfFirst { it.type == moduleType }
        if (moduleIndex != -1) {
            currentModules[moduleIndex].enabled = enabled
            // 使用协程延迟执行数据更新，避免RecyclerView在布局过程中被修改
            viewModelScope.launch {
                delay(16) // 延迟一帧时间
                // 只触发单项更新事件，不触发全列表更新
                _moduleItemUpdateEvent.value = Pair(moduleIndex, currentModules[moduleIndex])
            }
        }
    }

    /**
     * 更新模块排序（从排序对话框返回）
     */
    fun updateModuleOrder(modules: List<BusinessCardModule>) {
        val info = _businessCardInfo.value ?: return
        val templateId = info.templateId ?: return

        // 根据模块类型创建排序数组
        val moduleTypeList = modules.map { module ->
            when (module.type) {
                ModuleType.PROMOTION -> "1"
                ModuleType.HOT_NEWS -> "2"
                ModuleType.PRODUCTS -> "3"
            }
        }.toTypedArray()

        makeRequest<Any>(
            request = GlobalRequest.reqBusinessCardModuleSort(templateId, moduleTypeList),
            showLoading = true,
            onSuccess = { _ ->
                LogUtils.d(TAG, "更新模块排序成功")
                // 接口调用成功后更新本地数据
                updateLocalModuleOrder(modules)
            },
            onError = { errorMsg ->
                LogUtils.e(TAG, "更新模块排序失败: $errorMsg")
            }
        )
    }

    /**
     * 更新本地模块排序
     * 注意：排序操作需要更新整个列表，因为排序涉及到多个项目的位置变化
     */
    private fun updateLocalModuleOrder(modules: List<BusinessCardModule>) {
        val currentModules = modules.toMutableList()
        // 更新排序属性
        currentModules.forEachIndexed { index, module ->
            currentModules[index] = module.copy(order = index)
        }
        // 排序操作需要更新整个列表
        _moduleList.value = currentModules
    }

    /**
     * 清空模块内容
     */
    fun clearModuleContent(moduleType: ModuleType) {
        // 只有全球热点和推荐产品模块可以清空
        if (moduleType == ModuleType.PROMOTION) {
            return
        }

        when (moduleType) {
            ModuleType.HOT_NEWS -> {
                hotNewsViewModel.clearHotNewsList()
            }

            ModuleType.PRODUCTS -> {
                productsViewModel.clearProductList()
            }

            else -> return
        }
    }

    /**
     * 一键添加模块内容
     */
    fun quickAddModuleContent(moduleType: ModuleType) {
        // 只有全球热点和推荐产品模块可以一键添加
        if (moduleType == ModuleType.PROMOTION) {
            return
        }

        when (moduleType) {
            ModuleType.HOT_NEWS -> {
                hotNewsViewModel.quickAddModuleContent()
            }

            ModuleType.PRODUCTS -> {
                productsViewModel.quickAddModuleContent()
            }

            else -> return
        }
    }

    /**
     * 更新模块内容
     */
    fun <T> updateModuleContent(moduleType: ModuleType, content: List<T>) {
        val currentModules = _moduleList.value?.toMutableList() ?: return
        val moduleIndex = currentModules.indexOfFirst { it.type == moduleType }
        if (moduleIndex != -1) {
            when (moduleType) {
                ModuleType.HOT_NEWS -> {
                    val typedList = content as List<HwZxItem>
                    currentModules[moduleIndex].hotNewsList = typedList.toMutableList()
                }

                ModuleType.PRODUCTS -> {
                    val typedList = content as List<SmBestItem>
                    currentModules[moduleIndex].productList = typedList.toMutableList()
                }

                else -> return
            }

            // 触发单项更新事件
            viewModelScope.launch {
                delay(16) // 延迟一帧时间
                // 只触发单项更新事件，不触发全列表更新
                _moduleItemUpdateEvent.value = Pair(moduleIndex, currentModules[moduleIndex])
            }
        }
    }

    /**
     * 分享上报接口
     * @param templateId 模板ID
     */
    fun reportShare(templateId: String) {
        makeRequest<Any>(
            request = GlobalRequest.reqShareReport(templateId),
            showLoading = false,
            onSuccess = { _ ->
                LogUtils.d(TAG, "分享上报成功")
                // 接口调用成功后更新本地数据
                updateLocalSendNum()
            },
            onError = { errorMsg ->
                LogUtils.e(TAG, "分享上报失败: $errorMsg")
                // 分享上报失败不需要向用户提示
            }
        )
    }

    /**
     * 更新本地发送名片次数
     */
    private fun updateLocalSendNum() {
        val info = _businessCardInfo.value ?: return

        // 本地发送名片次数 sendNum +1
        val currentSendNum = info.sendNum?.toIntOrNull() ?: 0
        val newSendNum = (currentSendNum + 1).toString()
        info.sendNum = newSendNum

        // 只更新分享次数LiveData，触发UI中分享次数的局部更新
        _shareCount.postValue(newSendNum)
    }

}
