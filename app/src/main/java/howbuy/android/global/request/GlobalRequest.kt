package howbuy.android.global.request

import com.google.gson.reflect.TypeToken
import com.howbuy.fund.base.entity.WeChatLoginResult
import com.howbuy.fund.base.mvp.rx.RxHttpObservable
import com.howbuy.fund.net.HttpCaller
import com.howbuy.fund.net.cache.CacheMode
import com.howbuy.fund.net.entity.common.SimpleDto
import com.howbuy.fund.net.entity.common.normal.CommonDto
import com.howbuy.fund.net.entity.http.ReqNetOpt
import com.howbuy.fund.net.entity.http.ReqResult
import com.howbuy.fund.net.http.ReqParams
import com.howbuy.fund.net.http.RequestContentType
import com.howbuy.fund.net.interfaces.IReqNetFinished
import com.howbuy.fund.net.util.XUtils
import com.howbuy.global.data_api.apiUserInfo
import com.howbuy.global.data_api.getHkCustNo
import com.howbuy.global.data_api.isLogined
import howbuy.android.global.UrlKey
import howbuy.android.global.archive.entity.BjjzData
import howbuy.android.global.archive.entity.FundChartHintShowBody
import howbuy.android.global.archive.entity.FundDetailesInfo
import howbuy.android.global.archive.entity.FundDetailsBuyStatus
import howbuy.android.global.archive.entity.FundDetailsTradeTimeLineBody
import howbuy.android.global.archive.entity.FundNetValueBody
import howbuy.android.global.archive.entity.QuickPkChartBody
import howbuy.android.global.archive.entity.QuickPkDthcBody
import howbuy.android.global.archive.entity.QuickPkRecommend
import howbuy.android.global.archive.entity.SmArchiveDetailsBody
import howbuy.android.global.archive.entity.SmDetailHcData
import howbuy.android.global.archive.entity.SmDetailYjData
import howbuy.android.global.archive.entity.SmNormalPerformanceBody
import howbuy.android.global.archive.entity.SmRiskIndicatorBody
import howbuy.android.global.archive.entity.SmTradeCalendarBody
import howbuy.android.global.best.GmBestBody
import howbuy.android.global.best.SmBestEntity
import howbuy.android.global.businesscard.model.BusinessCardContent
import howbuy.android.global.businesscard.model.BusinessCardContentAddResult
import howbuy.android.global.businesscard.model.BusinessCardInfo
import com.howbuy.global.login_impl.entity.BindOtherAccountBody
import howbuy.android.global.entity.BlacklistIPInfoBody
import howbuy.android.global.entity.ConfigParamsBody
import howbuy.android.global.entity.GlobalDialogData
import howbuy.android.global.entity.GrayFunctionBean
import howbuy.android.global.entity.HeadlinesQrcodeBody
import howbuy.android.global.entity.HomePageBody
import howbuy.android.global.entity.HotRearchData
import howbuy.android.global.entity.MoreUserfavoriteModel
import howbuy.android.global.entity.SmFavorFundCodeBody
import howbuy.android.global.entity.SmListChartBody
import howbuy.android.global.entity.SmOptResponseBody
import howbuy.android.global.entity.SmOptionalGroupTitleBody
import howbuy.android.global.entity.SmOptionalHoldSortBody
import howbuy.android.global.entity.SmOptionalRecommendData
import howbuy.android.global.headlines.entity.ColumnData
import howbuy.android.global.headlines.entity.ColumnList
import howbuy.android.global.headlines.entity.HeadlinesData
import howbuy.android.global.headlines.entity.HeadlinesSearchResult
import howbuy.android.global.headlines.entity.HwTtsContentBody
import howbuy.android.global.headlines.entity.HwVideoBody
import howbuy.android.global.headlines.vhall.video.vm.CommentBody
import howbuy.android.global.headlines.vhall.video.vm.SmLivePosterBody
import howbuy.android.global.headlines.vhall.video.vm.SmVhallDetail
import howbuy.android.global.headlines.vhall.video.vm.SmVhallHeraldSubmitBody
import howbuy.android.global.headlines.vhall.video.vm.SmVhallPreviewData
import howbuy.android.global.headlines.vhall.video.vm.SmVhallVideoStatusBody
import howbuy.android.global.headlines.vhall.video.vm.SmVhallVideoValidPwdBody
import howbuy.android.global.search.SearchResultInfo
import howbuy.android.global.splashadds.AdvertList
import io.reactivex.Observable
import java.lang.reflect.Type

/**
 * @Description 接口请求
 * <AUTHOR>
 * @Date 2024/03/20
 * @Version V2.0
 */
object GlobalRequest {


    /**
     * 启动广告页
     */
    fun reqSplashAd(imgWidth: String, imgHeight: String, moduleKey: String, handType: Int, callback: IReqNetFinished) {
        val map = HashMap<String, Any?>()
        map["imageWidth"] = imgWidth
        map["imageHeight"] = imgHeight
        map["key"] = moduleKey
        map["hkCustNo"] = getHkCustNo()
        val reqParams = createNormalReqParams(
            UrlKey.HK_V260_ADBANNERLIST,
//            "https://apifoxmock.com/m1/2829913-1212853-default/hk/v260/adbannerlist.json",
            AdvertList::class.java,
            true,
            CacheMode.CACHEE_THEN_NETWORK,
            false,
            handType,
            callback,
            map
        )
        HttpCaller.getInstance().request(reqParams, callback)
    }

    /**
     * cms配置接口数据
     */
    fun reqCmsConfigData(handType: Int, callback: IReqNetFinished?) {
        val reqParams = createNormalReqParams(
            UrlKey.MAIN_V754_CONFIGPARAMS,
            ConfigParamsBody::class.java,
            false,
            CacheMode.CACHEE_THEN_NETWORK,
            false,
            handType,
            callback,
//            hashMapOf(Pair<String, Any?>("paramKey", "hk-mobile"))
            hashMapOf()
        )
        HttpCaller.getInstance().request(reqParams, callback)
    }

    /**
     * 首页接口请求(先缓存,再网络)
     */
    fun reqHomeData(handType: Int, callback: IReqNetFinished?) {
        val params = if (isLogined()) {
            hashMapOf(Pair<String, Any?>("hkCustNo", getHkCustNo()))
        } else {
            null
        }
        val reqParams = createNormalReqParams(
            UrlKey.HK_V200_HKHOMEPAGE,
            HomePageBody::class.java,
            true,
            CacheMode.CACHEE_THEN_NETWORK,
            false,
            handType,
            callback,
            params
        )
        HttpCaller.getInstance().request(reqParams, callback)
    }

    /**
     * 比较基准
     * @param fundCode  基金代码
     */
    fun reqBjjzList(fundCode: String): Observable<ReqResult<ReqNetOpt?>> {
        val map = HashMap<String, Any?>()
        map["fundCode"] = fundCode
        val params = createNormalReqParams(
            UrlKey.HK_V200_GETHKBJJZLIST, BjjzData::class.java, true, null, false, 0, { }, map
        )
        return RxRequestObservable.buildObservable(params)
    }

    /**
     * 私募档案页走势图,选择完日期后判断是否展示提示
     * @apiParam {String} fundCode 基金代码(多个,分隔)
     * @apiParam {String} startTime 起始日期(yyyyMMdd)
     * @apiParam {String} [endTime]   结束日期(yyyyMMdd)
     * @apiParam {String} [type=1] 判断类型(1:档案页走势图-默认,2:组合走势图)
     */
    fun reqSmChartSelectHintShow(
        fundCode: String,
        startTime: String?,
        endTime: String?,
        handType: Int,
        callback: IReqNetFinished?
    ) {
        val map = HashMap<String, Any?>()
        map["fundCode"] = fundCode
        map["startTime"] = startTime
        map["endTime"] = endTime
        map["type"] = "1"
        val url = UrlKey.HK_V250_CHARTSHOWFLAG
        val params = createNormalReqParams(
            url, FundChartHintShowBody::class.java, true, null, false, handType, callback, map
        )
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 业绩走势图
     * @param fundCode  基金代码
     * @param zsdm      市场指数代码(ZBXZ:代表暂不选择)
     * @param qjType    区间类型，默认从档案页接口取值(hbjn:回报今年 hb1y:回报一月 hb3y:回报三月 hb6y:回报6月 hb1n:回报1年 hb2n:回报两年 hb3n:回报3年 hb5n:回报5年 hbcl:回报成立)
     * @param queryType 查询类型(0:全部-走势图+走势图区间 1:走势图（手动切换时使用）)
     * @param hkCustNo  香港客户号
     * @param year      年份(2022) --2.5.0 add
     * @param startTime 开始时间(yyyyMMdd) --2.5.0 add
     * @param endTime   结束时间(yyyyMMdd) --2.5.0 add
     */
    fun reqHbChart(
        fundCode: String,
        zsdm: String?,
        qjType: String?,
        queryType: String, startTime: String?, endTime: String?, year: String?
    ): Observable<ReqResult<ReqNetOpt?>> {
        val map = HashMap<String, Any?>()
        map["fundCode"] = fundCode
        map["zsdm"] = zsdm
        map["qjType"] = qjType
        map["startTime"] = startTime
        map["endTime"] = endTime
        map["year"] = year
        map["queryType"] = queryType
        map["hkCustNo"] = apiUserInfo().getHkCustNo()
        val url = UrlKey.HK_V200_HKHBCHART
        val params = createNormalReqParams(
            url, SmDetailYjData::class.java, true, null, false, 0, { }, map
        )
        return RxRequestObservable.buildObservable(params)
    }

    /**
     * 历史回撤图
     * @param fundCode  基金代码
     * @param zsdm      市场指数代码(ZBXZ:代表暂不选择)
     * @param qjType    区间类型，默认从档案页接口取值(hbjn:回报今年 hb1y:回报一月 hb3y:回报三月 hb6y:回报6月 hb1n:回报1年 hb2n:回报两年 hb3n:回报3年 hb5n:回报5年 hbcl:回报成立)
     * @param queryType 查询类型(0:全部-走势图+走势图区间 1:走势图（手动切换时使用）)
     * @param hkCustNo  香港客户号
     * @param year      年份(2022) --2.5.0 add
     * @param startTime 开始时间(yyyyMMdd) --2.5.0 add
     * @param endTime   结束时间(yyyyMMdd) --2.5.0 add
     */
    fun reqHcChart(
        fundCode: String,
        zsdm: String?,
        qjType: String?,
        queryType: String, startTime: String?, endTime: String?, year: String?
    ): Observable<ReqResult<ReqNetOpt?>> {
        val map = HashMap<String, Any?>()
        map["fundCode"] = fundCode
        map["zsdm"] = zsdm
        map["qjType"] = qjType
        map["startTime"] = startTime
        map["endTime"] = endTime
        map["year"] = year
        map["queryType"] = queryType
        map["hkCustNo"] = apiUserInfo().getHkCustNo()
        val url = UrlKey.HK_V250_HKHCCHART
        val params = createNormalReqParams(
            url, SmDetailHcData::class.java, true, null, false, 0, { }, map
        )
        return RxRequestObservable.buildObservable(params)
    }

    /**
     * 档案页-基本信息/交易须知/产品材料
     * @param fundCode  基金代码
     * @param queryType  查询类型 1:基本信息、交易须知、产品材料，2：仅交易须知 [默认:为空查全部]
     */
    fun reqFundInfo(fundCode: String, queryType: String, handType: Int, callback: IReqNetFinished?) {
        val map = HashMap<String, Any?>()
        map["fundCode"] = fundCode
        map["queryType"] = queryType
        val params = createNormalReqParams(
            UrlKey.HK_V200_FUNDINFO,
            FundDetailesInfo::class.java, true, null, false, handType, callback, map
        )
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 海外产品购-交易日历-时间轴(只有预约产品才有时间轴模块)
     */
    fun reqFundTradeTimeLineInfo(fundCode: String?, handType: Int, callback: IReqNetFinished) {
        val map = HashMap<String, Any?>()
        map["fundCode"] = fundCode
        val params = createNormalReqParams(
            UrlKey.HK_V250_QUERYPRODUCTOPENCYCLE,
            FundDetailsTradeTimeLineBody::class.java, true, null, false, handType, callback, map
        )
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 海外搜索
     * @param type 0:首页综合搜索 1:搜索单页,
     * @param q 关键词
     * @param fq 搜索内容,type = 0的时候可以不传
     */
    fun reqSearchData(key: String, type: Int = 0, page: Int = 1, fq: String = ""): Observable<ReqResult<ReqNetOpt?>> {
        val map = HashMap<String, Any?>()
        map["q"] = key
        map["type"] = type.toString()
        map["page"] = page.toString()
        map["perPage"] = "20"
        val params = createNormalReqParams(
            UrlKey.HK_SEARCHDATA_NEW, SearchResultInfo::class.java, true, null, false, 0, { }, map
        )
        return RxRequestObservable.buildObservable(params)
    }

    /**
     * 海外搜索
     * @param type 0:首页综合搜索 1:搜索单页,
     * @param q 关键词
     * @param fq 搜索内容,type = 0的时候可以不传
     */
    fun reqSearchData(key: String, type: Int = 0, page: Int = 1, fq: String = "", handType: Int, callback: IReqNetFinished?) {
        val map = HashMap<String, Any?>()
        map["q"] = key
        map["type"] = type.toString()
        map["page"] = page.toString()
        map["perPage"] = "20"
        val params = createNormalReqParams(
            UrlKey.HK_SEARCHDATA_NEW, SearchResultInfo::class.java, true, null, false, handType, callback, map
        )
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 基金历史净值列表数据
     */
    fun reqHistoryNetWorthList(
        fundCode: String?,
        pageNum: String?,
        pageCount: String,
        handType: Int,
        callback: IReqNetFinished?
    ) {
        val map = HashMap<String, Any?>()
        map["fundCode"] = fundCode
        map["pageNum"] = pageNum
        map["pageCount"] = pageCount
        val params = createNormalReqParams(
            UrlKey.HK_V200_GETHKLSJZLIST,
            FundNetValueBody::class.java,
            true, null, false, handType, callback, map
        )
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 档案页主接口
     * @param sfhwkj 是否海外可见，0否1是(app请求入参)[首页配置的 不可见产品, 进入档案页面也要能看]
     */
    fun reqSmDetailsMainData(fundCode: String?, sfhwkj: String?): Observable<ReqResult<ReqNetOpt?>> {
        val map = HashMap<String, Any?>()
        map["fundCode"] = fundCode
        map["sfhwkj"] = sfhwkj
        val params = createNormalReqParams(
            UrlKey.HK_V200_ARCHIVEPAGEINFO,
            SmArchiveDetailsBody::class.java, true, null, false, 0, { }, map
        )
        return RxRequestObservable.buildObservable(params)
    }

    /**
     * 海外产品购买状态+平替产品+提示栏接口
     * /hk/v200/gethkbuystatus.json
     */
    fun reqFundDetailsBuyStatus(fundCode: String?): Observable<ReqResult<ReqNetOpt?>> {
        val map = HashMap<String, Any?>()
        map["fundCode"] = fundCode
        map["hkCustNo"] = getHkCustNo()
        val params = createNormalReqParams(
            UrlKey.HK_V200_GETHKBUYSTATUS,
            FundDetailsBuyStatus::class.java, true, null, false, 0, { }, map
        )
        return RxRequestObservable.buildObservable(params)
    }

    /**
     * 海外产品历史业绩列表（增加默认比较基准逻辑）
     * @param fundCode  基金代码
     * @param bjjzdm 比较基准代码(ZBXZ:代表暂不选择)
     */
    fun reqSmHistoryPerformanceData(
        fundCode: String?,
        bjjzdm: String?,
    ): Observable<ReqResult<ReqNetOpt?>> {
        val map = HashMap<String, Any?>()
        map["fundCode"] = fundCode
        map["bjjzdm"] = bjjzdm
        val params = createNormalReqParams(
            UrlKey.HK_V200_GETHKLSYJLIST, SmNormalPerformanceBody::class.java, true, null, false, 0, { }, map
        )
        return RxRequestObservable.buildObservable(params)
    }

    /**
     * 海外私募(原海外优选)
     * @param isSm 是否私募
     * @param needCategory 是否需要tab信息 0:否,1:是
     * @param firstCode 一级code
     * @param secondCode 二级code
     * @param currPage 当前页（从1开始，默认值为1）
     * @param pageSize 每页大小（默认每页20条）
     */
    fun reqSmBestList(
        isSm: Boolean,
        needCategory: String,
        firstCode: String?,
        secondCode: String?,
        currPage: String,
        pageSize: String,
        handType: Int,
        callback: IReqNetFinished?,
    ) {
        val map = HashMap<String, Any?>()
        map["needCategory"] = needCategory
        map["firstCode"] = firstCode
        if (isSm) {
            map["secondCode"] = secondCode
        }
        map["currPage"] = currPage
        map["pageSize"] = pageSize
        val params = createNormalReqParams(
            if (isSm) UrlKey.HK_V200_HOTSALES else UrlKey.HK_V254_GMHOTSALES,
            SmBestEntity::class.java,
            true, null, false, handType, callback, map
        )
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 海外公募
     * @param needCategory 是否需要tab信息 0:否,1:是
     */
    fun reqGmBestList(handType: Int, callback: IReqNetFinished) {
        val map = HashMap<String, Any?>()
        val params = createNormalReqParams(
            UrlKey.HK_V250_GMHOTSALES,
            GmBestBody::class.java,
            true, null, false, handType, callback, map
        )
        HttpCaller.getInstance().request(params, callback)
    }


    /**
     * 请求私募走势图数据
     * @param chartUrl 走势图url
     */
    fun reqSmChartJosnByUrl(chartUrl: String?, handType: Int, callback: IReqNetFinished?) {
        HttpCaller.getInstance().requestNormal(
            chartUrl,
            SmListChartBody::class.java, false, CacheMode.ONLY_READ_CACHE, handType, callback
        )
    }

    /**
     * 获取黑名单配置信息
     */
    fun queryBlackIpConfig(handType: Int, callback: IReqNetFinished?) {
        HttpCaller.getInstance().requestNormal(
            UrlKey.HK_MAIN_V828_XWYZ_GETHOST,
            BlacklistIPInfoBody::class.java, true, null,
            handType, callback
        )
    }

    /**
     * 获取海外app灰度功能配置信息
     * 说明: 这个接口与掌基是同一个, 但在实际业务中,两个app传入的参数有稍微的差别,目前不需要的参数,都没有传入
     * 新灰度平台(产线): http://cms.intelnal.howbuy.com/#/login?redirect=/home
     * 新灰度平台(测试): http://howbuy-cms.it35.k8s.howbuy.com/#/login?redirect=/link/table
     * 日志回捞平台("app 日志上报" 栏)[产线]: http://grayscale-cms.intelnal.howbuy.com/index.html#/grayscale/featurelist
     */
    fun queryAppFuncConfig(
        osVer: String, h5Ver: String?, hkCustNo: String?,
        handType: Int, callback: IReqNetFinished?

    ) {
        HttpCaller.getInstance().requestNormal(
            UrlKey.HK_MAIN_GRAYSCALE_GET_FEATURE_LISTS, object : TypeToken<List<GrayFunctionBean>>() {
            }.type,
            false, CacheMode.CACHEE_THEN_NETWORK, handType, callback,
            "osVer", osVer,
            "app", "hwapp", "source", "0", "h5Ver", h5Ver, "hkCustNo", hkCustNo
        )
    }

    /**
     * 海外资讯
     * @param tabCode   查询指定资讯tap下内容(不传:默认查询推荐内容)
     * @param queryTime   上次请求时间(yyyy-MM-dd HH:mm:ss)
     * @param currPage  当前页（从1开始，默认值为1）
     * @param pageSize  每页大小（默认每页20条）
     */
    fun reqSmNewsList(
        tabCode: String?,
        queryTime: String?,
        currPage: String,
        pageSize: String,
        handType: Int,
        callback: IReqNetFinished?
    ) {
        val map = HashMap<String, Any?>()
        map["hkCustNo"] = getHkCustNo()
        map["tabCode"] = tabCode
        map["queryTime"] = queryTime
        map["currPage"] = currPage
        map["pageSize"] = pageSize
        val params = createNormalReqParams(
            UrlKey.HK_V260_NEWS,
//            "https://apifoxmock.com/m1/2829913-1212853-default/hk/v260/news.json",
            HeadlinesData::class.java,
            true, null, false, handType, callback, map
        )
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 专栏详情页
     * @param columnId  专栏id
     * @param currPage  当前页（从1开始，默认值为1）
     * @param pageSize  每页大小（默认每页20条）
     */
    fun reqSmColumnDetails(
        columnId: String,
        currPage: String,
        pageSize: String,
        handType: Int,
        callback: IReqNetFinished?
    ) {
        val map = HashMap<String, Any?>()
        map["hkCustNo"] = getHkCustNo()
        map["columnId"] = columnId
        map["currPage"] = currPage
        map["pageSize"] = pageSize
        val params = createNormalReqParams(
            UrlKey.HK_V210_NEWSCOLUMN,
            ColumnData::class.java,
            true, null, false, handType, callback, map
        )
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 资讯视听搜索-大家都在搜
     */
    fun reqHotSearchInfo(handType: Int, callback: IReqNetFinished?) {
        val url = UrlKey.HK_V260_HOTSEARCH
//        val url = "https://apifoxmock.com/m1/2829913-1212853-default/hk/v260/hotsearch.json"
        val params = createNormalReqParams(url, HotRearchData::class.java, true, null, false, handType, callback, null)
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 头条资讯截图中,配置的 两个二维码
     * @param type 0只查二维码，1查所有信息
     * @param contentId 获取文章内容,但app端不需要文章信息,接口必传该字段
     */
    fun reqHeadNewsQrcodeInfo(handType: Int, callback: IReqNetFinished?) {
        HttpCaller.getInstance().requestNormal(
            UrlKey.HK_V210_NEWSINFO,
            HeadlinesQrcodeBody::class.java, true, null,
            handType, callback, "type", "0", "contentId", "1"
        )
    }

    /**
     * 入参 海外资讯搜索
     *  @param queryType 查询类型(默认:ALL-全部 ZX-资讯 ST-视听  )
     *  @param q 搜索内容
     *  @param page 页码
     *  @param perPage 每页数量
     */
    fun searchHeadlines(
        queryType: String,
        q: String,
        page: String,
        perPage: String
    ): Observable<ReqResult<ReqNetOpt?>> {
        val map = HashMap<String, Any?>()
        map["queryType"] = queryType
        map["q"] = q
        map["page"] = page
        map["perPage"] = perPage
        val params = createNormalReqParams(
            UrlKey.HK_V260_SEARCHNEWS,
//            "https://apifoxmock.com/m1/2829913-1212853-default/hk_v260_searchnews.do",
            HeadlinesSearchResult::class.java, true, null, false, 0, { }, map
        )
        return RxRequestObservable.buildObservable(params)
    }

    /**
     * 首页-预约咨询提交(合作预约)
     * reqSource  1:首页 2:交易须知-提醒我(默认1)
     */
    fun subscribeSubmitHomePage(
        title: String?,
        name: String,
        mobileAreaCode: String,
        mobile: String,
        email: String,
        reqSource: String,
        callback: IReqNetFinished?
    ) {
        val map = HashMap<String, Any?>()
        map["hkCustNo"] = getHkCustNo()
        map["type"] = "129"//好买香港预约咨询
        map["title"] = title
        map["name"] = name
        map["mobileAreaCode"] = mobileAreaCode
        map["mobile"] = mobile
        map["email"] = email
        map["reqSource"] = reqSource
        val params = createNormalReqParams(
            UrlKey.HK_USER_YUYUE_COOPERATION_PRECONTRACT, CommonDto::class.java,
            true, null, false, 0, callback, map
        )
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 档案页-预约接口
     * 118:基金档案页面产品预约; "129":首页预约咨询/产品档案页面-交易须知预约也用129;
     */
    fun subscribeSubmitFundPage(
        fundCode: String,
        fundName: String?,
        type: String,
        name: String,
        mobileAreaCode: String,
        mobile: String,
        email: String,
        callback: IReqNetFinished?
    ) {
        val map = HashMap<String, Any?>()
        map["hkCustNo"] = getHkCustNo()
        map["jjdm"] = fundCode
        map["memo"] = fundName
        map["type"] = type
        map["name"] = name
        map["mobileAreaCode"] = mobileAreaCode
        map["mobile"] = mobile
        map["email"] = email
        val params = createNormalReqParams(
            UrlKey.HK_USER_YUYUE_PRODUCT_PRECONTRACT, CommonDto::class.java,
            true, null, false, 0, callback, map
        )
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 私募自选持仓列表顺序数据-7.7.6
     * /hk/v240/simuholdjjdmindex.json
     * @param jjdms 基金代码(多个用逗号分割),[该字段传值,代表保存基金顺序, 不传代表查询该用户的基金顺序](全量jjdm拼接)
     */
    fun reqSmHoldSortData(jjdms: String?, topJjdms: String?, handType: Int, callback: IReqNetFinished?) {
        val map = HashMap<String, Any?>()
        map["hkCustNo"] = getHkCustNo()
        map["jjdms"] = jjdms
        map["topJjdms"] = topJjdms
        val params = createNormalReqParams(
            UrlKey.HK_V240_SIMUHOLDJJDMINDEX,
            SmOptionalHoldSortBody::class.java,
            true, null, false, handType, callback, map
        )
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 私募自选持仓列表顺序数据-7.7.6
     * hk/v240/usersimufavoritesort.json
     *
     * @param jjdms 基金代码(多个用逗号分割),[该字段传值,代表保存基金顺序, 不传代表查询该用户的基金顺序](全量jjdm拼接)
     */
    fun reqSmOptionalFavSort(jjdms: String?, topJjdms: String?, handType: Int, callback: IReqNetFinished?) {
        val map = HashMap<String, Any?>()
        map["hkCustNo"] = getHkCustNo()
        map["jjdms"] = jjdms
        map["toTopJjdms"] = topJjdms
        val params = createNormalReqParams(
            UrlKey.HK_V240_USERSIMUFAVORITESORT,
            CommonDto::class.java,
            true, null, false, handType, callback, map
        )
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 实时提交, 私募自选自定义分组操作接口--7.5.8
     * hk/v240/simufavoritecustomgroupoperate.json
     * @param operateList 操作数据json数组字符串([{groupId:分组id(新增为空),groupName:分组名称,operateType:操作类型(0:删除,1:新增,2:修改)}])
     */
    fun commitSmOptionalGroup(
        operateList: String?,
        handType: Int,
        callback: IReqNetFinished?
    ) {
        val map = HashMap<String, Any?>()
        map["hkCustNo"] = getHkCustNo()
        map["operateList"] = operateList
        val params = createNormalReqParams(
            UrlKey.HK_V240_SIMUFAVORITECUSTOMGROUPOPERATE,
            CommonDto::class.java,
            true, null, false, handType, callback, map
        )
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 私募自选基金列表相关信息接口 --8.0.6
     * hk/v240/smfavoritefundlist.json
     * @param fundCodes 按基金code用,拼接查询基金的信息, 有code,以code为准,无code,以hboneNo为准
     * @param hboneNo   用户账号
     */
    fun reqOptionalFundInfo(
        fundCodes: String?,
        handType: Int,
        callback: IReqNetFinished?
    ) {
        val map = HashMap<String, Any?>()
        map["hkCustNo"] = getHkCustNo()
        map["fundCodes"] = fundCodes
        val params = createNormalReqParams(
            UrlKey.HK_V240_SMFAVORITEFUNDLIST,
            SmOptResponseBody::class.java,
            true, null, false, handType, callback, map
        )
        HttpCaller.getInstance().request(params, callback)
    }


    /**
     * 批量修改用户自选基金726
     * /hk/v240/userfavoritemodifybatch.json
     * @param jjdms 逗号分隔
     * @param type  用户关注产品类型(1:公募基金--默认,4:私募基金)
     */
    fun modifyMoreUserFavorite(
        jjdms: String,
        type: String?,
        isAdd: Boolean
    ): Observable<ReqResult<ReqNetOpt?>?>? {
        return RxHttpObservable.buildObservable(
            UrlKey.HK_V240_USERFAVORITEMODIFYBATCH,
            MoreUserfavoriteModel::class.java,
            false,
            true,
            null,
            "hkCustNo",
            getHkCustNo(),
            "jjdms",
            jjdms,
            "type",
            type,
            "status",
            if (isAdd) "1" else "0"
        )
    }

    /**
     * 私募自选批量添加删除自定义分组关系接口--7.5.8
     * /hk/v240/batchoperatefavoritegrouprelation.json
     * @param jjdms       基金代码列表(英文逗号分隔，排在前面的先添加)
     * @param addGroupIds 新增自定义分组id(多个,分隔)
     * @param delGroupIds 删除自定义分组id(多个,分隔)
     */
    fun reqSmOptionalGroupOptBatch(
        jjdms: String?,
        addGroupIds: String?,
        delGroupIds: String?,
        handType: Int,
        callback: IReqNetFinished?
    ) {
        val map = HashMap<String, Any?>()
        map["hkCustNo"] = getHkCustNo()
        map["jjdms"] = jjdms
        map["addGroupIds"] = addGroupIds
        map["delGroupIds"] = delGroupIds
        val params = createNormalReqParams(
            UrlKey.HK_V240_BATCHOPERATEFAVORITEGROUPRELATION,
            CommonDto::class.java,
            true, null, false, handType, callback, map
        )
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 私募自选基金排序--7.7.6
     * /hk/v240/batchsortfavoritebygroupid.json
     * @param dataArray 操作的对象 (数据如 [ { "groupId":"5002", "jjdmArray":"P00107,P00108" },
     * { "groupId":"5002", "jjdmArray":"P00107,P00108","topArray":"" }] ) ]
     * (英文逗号分隔，列表从上到下为1-2-3，传入3,2,1)
     */
    fun reqSmCustomGroupSortBatch(
        dataArray: String?,
        handType: Int,
        callback: IReqNetFinished?
    ) {
        val map = HashMap<String, Any?>()
        map["hkCustNo"] = getHkCustNo()
        map["dataArray"] = dataArray
        val params = createNormalReqParams(
            UrlKey.HK_V240_BATCHSORTFAVORITEBYGROUPID,
            CommonDto::class.java,
            true, null, false, handType, callback, map
        )
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 私募自选批量移除--7.7.6
     * /hk/v240/simufavbatchremove.json
     * @param dataArray 操作的对象 (数据如 [{"groupId":"5002", "jjdmArray":"P00107,P00108"},
     * {"groupId":"5002", "jjdmArray":"P00107,P00108"} ] )
     */
    fun reqSmRemoveCustomFavBatch(
        dataArray: String?,
        handType: Int,
        callback: IReqNetFinished?
    ) {
        val map = HashMap<String, Any?>()
        map["hkCustNo"] = getHkCustNo()
        map["dataArray"] = dataArray
        val params = createNormalReqParams(
            UrlKey.HK_V240_SIMUFAVBATCHREMOVE,
            CommonDto::class.java,
            true, null, false, handType, callback, map
        )
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 根据groupId查询当前分组下的自选基金fundcode列表--7.5.8
     * /hk/v240/getfavoritelistbygroupid.json
     * @param dataRange 查询数据范围(0: 全部-默认,1: 净值型)
     * @param groupId   自定义分组id
     */
    fun reqSmFavorListByGroupId(
        groupId: String?,
        handType: Int,
        callback: IReqNetFinished?
    ) {
        val map = HashMap<String, Any?>()
        map["hkCustNo"] = getHkCustNo()
        map["groupId"] = groupId
        val params = createNormalReqParams(
            UrlKey.HK_V240_GETFAVORITELISTBYGROUPID,
            SmFavorFundCodeBody::class.java, true, null, false, handType, callback, map
        )
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 私募自选持仓基金一键全部置顶 --7.7.6
     * /hk/v240/simucctotopall.json
     * @param fundCodes 持仓基金代码逗号分割
     */
    fun reqSmHoldOneKeyTop(
        fundCodes: String?,
        handType: Int,
        callback: IReqNetFinished?
    ) {
        val map = HashMap<String, Any?>()
        map["hkCustNo"] = getHkCustNo()
        map["fundCodes"] = fundCodes
        val params = createNormalReqParams(
            UrlKey.HK_V240_SIMUCCTOTOPALL,
            SmFavorFundCodeBody::class.java,
            true, null, false, handType, callback, map
        )
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 私募热门推荐数据
     *
     */
    fun reqSmOptionalHotRecommend(handType: Int, callback: IReqNetFinished?) {
        val map = HashMap<String, Any?>()
        map["hkCustNo"] = getHkCustNo()
        map["custRiskLevel"] = apiUserInfo().riskLevel()
        map["investorQualification"] = apiUserInfo().investorType()
        val params = createNormalReqParams(
            UrlKey.HK_V240_FAVORITERECOMMENDPRODUCTLIST,
            SmOptionalRecommendData::class.java,
            true, null, false, handType, callback, map
        )
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 提交: 打开/关闭 预设分组开关接口
     * /hk/v240/simufavoriteswitchoperate.json
     * @param hboneNo
     * @param ccSwitch,zqSwitch,gsSwitch,gqSwitch (0 : 关, 1 : 开)
     */
    fun reqSmOptionalGroupSwitchStatus(
        ccSwitch: String?,
        handType: Int,
        callback: IReqNetFinished?
    ) {
        val map = HashMap<String, Any?>()
        map["hkCustNo"] = getHkCustNo()
        map["ccSwitch"] = ccSwitch
        val params = createNormalReqParams(
            UrlKey.HK_V240_SIMUFAVORITESWITCHOPERATE,
            CommonDto::class.java,
            true, null, false, handType, callback, map
        )
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 私募自选自定义分组批量同步接口--7.5.8
     * /hk/v240/favoritecustomgroupoperatebatch.jso
     * @param groupListData  自定义分组列表数据json字符串({groupId:分组id(新增为空),groupName:分组名称})
     * @param deleteGroupIds 自定义分组删除id(多个,分隔)
     */
    fun syncSmOptionalGroupBatch(
        groupListData: String?,
        deleteGroupIds: String?,
        handType: Int,
        callback: IReqNetFinished?
    ) {
        val map = HashMap<String, Any?>()
        map["hkCustNo"] = getHkCustNo()
        map["groupListData"] = groupListData
        map["deleteGroupIds"] = deleteGroupIds
        val params = createNormalReqParams(
            UrlKey.HK_V240_FAVORITECUSTOMGROUPOPERATEBATCH,
            CommonDto::class.java,
            true, null, false, handType, callback, map
        )
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 获取私募自选分组数据列表,单基金所属分组接口
     * /hk/v240/simufavoritegrouplist.json
     * @param hboneNo
     * @param fundCode   [可选]基金代码(查询单个基金分组列表)
     * @param queryCount [可选]是否查询自选数量1:是;0:否
     */
    fun reqSmOptionalGroupTitleList(
        fundCode: String?,
        queryCount: String?,
        handType: Int,
        callback: IReqNetFinished?
    ) {
        val map = HashMap<String, Any?>()
        map["hkCustNo"] = getHkCustNo()
        map["fundCode"] = fundCode
        map["queryCount"] = queryCount
        val params = createNormalReqParams(
            UrlKey.HK_V240_SIMUFAVORITEGROUPLIST,
            SmOptionalGroupTitleBody::class.java,
            true, null, false, handType, callback, map
        )
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 海外产品档案页面-风险指标
     */
    fun reqProdDetailsRiskInfoList(fundCode: String?, handType: Int, callback: IReqNetFinished) {
        val map = HashMap<String, Any?>()
        map["fundCode"] = fundCode
        val params = createNormalReqParams(
            UrlKey.HK_V250_RISKINDEX,
            SmRiskIndicatorBody::class.java,
            true, null, false, handType, callback, map
        )
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 快速pk-推荐数据
     */
    fun reqSmQuickPkRecommend(fundCode: String?, handType: Int, callback: IReqNetFinished) {
        val map = HashMap<String, Any?>()
        map["fundCode"] = fundCode
        map["hkCustNo"] = getHkCustNo()
        map["investorQualification"] = apiUserInfo().investorType()
        val url = UrlKey.HK_V250_PKRECOMMENDFUND
        val params = createNormalReqParams(url, QuickPkRecommend::class.java, true, null, false, handType, callback, map)
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 档案页pk搜索
     */
    fun reqSearchPkData(key: String, page: Int = 1, handType: Int, callback: IReqNetFinished?) {
        val map = HashMap<String, Any?>()
        map["q"] = key
        map["page"] = page.toString()
        map["perPage"] = "20"
        map["hkCustNo"] = getHkCustNo()
        map["investorQualification"] = apiUserInfo().investorType()
        val url = UrlKey.HK_PK_SEARCHFUND
        val params = createNormalReqParams(
            url, SearchResultInfo::class.java, true, null, false, handType, callback, map
        )
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 海外档案页-快速PK-同区间业绩走势图生成
     * @param fundCodes  基金代码 (以,分割)
     * @param type      {0:自定义 1:同区间, 2：近3年, 3：近1年 , 4:近6个月, 5:近3个月, 6:近1个月,7:今年以来}
     * @param zsdm      指数代码
     * @param startTime 开始时间(yyyyMMdd)
     */
    fun reqQuickPkHb(fundCode: String, type: String, handType: Int, callback: IReqNetFinished) {
        val map = HashMap<String, Any?>()
        map["fundCodes"] = fundCode
        map["type"] = type
        val url = UrlKey.HK_V250_PKCHARTHBCOMPARE
        val params = createNormalReqParams(url, QuickPkChartBody::class.java, true, null, false, handType, callback, map)
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 私募对比动态回撤数据--7.1.0
     * @param fundCodes 基金代码,多个以 , 隔开
     * @param type     {1:同区间-默认, 2：近3年, 3：近1年 , 4:近6月, 5:成立以来}
     */
    fun reqQuickPkHc(fundCode: String, type: String, handType: Int, callback: IReqNetFinished) {
        val map = HashMap<String, Any?>()
        map["fundCodes"] = fundCode
        map["type"] = type
        val url = UrlKey.HK_V250_PKCHARTHCCOMPARE
        val params = createNormalReqParams(url, QuickPkDthcBody::class.java, true, null, false, handType, callback, map)
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 私募基金交易相关日期--8.3.6
     * @param month  月份(yyyyMM)[服务端根据这个值往前查7天, 往后查7天]
     */
    fun reqSmDetailsTradeCalendar(
        fundCode: String?, month: String?,
        handType: Int, callback: IReqNetFinished?
    ) {
        val map = HashMap<String, Any?>()
        map["fundCode"] = fundCode
        map["month"] = month
        val params = createNormalReqParams(
            UrlKey.HK_V250_TRADECALENDERLIST,
            SmTradeCalendarBody::class.java,
            true,
            null,
            false,
            handType,
            callback,
            map
        )
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 档案页-相关资讯
     */
    fun reqSmDetailNewsList(fundCode: String, handType: Int, callback: IReqNetFinished?) {
        val map = HashMap<String, Any?>()
        map["fundCode"] = fundCode
        val params = createNormalReqParams(
            UrlKey.HK_V250_FUNDRELATEDNEWS,
            HeadlinesData::class.java,
            true, null, false, handType, callback, map
        )
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 全局弹框接口
     * key: 默认 "hmqq-tp"
     */
    fun reqAppGlobalPopDlg(key: String, handType: Int, callback: IReqNetFinished?) {
        val map = HashMap<String, Any?>()
        map["popupkey"] = key
        map["hkCustNo"] = getHkCustNo()
        val url = UrlKey.HK_V250_GLOBALPOPUP
        val params = createNormalReqParams(url, GlobalDialogData::class.java, true, null, false, handType, callback, map)
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 头条视听tab-视频数据
     * @param queryTime yyyy-MM-dd HH:mm:ss
     * @param subTabCode subTabCode 默认返回推荐subTabCode 产品策略 （0:全部 1:主观股多 2:量化 3:对冲 4:类固收 5:股权 7:海外),
     *                              默认进入资讯/视听页面 首页请求tab信息和推荐页面请求时, 无subTabCode,直接传入null
     */
    fun reqHeadlinesHwVideo(
        tabCode: String?, subTabCode: String?, queryTime: String?,
        currPage: String, pageSize: String,
        handType: Int, callback: IReqNetFinished
    ) {
        val map = HashMap<String, Any?>()
        map["hkCustNo"] = getHkCustNo()
        map["tabCode"] = tabCode
        map["subTabCode"] = subTabCode
        map["queryTime"] = queryTime
        map["currPage"] = currPage
        map["pageSize"] = pageSize
        val url = UrlKey.HK_V260_AUDIOVISUAL
//        val url = "https://apifoxmock.com/m1/2829913-1212853-default/hk/v260/audiovisual.json"
        val params = createNormalReqParams(url, HwVideoBody::class.java, true, null, false, handType, callback, map)
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 音视频播放时长记录添加
     *  @param  contentId 音视频ID
     *  @param  type 数据类型("0":音频,"1":视频 "2":直播)
     *  @param  [playTime] 播放时间, 单位: s
     */
    fun reqUploadPlayTimeDuration(
        contentId: String?,
        type: String?,
        playTime: String?,
        handType: Int,
        callback: IReqNetFinished
    ) {
        val map = HashMap<String, Any?>()
        map["hkCustNo"] = getHkCustNo()
        map["contentId"] = contentId
        map["type"] = type
        map["playTime"] = playTime
        val url = UrlKey.HK_V260_SMAUDIOPLAYRECORDADD
//            "https://apifoxmock.com/m1/2829913-1212853-default/hk/v260/smvaudioplayrecordadd.json"
        val params = createNormalReqParams(url, CommonDto::class.java, true, null, false, handType, callback, map)
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 音视频播次数上报.
     * 注意: 这个接口要加密参数处理,走交易这套规则,所以,下行参数必须是交易数据格式
     * @param objectCode 对象id
     * @param operType 操作类型 1 点赞; 2 浏览 ; 3 踩帖
     * @param analyseType  操作场景{29:海外资讯；30海外视频；31海外直播}
     * @param isCancel  是否取消操作（针对点赞） 1:是 0：否 默认为0
     * @param phone  手机 (传带后四位掩码)
     */
    fun reqUploadPlayCount(
        objectCode: String?, operType: String?, analyseType: String?,
        isCancel: String?,
        handType: Int, callback: IReqNetFinished
    ) {
        val map = HashMap<String, Any?>()
        map["hkCustNo"] = getHkCustNo()
        map["objectCode"] = objectCode
        map["operType"] = operType
        map["analyseType"] = analyseType
        map["isCancel"] = isCancel
        val url = UrlKey.HK_V260_STATISTICANALYSEENC
//            "https://apifoxmock.com/m1/2829913-1212853-default/hk/v260/statisticanalyseenc.json"
        val params = createNormalReqParams(url, SimpleDto::class.java, true, null, true, handType, callback, map)
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 获取资讯文章tts语音播放内容
     * @param contentId 文章内容id
     * @param type 内容类型(ZX:资讯)
     */
    fun reqSmTtsContent(contentId: String?, handType: Int, callback: IReqNetFinished) {
        val map = HashMap<String, Any?>()
        map["contentId"] = contentId
        map["type"] = "ZX"
        val url = UrlKey.HK_V260_SIMUVOICECONTENT
//            "https://apifoxmock.com/m1/2829913-1212853-default/hk/v260/simuvoicecontent.json"
        val params = createNormalReqParams(url, HwTtsContentBody::class.java, true, null, false, handType, callback, map)
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 资讯专栏合集
     */
    fun reqColumnList(handType: Int, callback: IReqNetFinished) {
        val url = UrlKey.HK_V260_COLUMNLIST
//        val url = "https://apifoxmock.com/m1/2829913-1212853-default/hk/v260/columnlist.json"
        val params = createNormalReqParams(url, ColumnList::class.java, true, null, false, handType, callback, null)
        HttpCaller.getInstance().request(params, callback)
    }

    /*************************************vhall直播-start********************************************/

    /**
     * vhall直播前置中转页
     * @param liveId
     */
    fun reqSmVhallPreviewData(liveId: String?): Observable<ReqResult<ReqNetOpt>> {
        val url = UrlKey.HK_V260_LIVETRANSFERINFO
//        val url = "https://apifoxmock.com/m1/2829913-1212853-default/hk/v260/livetransferinfo.json"
        return RxHttpObservable.buildObservable(
            url,
            SmVhallPreviewData::class.java,
            false,
            true,
            null,
            "hkCustNo",
            getHkCustNo(),
            "liveId",
            liveId
        )
    }

    /**
     * 私募视频直播分享海报信息接口
     */
    fun reqSmLivingPosterInfo(liveId: String?, handType: Int, callback: IReqNetFinished?) {
        val url = UrlKey.HK_V260_SIMULIVEPOSTERINFO
//        val url = "https://apifoxmock.com/m1/2829913-1212853-default/hk/v260/simuliveposterinfo.json"
        val map = HashMap<String, Any?>()
        map["hkCustNo"] = getHkCustNo()
        map["liveId"] = liveId
        val params = createNormalReqParams(url, SmLivePosterBody::class.java, true, null, false, handType, callback, map)
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 获取直播视频是否直播状态信息
     */
    fun reqVhallVideoLivingStatus(webinarId: String?, handType: Int, callback: IReqNetFinished?) {
        val url = UrlKey.HK_V260_GETVHALLLIVEDETAIL
//        val url = "https://apifoxmock.com/m1/2829913-1212853-default/hk/v260/getvhalllivedetail.json"
        val map = HashMap<String, Any?>()
//        map["hkCustNo"] = getHkCustNo()
        map["webinarId"] = webinarId
        val params = createNormalReqParams(url, SmVhallVideoStatusBody::class.java, true, null, false, handType, callback, map)
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 获取直播预告信息
     *
     * @param msgSwitch 短信提醒开关（0:关 1:开）
     * @param type      预约类型（126：直播预约）http://data.it42.k8s.howbuy.com/cgi
     */
    fun reqVhallHeraldMsgSwitchStatus(
        liveId: String?,
        msgSwitch: String?,
        type: String?,
        handType: Int,
        callback: IReqNetFinished?
    ) {
        val url = UrlKey.HK_V260_LIVEREMINDSWITCH
//        val url = "https://apifoxmock.com/m1/2829913-1212853-default/hk/v260/liveremindswitch.json"
        val map = HashMap<String, Any?>()
        map["hkCustNo"] = getHkCustNo()
        map["liveId"] = liveId
        map["msgSwitch"] = msgSwitch
        map["type"] = type
        val params = createNormalReqParams(url, CommonDto::class.java, true, null, false, handType, callback, map)
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 直播预约接口
     * sourceUrl 预约来源
     * type 预约类型（126：直播预约）
     */
    fun reqVhallHeraldSubmit(
        liveId: String?,
        sourceUrl: String?,
        type: String?,
        handType: Int,
        callback: IReqNetFinished?
    ) {
        val url = UrlKey.HK_V260_ADDLIVEAPPOINT
//        val url = "https://apifoxmock.com/m1/2829913-1212853-default/hk/v260/addliveappoint.json"
        val map = HashMap<String, Any?>()
        map["hkCustNo"] = getHkCustNo()
        map["liveId"] = liveId
        map["sourceUrl"] = sourceUrl
        map["type"] = type
        val params = createNormalReqParams(url, SmVhallHeraldSubmitBody::class.java, true, null, false, handType, callback, map)
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 直播密码校验
     */
    fun reqSmVhallVideoValidPwd(liveId: String?, password: String, handType: Int, callback: IReqNetFinished) {
        val url = UrlKey.HK_V260_LIVEPWDCHECK
//        val url = "https://apifoxmock.com/m1/2829913-1212853-default/simu/v806/livepwdcheck.json"
        val map = HashMap<String, Any?>()
        map["hkCustNo"] = getHkCustNo()
        map["liveId"] = liveId
        map["password"] = password
        val params = createNormalReqParams(url, SmVhallVideoValidPwdBody::class.java, true, null, false, handType, callback, map)
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * vhall直播详情
     * @param liveId
     */
    fun reqSmVhallDetail(liveId: String?): Observable<ReqResult<ReqNetOpt>> {
        return RxHttpObservable.buildObservable(
            UrlKey.HK_V260_VHALLLIVEDETAIL,
//            "https://apifoxmock.com/m1/2829913-1212853-default/hk/v260/vhalllivedetail.json",
            SmVhallDetail::class.java, false, true, null,
            "hkCustNo", getHkCustNo(),
            "liveId", liveId
        )
    }

    /**
     * 直播后-评论列表
     * itemId 直播id
     * itemSource 评论类型 21:海外资讯；22:海外视频;23:海外直播
     * [pagenum=1] 页码
     * [perpage=20] 每页记录数
     */
    fun reqVahllCommentList(
        itemId: String?,
        itemsource: String?,
        pagenum: String?,
        perpage: String?,
        handType: Int,
        callback: IReqNetFinished?
    ) {
        val url = UrlKey.HK_V210_DISCUSSIONLIST
//        val url = "https://apifoxmock.com/m1/2829913-1212853-default/hk/v210/discussionlist.json"
        val map = HashMap<String, Any?>()
        map["hkCustNo"] = getHkCustNo()
        map["itemId"] = itemId
        map["itemSource"] = itemsource
        map["pagenum"] = pagenum
        map["perpage"] = perpage
        val params = createNormalReqParams(url, CommentBody::class.java, true, null, false, handType, callback, map)
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 直播后发评论.
     * 注意: 这个接口要加密参数处理,走交易这套规则,所以,下行参数必须是交易数据格式
     * itemId:直播id
     * itemSource 评论类型 21:海外资讯；22:海外视频;23:海外直播
     */
    fun addVhallComment(
        itemId: String?,
        content: String?,
        theme: String?,
        itemsource: String?,
        handType: Int,
        callback: IReqNetFinished?
    ) {
        val url = UrlKey.HK_V260_DISCUSSIONADDENC
//        val url = "https://apifoxmock.com/m1/2829913-1212853-default/hk/v260/discussionaddenc.json"
        val map = HashMap<String, Any?>()
        map["hkCustNo"] = getHkCustNo()
        map["itemId"] = itemId
        map["content"] = content
        map["theme"] = theme
        map["itemSource"] = itemsource
        val params = createNormalReqParams(url, SimpleDto::class.java, true, null, true, handType, callback, map)
        HttpCaller.getInstance().request(params, callback)
    }

    /*************************************vhall直播-end**********************************************/

    /**
     * 持牌人主页信息
     */
    fun reqBusinessCardHome(): Observable<ReqResult<ReqNetOpt>> {
        return RxHttpObservable.buildObservable(
            UrlKey.HK_V290_TGMP_MAININFO,
//            "https://m1.apifoxmock.com/m1/2829913-1212853-default/hk/v290/tgmp/maininfo.json",
            BusinessCardInfo::class.java, false, true, null,
            "hkCustNo", getHkCustNo()
        )
    }

    /**
     * 持牌人主页内容
     */
    fun reqBusinessCardContent(): Observable<ReqResult<ReqNetOpt>> {
        return RxHttpObservable.buildObservable(
            UrlKey.HK_V290_TGMP_GETRELATIONCONTENT,
//            "https://m1.apifoxmock.com/m1/2829913-1212853-default/hk/v290/tgmp/getrelationcontent.json",
            BusinessCardContent::class.java, false, true, null,
            "hkCustNo", getHkCustNo()
        )
    }

    /**
     * 投顾名片风格数据提交
     * /hk/v290/tgmp/updatestyletype.json
     * @param templateId    模版ID
     * @param styleType     名片风格(1:好买红、2:商务蓝、3:海报风)
     */
    fun reqBusinessCardStyle(templateId: String, styleType: String): Observable<ReqResult<ReqNetOpt>> {
        return RxHttpObservable.buildObservable(
            UrlKey.HK_V290_TGMP_UPDATESTYLETYPE,
//            "https://m1.apifoxmock.com/m1/2829913-1212853-default/hk/v290/tgmp/updatestyletype.json",
            CommonDto::class.java, false, true, null,
            "hkCustNo", getHkCustNo(), "templateId", templateId, "styleType", styleType
        )
    }

    /**
     * 更新投顾名片模块开关状态
     * /hk/v290/tgmp/updatemoduleopenflag.json
     * @param templateId    模版ID
     * @param moduleType    模块类型(1:关于好买香港、2:全球热点模块、3:推荐产品模块)
     * @param openFlag      开关标识(true:开；false关)-> (1:开；0关)
     */
    fun reqBusinessCardModuleSwitch(templateId: String, moduleType: String, openFlag: Boolean): Observable<ReqResult<ReqNetOpt>> {
        return RxHttpObservable.buildObservable(
            UrlKey.HK_V290_TGMP_UPDATEMODULEOPENFLAG,
//            "https://m1.apifoxmock.com/m1/2829913-1212853-default/hk/v290/tgmp/updatemoduleopenflag.json",
            CommonDto::class.java, false, true, null,
            "hkCustNo", getHkCustNo(), "templateId", templateId, "moduleType", moduleType, "openFlag", if (openFlag) "1" else "0"
        )
    }

    /**
     * 更新投顾名片模块顺序
     * /hk/v290/tgmp/updatemodulesort.json
     * @param templateId    模版ID
     * @param moduleTypeList 模块类型集合,按调整后的顺序(ex:["1","2","3"])
     */
    fun reqBusinessCardModuleSort(templateId: String, moduleTypeList: MutableList<String>): Observable<ReqResult<ReqNetOpt>> {
        return RxHttpObservable.buildObservable(
             UrlKey.HK_V290_TGMP_UPDATEMODULESORT,
//            "https://m1.apifoxmock.com/m1/2829913-1212853-default/hk/v290/tgmp/updatemodulesort.json",
            CommonDto::class.java, false, true, null,
            "hkCustNo", getHkCustNo(), "templateId", templateId, "moduleTypeList", moduleTypeList
        )
    }

    /**
     * 一键清空推荐产品或内容数据接口
     * /hk/v290/tgmp/deletemodulecontent.json
     * @param templateId    模版ID
     * @param moduleType    模块类型(2:全球热点模块、3:推荐产品模块)
     */
    fun reqBusinessCardContentClean(templateId: String, moduleType: String): Observable<ReqResult<ReqNetOpt>> {
        return RxHttpObservable.buildObservable(
             UrlKey.HK_V290_TGMP_DELETEMODULECONTENT,
//            "https://m1.apifoxmock.com/m1/2829913-1212853-default/hk/v290/tgmp/deletemodulecontent.json",
            CommonDto::class.java, false, true, null,
            "hkCustNo", getHkCustNo(), "templateId", templateId, "moduleType", moduleType
        )
    }

    /**
     * 快捷添加热点或产品信息接口
     * /hk/v290/tgmp/quickaddcontent.json
     * @param templateId    模版ID
     * @param moduleType    模块类型(2:全球热点模块、3:推荐产品模块)
     */
    fun reqBusinessCardContentQuickAdd(templateId: String, moduleType: String): Observable<ReqResult<ReqNetOpt>> {
        return RxHttpObservable.buildObservable(
             UrlKey.HK_V290_TGMP_QUICKADDCONTENT,
//            "https://m1.apifoxmock.com/m1/2829913-1212853-default/hk/v290/tgmp/quickaddcontent.json",
            BusinessCardContentAddResult::class.java, false, true, null,
            "hkCustNo", getHkCustNo(), "templateId", templateId, "moduleType", moduleType
        )
    }

    /**
     * 单条内容添加或删除
     * /hk/v290/tgmp/addordeletecontent.json
     * @param templateId    模版ID
     * @param moduleType    模块类型(2:全球热点模块、3:推荐产品模块)
     * @param contentType   内容类型(1:资讯、2:视频/直播、3:产品)
     * @param contentId     内容ID
     * @param opType        操作类型(1:新增、2:删除)
     */
    fun reqBusinessCardContentSingleAddOrDel(
        templateId: String?,
        moduleType: String,
        contentType: String?,
        contentId: String,
        opType: String
    ): Observable<ReqResult<ReqNetOpt>> {
        return RxHttpObservable.buildObservable(
             UrlKey.HK_V290_TGMP_ADDORDELETECONTENT,
//            "https://m1.apifoxmock.com/m1/2829913-1212853-default/hk/v290/tgmp/addordeletecontent.json",
            BusinessCardContentAddResult::class.java, false, true, null,
            "hkCustNo", getHkCustNo(), "templateId", templateId, "moduleType", moduleType,
            "contentType", contentType, "contentId", contentId, "opType", opType
        )
    }

    /**
     * 分享上报-加密接口
     * 注意: 这个接口要加密参数处理,走交易这套规则,所以,下行参数必须是交易数据格式
     * /hk/v290/tgmp/statisticenc.json
     * @param templateId    模版ID
     * @param opType        操作类型：1:分享:2:点击;3:点赞
     */
    fun reqShareReport(templateId: String): Observable<ReqResult<ReqNetOpt>> {
        return RxHttpObservable.buildObservable(
             UrlKey.HK_V290_TGMP_STATISTICENC,
//            "https://m1.apifoxmock.com/m1/2829913-1212853-default/hk/v290/tgmp/statisticenc.json",
            SimpleDto::class.java, true, true, null,
            "templateId", templateId, "opType", "1"
        )
    }


    /**
     * 编辑投顾信息
     */
    fun updateBusinessInfo(
        pictureUrl: String?,
        oneIntroduction: String?,
        tgTagList: List<String>?
    ): Observable<ReqResult<ReqNetOpt>> {
        val map = HashMap<String, Any?>()
        map["hkCustNo"] = apiUserInfo().getHkCustNo()
        map["pictureUrl"] = pictureUrl
        map["oneIntroduction"] = oneIntroduction
        map["tgTagList"] = tgTagList
        val key = UrlKey.HK_V290_TGMP_UPDATETGINFO
//            "https://m1.apifoxmock.com/m1/2829913-1212853-default/hk/v290/tgmp/updatetginfo.json"
        return RxHttpObservable.buildObservable(
            key,
            SimpleDto::class.java,
            false,
            true,
            null,
            map
        )
    }

    /**
     * 微信登录
     * @param unionId unionId
     */
    fun wechatLogin(unionId: String?): Observable<ReqResult<ReqNetOpt>> {
        val map = HashMap<String, Any?>()
        map["unionId"] = unionId
        return RxHttpObservable.buildObservable(
            UrlKey.CRM_CGI_HKACCOUNT_OHTERLGION_LOGINBYWECHAT,
//            "https://m1.apifoxmock.com/m1/2829913-1212853-default/hkaccount/login/loginByWeChat",
            WeChatLoginResult::class.java, false, true, null,
            map
        )
    }

    /**
     * 创建请求参数对象 表单提交
     */
    private fun createNormalReqParams(
        uri: String?,
        clazz: Type?,
        post: Boolean,
        cacheMode: CacheMode?,
        tradeParseMode: Boolean,
        handType: Int,
        callback: IReqNetFinished?,
        paramsMap: HashMap<String, Any?>?,
    ): ReqParams {
        val url = XUtils.getUrl(uri)
        val safePolicy = XUtils.getSafePolicy(uri)
        val encrypt = XUtils.isNeedEncryption(uri)
        val needEnvelope = XUtils.isNeedEnvelope(uri)
        val needSign = XUtils.isNeedSign(uri)
        val reqParams = ReqParams()
        reqParams.url = url
        reqParams.uriKey = uri
        reqParams.cls = clazz
        reqParams.isPost = post
        reqParams.reqTag = uri
        reqParams.isTradeParseMode = tradeParseMode
        //固定拼接公共参数
        reqParams.needPublicParams = true
        reqParams.safePolicy = safePolicy
        //post by json 格式
        reqParams.requestContentType = RequestContentType.NORMAL
        reqParams.isEncrypt = encrypt
        reqParams.cacheMode = cacheMode
        reqParams.params = paramsMap
        reqParams.bytes = null
        reqParams.handType = handType
        reqParams.reqNetFinished = callback
        reqParams.startTime = System.currentTimeMillis()
        reqParams.isNeedEnvelope = needEnvelope
        reqParams.isNeedSign = needSign
        return reqParams
    }
}