package howbuy.android.global

/**
 * description.
 * 路由地址
 * tao.liang
 * 2024/2/22
 */


const val PATH_FRAG_SPLASH = "/global/main/FragSplash"
const val PATH_ACTIVITY_ENTRY = "/global/main/EntryActivity"
const val PATH_FRAG_TEST = "/global/debug/FragTest"
const val PATH_FRAG_TEST_INPUT = "/global/debug/FragTestInput"

//档案页头部重要信息
const val PATH_ARCHIVE_IMPORT_INFO = "/global/main/FragArchiveImportView"
const val PATH_FRAG_FRAG_TAB_PERFORMANCE = "/global/archive/FragTabPerformance"
const val PATH_SEARCH = "/global/main/search"
const val PATH_FRAG_CHART_DETAIL = "/global/main/FragFundDetailsYjzs"
const val PATH_FRAG_DETAIL_INFO = "/global/main/FragFundDetailsInfo"
const val PATH_FRAG_TRADE_RULE_TAB = "/global/main/FragTradeRuleTab"
const val PATH_FRAG_FUND_DETAIL_NEWS = "/global/main/FragFundDetailsNews"
const val PATH_FRAG_TRADE_CALENDAR = "/global/main/FragTradeCalendar"

//档案页面主页面
const val PATH_FRAG_FUND_DETAILLS = "/global/main/FragFundDetails"
const val PATH_FRAG_SM_SHOW_PDF_LIST = "/global/main/FragSmShowPdfList"
//合规页面
const val PATH_HEGUI_PAGE = "/global/main/FragHeguiPage"

/**海外优选-包含公募私募)*/
const val PATH_FRAG_BEST_PAGE = "/global/main/FragBestPage"

//海外资讯
const val PATH_FRAG_NEWS = "/global/main/FragNews"
//海外资讯专栏详情页
const val PATH_FRAG_COLUMN_DETAILS = "/global/main/FragColumnDetails"
//海外资讯专栏合集
const val PATH_FRAG_COLUMN_LIST = "/global/main/FragColumnList"
/**消息中心*/
const val PATH_FRAG_MESSAGE_CENTER = "/global/main/FragMessageCenter"
//人机安全验证
const val PATH_FRAG_GLOBAL_BLACKLIST_VALID_PAGE = "/global/main/FragBlackListValidPage"
//头条搜索
const val PATH_FRAG_SEARCH_HEADLINES = "/global/main/FragSearchHeadlines"
//自选首页
const val PATH_FRAG_SM_OPTIONAL_TAB_GROUP = "/global/main/FragSmOptionalTabGroup"
//分组管理
const val PATH_FRAG_SM_OPTIONAL_GROUP_MANAGER = "/global/main/FragSmOptionalGroupManager"
//选基金页面
const val PATH_FRAG_SM_CUSTOM_ADD_OPTIONAL_FUND = "/global/main/FragCustomTabAddOptionalFund"
//自选编辑页面
const val PATH_FRAG_SM_OPTIONAL_EDIT = "/global/main/FragTabSmOptionalEdit"
//私募微吼直播Aty
const val PATH_ATY_SM_VHALL = "/global/main/AtySmVhall"
const val PATH_FRAG_SM_VHALL_LIVING = "/global/main/FragSmVhallVideo"
//视频H5 页面
const val PATH_FRAG_SM_LIVING_VIDEO = "/global/main/FragSmLivingVideo"
//强制分享H5页面(如果三方H5页面有,需要分享功能的,需要从外部将参数传入)
const val PATH_FRAG_SHARE_WEBVIEW = "/global/main/FragShareWebView"

// 持牌人名片主页
const val PATH_FRAG_BUSINESS_CARD_HOME = "/global/main/FragBusinessCardHome"

//名片资料编辑
const val PATH_FRAG_BUSINESS_CARD_EDIT = "/global/main/FragBusinessCardEdit"

//图片上传预览
const val PATH_FRAG_BUSINESS_UP_IMAGE = "/global/main/FragBusinessUpImage"

//图片上传结果展示
const val PATH_FRAG_BUSINESS_UP_IMAGE_RESULT = "/global/main/FragBusinessUpImageResult"