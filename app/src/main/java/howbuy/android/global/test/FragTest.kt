package howbuy.android.global.test

import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.webkit.WebStorage
import android.widget.TextView
import androidx.annotation.RequiresApi
import androidx.core.content.FileProvider
import androidx.core.util.Consumer
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.android.arouter.facade.annotation.Route
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.howbuy.android.modifyurl.AtyDebugModifyUrl
import com.howbuy.android.toast.ToastUtils
import com.howbuy.arouter_intercept_api.IGlobalInterceptCode
import com.howbuy.fund.base.IWechatProvider
import com.howbuy.fund.base.config.ApkConfig
import com.howbuy.fund.base.config.ValConfig
import com.howbuy.fund.base.frag.AbsHbFrag
import com.howbuy.fund.base.nav.NavHelper
import com.howbuy.fund.base.push.PushDispatchHelper
import com.howbuy.fund.base.router.RouterHelper
import com.howbuy.fund.logupload.gain.LogGainManager
import com.howbuy.global.login_api.ILoginProvider
import com.howbuy.global.login_api.LoginCallback
import com.howbuy.global.login_api.LoginParams
import com.howbuy.global.login_api.LoginResult
import com.howbuy.global.login_api.LoginRouterPath
import com.howbuy.global.login_api.LoginRouterPath.PATH_BIND_PHONE
import com.howbuy.global.login_api.LoginType
import com.howbuy.h5.H5FileHelper
import com.howbuy.h5.h5config.Configs
import com.howbuy.lib.compont.GlobalApp
import com.howbuy.lib.utils.ColorUtils
import com.howbuy.lib.utils.LogUtils
import com.howbuy.lib.utils.SpanBuilder
import com.howbuy.lib.utils.StrUtils
import com.howbuy.lib.utils.SysUtils
import com.howbuy.router.provider.IWebProvider
import com.howbuy.router.proxy.Invoker
import howbuy.android.global.Constants
import howbuy.android.global.PATH_ATY_SM_VHALL
import howbuy.android.global.PATH_FRAG_BEST_PAGE
import howbuy.android.global.PATH_FRAG_BUSINESS_CARD_EDIT
import howbuy.android.global.PATH_FRAG_BUSINESS_CARD_HOME
import howbuy.android.global.PATH_FRAG_DETAIL_INFO
import howbuy.android.global.PATH_FRAG_MESSAGE_CENTER
import howbuy.android.global.PATH_FRAG_NEWS
import howbuy.android.global.PATH_FRAG_TEST
import howbuy.android.global.PATH_FRAG_TEST_INPUT
import howbuy.android.global.PATH_FRAG_TRADE_CALENDAR
import howbuy.android.global.PATH_SEARCH
import howbuy.android.global.R
import com.howbuy.global.login_impl.wechatlogin.WechatAuthUtil
import kotlinx.coroutines.Runnable
import java.io.File
import java.math.BigDecimal
import java.text.DecimalFormat


@RequiresApi(Build.VERSION_CODES.Q)
@Route(path = PATH_FRAG_TEST)
class FragTest : AbsHbFrag() {
    private val funcList = arrayListOf<Pair<String, Runnable>>()

    init {
        funcList.add(Pair("持牌人名片主页", Runnable {
            val b = Bundle()
            b.putIntArray(ValConfig.IT_INTERCEPT_CODE, intArrayOf(IGlobalInterceptCode.GLOBAL_CHECK_SM_TG))
            RouterHelper.launchFrag(this, PATH_FRAG_BUSINESS_CARD_HOME, b)
        }))
        funcList.add(Pair("海外优选", Runnable {
            RouterHelper.launchFrag(this, PATH_FRAG_BEST_PAGE, null)
        }))
        funcList.add(Pair("名片编辑", Runnable {
            RouterHelper.launchFrag(
                this,
                PATH_FRAG_BUSINESS_CARD_EDIT,
                NavHelper.obtainArg("编辑资料")
            )
        }))
        funcList.add(Pair("资讯-产品策略", Runnable {
            PushDispatchHelper.pushDispatch(context,"T=(SMTT)&V=(13001)")
//            RouterHelper.launchAty(context, PATH_ATY_SM_VHALL, NavHelper.obtainArg("直播", ValConfig.IT_ID, "123"))
        }))
        funcList.add(Pair("视听-路演直播", Runnable {
            PushDispatchHelper.pushDispatch(context,"T=(HWST)&V=(8)")
//            RouterHelper.launchAty(context, PATH_ATY_SM_VHALL, NavHelper.obtainArg("直播", ValConfig.IT_ID, "123"))
        }))
        funcList.add(Pair("vhall直播", Runnable {
            RouterHelper.launchAty(context, PATH_ATY_SM_VHALL, NavHelper.obtainArg("直播", ValConfig.IT_ID, "123"))
        }))
        funcList.add(Pair("空白H5页面", Runnable {
            Invoker.getInstance().navigation(IWebProvider::class.java).launchWebView(
                this,
                "空白页面",
                "about:blank",
                null,
                null
            )
        }))
        funcList.add(Pair("上传日志", Runnable {
            /**
             * 在灰度平台配置 下发开关状态
             * 然后如果是A状态, 在app启动/前后台 会触发上报一次日志文件
             */
            LogGainManager.getInstance().uploadLogByUser(null)
        }))
        funcList.add(Pair("重新登录", Runnable {
            RouterHelper.launchFrag(this, "/login/global/FragTestActive", null)
        }))
        funcList.add(Pair("海外资讯", Runnable {
            RouterHelper.launchFrag(this, PATH_FRAG_NEWS, null)
        }))
        funcList.add(Pair("跳转自定义web", Runnable {
            RouterHelper.launchFrag(this, PATH_FRAG_TEST_INPUT, null)
        }))
        funcList.add(Pair("消息中心", Runnable {
            RouterHelper.launchFrag(this, PATH_FRAG_MESSAGE_CENTER, NavHelper.obtainArg("消息中心"))
        }))
        funcList.add(Pair("跳转搜索", Runnable {
            RouterHelper.launchFrag(this, PATH_SEARCH, null)
        }))
        funcList.add(Pair("跳转档案页头部", Runnable {
//            RouterHelper.launchFrag(this, PATH_ARCHIVE_IMPORT_INFO, null)
            RouterHelper.launchFrag(
                this, PATH_FRAG_TRADE_CALENDAR, NavHelper.obtainArg(
                    "交易日历",
                    ValConfig.IT_FUND_CODE, "H04171"
                )
            )
        }))
        funcList.add(Pair("梁涛", Runnable {
//            LauncherFundDetailsMgr.launcherFundDetails(activity, "P00107")
            //预约产品-交易日历
        }))
        funcList.add(Pair("李根", Runnable {
            //是否绑定微信
            val bindWechat =
                Invoker.getInstance().navigation(IWechatProvider::class.java).wechatBindState()
            LogUtils.d(TAG, "是否绑定微信:$bindWechat")
        }))
        funcList.add(Pair("合规流程", Runnable {
            RouterHelper.launchFrag(this, PATH_FRAG_TEST_INPUT, NavHelper.obtainArg("", ValConfig.IT_BOOLEAN, true))
        }))
        funcList.add(Pair("自动安装权限", Runnable {
            autoInstall()
        }))
        funcList.add(Pair("绑定手机号", Runnable {
            RouterHelper.launchFrag(this, PATH_BIND_PHONE)
        }))
        funcList.add(Pair("微信登录", Runnable {
            wechatLogin()
        }))

        funcList.add(Pair("删除微信auth", Runnable {
            WechatAuthUtil.deleteAuth(activity)
        }))

        funcList.add(Pair("微信重新绑定确认弹框", Runnable {
            val oldName = "旧名字"
            val newName = "新名字"
            val bingContent =
                "该账号已和其他微信${oldName}绑定，需要取消原微信号绑定关系，并重新绑定本微信${newName}吗？"
            val spanBuilder = SpanBuilder(bingContent)
            spanBuilder.color(9, 9 + oldName.length, ColorUtils.parseColor("#C51D25"), false)
            val newIndex = bingContent.length - 2 - newName.length
            spanBuilder.color(
                newIndex,
                newIndex + newName.length,
                ColorUtils.parseColor("#C51D25"),
                false
            )

            Invoker.getInstance().navigation(IWechatProvider::class.java).showBindDialog(
                spanBuilder.getmSp(),
                true,
                this@FragTest,
                object : Consumer<Boolean> {
                    override fun accept(t: Boolean?) {
                        if (t == true) {
                            LogUtils.pop("重新绑定")
                        }
                    }
                })
        }))
        funcList.add(Pair("账号三方登录绑定列表", Runnable {
            RouterHelper.launchFragWithCallback(
                this,
                LoginRouterPath.PATH_FRAG_THIRD_AUTH_LIST,
                NavHelper.obtainArg("账户绑定"),
                null
            )
        }))
    }

    override fun getFragLayoutId(): Int = R.layout.frag_test_entry

    override fun stepAllViews(root: View, savedInstanceState: Bundle?) {
        //测试登录是否可用：密码登录成功，it06

        val tvAppInfo = root.findViewById<TextView>(R.id.tv_app_info)
        tvAppInfo.text =
            "版本号: ${SysUtils.getVersionName(activity)} \n包名: ${activity?.applicationInfo?.packageName}"

        addPwdLoginEntry(root)

        addWebviewEntry(root)

        addAppDownload(root)

        //后门切换成功
        addCgiChangeEntry(root)

        //清除web缓存
        addClearWebCache(root)

        root.findViewById<View>(R.id.btn_chart).setOnClickListener {
//            RouterHelper.launchFrag(
//                activity, PATH_FRAG_CHART_DETAIL,
//                NavHelper.obtainArg(
//                    "业绩走势",
//                    ValConfig.IT_FUND_CODE, "SZM650",
//                    ValConfig.IT_DATA, "hb1n",
//                    ValConfig.IT_TYPE, "0"
//                )
//            )
            RouterHelper.launchFrag(
                activity, PATH_FRAG_DETAIL_INFO,
                NavHelper.obtainArg(
                    "基本信息",
                    ValConfig.IT_FUND_CODE, "SZM650",
                    ValConfig.IT_FUND_NAME, "宽德九臻500指增臻享1号"
                )
            )
        }

        initFunctions(root)
    }

    fun formatF(f: String, format: String?): String {
        try {
            val df = DecimalFormat(if (StrUtils.isEmpty(format)) "0.00" else format)
            val bigDecimal = BigDecimal(f)
            return df.format(bigDecimal)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return f
    }

    private fun addPwdLoginEntry(root: View) {
        root.findViewById<View>(R.id.btnPwdLogin).setOnClickListener {
            Invoker.getInstance()
                .navigation(ILoginProvider::class.java)
                .login(
                    LoginParams(activity ?: return@setOnClickListener, LoginType.password),
                    "FragTest", object : LoginCallback {
                        override fun onComplete(result: LoginResult) {
                            ToastUtils.getInstance()
                                .show(activity ?: return, "成功：${result.success}")
                        }
                    })
        }
    }

    private fun addCgiChangeEntry(root: View) {
        root.findViewById<View>(R.id.btnConfig).setOnClickListener {
            AtyDebugModifyUrl.entry(
                this@FragTest,
                TextUtils.equals("debug", ApkConfig.getBuildType())
            )
        }
        root.findViewById<View>(R.id.btnConfig_save_true).setOnClickListener {
            H5FileHelper.getInstance().getsF().edit().putBoolean(Configs.SF_ZNZIP_SUCCESS, true).apply()
        }

    }

    private fun addClearWebCache(root: View) {
        root.findViewById<View>(R.id.btn_clearwebcache).setOnClickListener {
            val webStorage = WebStorage.getInstance()
            webStorage.deleteAllData() // 删除WebView的所有数据存储
            // 或单独删除某个类型的数据
            webStorage.deleteOrigin("__dom_storage") // DOM Storage
            webStorage.deleteOrigin("appcache") // Application Cache (AppCache)
        }
    }

    private fun addWebviewEntry(root: View) {
        root.findViewById<View>(R.id.btnWebUpload).setOnClickListener {
            RouterHelper.launchFrag(
                activity, "/global/web/FragGlobalWebView", NavHelper.obtainArg(
                    "文件选择js",
                    ValConfig.IT_URL, "file:///android_asset/upload.html"
                )
            )
            activity?.let {
                Invoker.getInstance().navigation(IWebProvider::class.java).launchWebView(
                    it,
                    "文件选择js",
                    "file:///android_asset/upload.html",
                    null,
                    null
                )
            }
        }
        root.findViewById<View>(R.id.btnWebPushModuleAction).setOnClickListener {
            activity?.let {
                Invoker.getInstance().navigation(IWebProvider::class.java).launchWebView(
                    it,
                    "pushModuleAction js相关",
                    "file:///android_asset/js_pushModuleAction.html",
                    null,
                    null
                )
            }
        }

        root.findViewById<View>(R.id.btnWebFunctionAction).setOnClickListener {
            activity?.let {
                Invoker.getInstance().navigation(IWebProvider::class.java).launchWebView(
                    it,
                    "functionAction js相关",
                    "file:///android_asset/js_functionAction.html",
                    null,
                    null
                )
            }
        }

    }

    private fun addAppDownload(root: View) {
        root.findViewById<View>(R.id.btnAppDownload).setOnClickListener {
            val json = """{
        "versionNeedUpdate": "0",
        "fileSize": "157286400",
        "versionNum": "8.2.8",
        "updateUrl": "http://trade.ehowbuy.test/tstatic//upload/mobileAppFile/fund/8.2.8/app-ErenEben-hbTest-v8.2.8-324.apk",
        "updateDesc": "[更新功能]\r\n*全新收益，关心你的每一笔钱\r\n*全新精选页，所有产品一目了然\r\n*新增话题讨论与粉丝专栏，欢迎与志同道合的朋\r\n友一起互动\r\n[功能优化]\r\n*首页加载速度优化\r\n*资产页加载速度优化\r\n*修复了一些BUG"
    }"""
//            val updateInfo = GsonUtils.toObj(
//                json,
//                UpDateInfo::class.java
//            )
//            UpdateManagerDialog(activity, updateInfo).show()
            activity?.let {
//                AppUpgradeDialog(
//                    it, "http://trade.ehowbuy.test/tstatic//upload/mobileAppFile/fund/8.2.8/app-ErenEben-hbTest-v8.2.8-324.apk",
//                    "0","2.0.0", false
//                ).show()
            }
        }
        root.findViewById<View>(R.id.btnAppDownload2).setOnClickListener {
            activity?.let {
//                AppUpgradeDialog(
//                    it, "http://trade.ehowbuy.test/tstatic//upload/mobileAppFile/fund/8.2.8/app-ErenEben-hbTest-v8.2.8-324.apk",
//                    "1","2.0.0", true
//                ).show()
            }
        }
        root.findViewById<View>(R.id.btnAppDownload3).setOnClickListener {
            activity?.let {
//                AppUpgradeDialog(
//                    it, "http://trade.ehowbuy.test/tstatic//upload/mobileAppFile/fund/8.2.8/app-ErenEben-hbTest-v8.2.8-324.apk",
//                    "0","2.0.0", true
//                ).show()
            }
        }
        root.findViewById<View>(R.id.btnAppDownload4).setOnClickListener {
            activity?.let {
//                AppUpgradeDialog(
//                    it, "http://trade.ehowbuy.test/tstatic//upload/mobileAppFile/fund/8.2.8/app-ErenEben-hbTest-v8.2.8-324.apk",
//                    "0","2.0.0", false
//                ).show()
            }
        }
    }

    private fun initFunctions(root: View) {
        val recyclerView = root.findViewById<RecyclerView>(R.id.rv_function)
        val adapter = object :
            BaseQuickAdapter<Pair<String, Runnable>, BaseViewHolder>(R.layout.item_test_func) {
            override fun convert(holder: BaseViewHolder, item: Pair<String, Runnable>) {
                holder.setText(R.id.tv_func_name, item.first)
                holder.getView<View>(R.id.tv_func_name).setOnClickListener {
                    item.second.run()
                }
            }

        }
        adapter.setList(funcList)
        recyclerView.adapter = adapter
    }

    override fun parseArgment(arg: Bundle?) {
    }

    private fun autoInstall() {
        val intent = Intent(Intent.ACTION_VIEW)
        //添加这一句表示对目标应用临时授权该Uri所代表的文件
        intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        // 注意：这里的file需要是在external files目录下
        val file = File("file:///android_asset/app-beta.apk")
        val uri = FileProvider.getUriForFile(
            requireActivity().application,
            requireActivity().application.packageName + ".fileProvider",
            file
        )
        intent.setDataAndType(uri, "application/vnd.android.package-archive")
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        requireActivity().startActivity(intent)
    }

    override fun onNetChanged(netType: Int, preNet: Int): Boolean {
        LogUtils.d("AAA", "网络=" + netType)
        LogUtils.pop("当前网络状态:" + netType)
        return super.onNetChanged(netType, preNet)
    }

    override fun shouldEnableLocalBroadcast(): Boolean {
        return true
    }

    private fun wechatLogin() {
        val wechatInstall = SysUtils.checkAPK(Constants.PACKAGE_NAME_WECHAT, GlobalApp.getApp())
        LogUtils.d(TAG, "是否安装微信-$wechatInstall,当前包-${GlobalApp.getApp().packageName}")
        if (!wechatInstall) {
            return
        }
        WechatAuthUtil.auth(activity)

    }
}