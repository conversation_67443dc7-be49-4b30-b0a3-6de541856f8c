package howbuy.android.global

/**
 * @Description 网络接口请求 url key
 * <AUTHOR>
 * @Date 2024/2/28
 * @Version V1.0
 */
object UrlKey {

    //公钥接口
    const val TRADE_CGI_GATEWAY_SECURITYKEY = "TRADE_CGI_GATEWAY_SECURITYKEY"
    const val CRM_GATEWAY_GET_APP_PUBLIC_URL_SUFFIX = "/gateway/securitykey.htm"

    /**
     * /hk/v260/adbannerlist.json banner列表
     */
    const val HK_V260_ADBANNERLIST = "HK_V260_ADBANNERLIST"

    /**
     * path: /hk/v200/hkhomepage.json
     */
    const val HK_V200_HKHOMEPAGE = "HK_V200_HKHOMEPAGE"

    /**
     * main/v754/cdn/setting/configparams.json
     */
    const val MAIN_V754_CONFIGPARAMS = "MAIN_V754_CONFIGPARAMS"

    /**
     * /hk/v200/gethklsjzlist.json
     */
    const val HK_V200_GETHKLSJZLIST = "HK_V200_GETHKLSJZLIST"

    /**
     * 灰度开关配置接口
     * /main/grayscale/getFeatureLists.json
     */
    const val HK_MAIN_GRAYSCALE_GET_FEATURE_LISTS = "HK_MAIN_GRAYSCALE_GET_FEATURE_LISTS"

    /**
     * 使用 data.howbuy.com 的域名
     * 安全验证-黑名单配置信息
     * /main/v828/xwyz/gethost.json
     */
    const val HK_MAIN_V828_XWYZ_GETHOST = "HK_MAIN_V828_XWYZ_GETHOST"

    /**
     * 头条搜索-大家都在搜
     * /hk/v260/hotsearch.json
     */
    const val HK_V260_HOTSEARCH = "HK_V260_HOTSEARCH"

    /**
     * 全球头条中配置的 android/ios二维码
     * /hk/v210/newsinfo.json
     */
    const val HK_V210_NEWSINFO = "HK_V210_NEWSINFO"


    /**比较基准*/
    const val HK_V200_GETHKBJJZLIST = "HK_V200_GETHKBJJZLIST"

    /**业绩走势图*/
    const val HK_V200_HKHBCHART = "HK_V200_HKHBCHART"

    /**历史回撤图*/
    const val HK_V250_HKHCCHART = "HK_V250_HKHCCHART"

    /**海外档案页走势图,选择完日期后判断是否展示走势图提示*/
    const val HK_V250_CHARTSHOWFLAG = "HK_V250_CHARTSHOWFLAG"

    /**海外档案页-快速PK产品推荐*/
    const val HK_V250_PKRECOMMENDFUND = "HK_V250_PKRECOMMENDFUND"

    /**快速pk-搜索*/
    const val HK_PK_SEARCHFUND = "HK_PK_SEARCHFUND"

    /**业绩走势图快速PK结果*/
    const val HK_V250_PKCHARTHBCOMPARE = "HK_V250_PKCHARTHBCOMPARE"

    /**动态回撤走势图快速PK结果*/
    const val HK_V250_PKCHARTHCCOMPARE = "HK_V250_PKCHARTHCCOMPARE"

    /**档案页主接口*/
    const val HK_V200_ARCHIVEPAGEINFO = "HK_V200_ARCHIVEPAGEINFO"

    /**档案页-基本信息/交易须知/产品材料*/
    const val HK_V200_FUNDINFO = "HK_V200_FUNDINFO"

    /**海外产品购买状态+平替产品+提示栏接口*/
    const val HK_V200_GETHKBUYSTATUS = "HK_V200_GETHKBUYSTATUS"

    /**私募搜索 */
    const val HK_SEARCHDATA_NEW: String = "HK_SEARCHDATA_NEW"

    /**历史业绩*/
    const val HK_V200_GETHKLSYJLIST = "HK_V200_GETHKLSYJLIST"

    /**海外优选*/
    const val HK_V200_HOTSALES = "HK_V200_HOTSALES"
    const val HK_V254_GMHOTSALES = "HK_V254_GMHOTSALES"

    /**海外公募*/
    const val HK_V250_GMHOTSALES = "HK_V250_GMHOTSALES"

    /**海外资讯*/
    const val HK_V260_NEWS = "HK_V260_NEWS"

    /**专栏详情*/
    const val HK_V210_NEWSCOLUMN = "HK_V210_NEWSCOLUMN"

    /**海外资讯搜索*/
    const val HK_V260_SEARCHNEWS = "HK_V260_SEARCHNEWS"

    /**首页-预约提交*/
    const val HK_USER_YUYUE_COOPERATION_PRECONTRACT = "HK_USER_YUYUE_COOPERATION_PRECONTRACT"

    /**档案页-预约提交*/
    const val HK_USER_YUYUE_PRODUCT_PRECONTRACT = "HK_USER_YUYUE_PRODUCT_PRECONTRACT"

    /**自选-推荐数据*/
    const val HK_V240_FAVORITERECOMMENDPRODUCTLIST = "HK_V240_FAVORITERECOMMENDPRODUCTLIST"
    const val HK_V240_SIMUFAVORITESWITCHOPERATE = "HK_V240_SIMUFAVORITESWITCHOPERATE"
    const val HK_V240_FAVORITECUSTOMGROUPOPERATEBATCH = "HK_V240_FAVORITECUSTOMGROUPOPERATEBATCH"
    const val HK_V240_SIMUFAVORITEGROUPLIST = "HK_V240_SIMUFAVORITEGROUPLIST"
    const val HK_V240_SIMUHOLDJJDMINDEX = "HK_V240_SIMUHOLDJJDMINDEX"
    const val HK_V240_USERSIMUFAVORITESORT = "HK_V240_USERSIMUFAVORITESORT"
    const val HK_V240_SIMUFAVORITECUSTOMGROUPOPERATE = "HK_V240_SIMUFAVORITECUSTOMGROUPOPERATE"
    const val HK_V240_SMFAVORITEFUNDLIST = "HK_V240_SMFAVORITEFUNDLIST"
    const val HK_V240_USERFAVORITEMODIFYBATCH = "HK_V240_USERFAVORITEMODIFYBATCH"
    const val HK_V240_BATCHOPERATEFAVORITEGROUPRELATION = "HK_V240_BATCHOPERATEFAVORITEGROUPRELATION"
    const val HK_V240_BATCHSORTFAVORITEBYGROUPID = "HK_V240_BATCHSORTFAVORITEBYGROUPID"
    const val HK_V240_SIMUFAVBATCHREMOVE = "HK_V240_SIMUFAVBATCHREMOVE"
    const val HK_V240_GETFAVORITELISTBYGROUPID = "HK_V240_GETFAVORITELISTBYGROUPID"
    const val HK_V240_SIMUCCTOTOPALL = "HK_V240_SIMUCCTOTOPALL"

    /**档案页面-预约产品-交易日历*/
    const val HK_V250_QUERYPRODUCTOPENCYCLE = "HK_V250_QUERYPRODUCTOPENCYCLE"

    /**档案页面-非预约产品-交易日历*/
    const val HK_V250_TRADECALENDERLIST = "HK_V250_TRADECALENDERLIST"

    /**全局弹框 /hk/v250/globalpopup.json*/
    const val HK_V250_GLOBALPOPUP = "HK_V250_GLOBALPOPUP"

    /**
     * 档案页面-风险指标
     * /hk/v250/riskindex.json
     */
    const val HK_V250_RISKINDEX = "HK_V250_RISKINDEX"

    /**
     * 档案页-相关资讯
     */
    const val HK_V250_FUNDRELATEDNEWS = "HK_V250_FUNDRELATEDNEWS"

    /**
     * 海外视频
     */
    const val HK_V260_AUDIOVISUAL = "HK_V260_AUDIOVISUAL"

    /**
     * 海外语音播放内容查询
     * /hk/v260/simuvoicecontent.json
     */
    const val HK_V260_SIMUVOICECONTENT: String = "HK_V260_SIMUVOICECONTENT"

    /**
     * 海外资讯/视频/直播 播放记录/浏览记录上报
     * /hk/v260/statisticanalyseenc.json
     */
    const val HK_V260_STATISTICANALYSEENC: String = "HK_V260_STATISTICANALYSEENC"


    /**资讯专栏合集*/
    const val HK_V260_COLUMNLIST = "HK_V260_COLUMNLIST"

    /**
     * 投顾名片编辑信息
     * /hk/v290/tgmp/queryeditinfo.json
     */
    const val HK_V290_TGMP_QUERYEDITINFO = "HK_V290_TGMP_QUERYEDITINFO"

    /**
     * 编辑投顾信息
     * /hk/v290/tgmp/updatetginfo.json
     */
    const val HK_V290_TGMP_UPDATETGINFO = "HK_V290_TGMP_UPDATETGINFO"

    /**
     * 投顾名片头像上传
     * /hk/v290/tgmp/upload.json
     */
    const val HK_V290_TGMP_UPLOAD = "HK_V290_TGMP_UPLOAD"

    /**
     * 微信登录
     * /hkaccount/login/loginByWeChat
     *
     */
    const val CRM_CGI_HKACCOUNT_OHTERLGION_LOGINBYWECHAT =
        "CRM_CGI_HKACCOUNT_OHTERLGION_LOGINBYWECHAT"

    /**
     * app手机号码登录并绑定三app手机号码登录并绑定三方账号----修改接口方账号
     * /hkaccount/login/loginappbymobileandverifycode
     *
     */
    const val CRM_CGI_HKACCOUNT_LOGIN_LOGINAPPBYMOBILEANDVERIFYCODE =
        "CRM_CGI_HKACCOUNT_LOGIN_LOGINAPPBYMOBILEANDVERIFYCODE"



    /*******************************vhall直播**************************************/
    ///hk/v260/livetransferinfo.json 私募直播前置中转页数据接口
    const val HK_V260_LIVETRANSFERINFO = "HK_V260_LIVETRANSFERINFO"

    ///hk/v260/simuliveposterinfo.json 私募视频直播分享海报信息接口
    const val HK_V260_SIMULIVEPOSTERINFO = "HK_V260_SIMULIVEPOSTERINFO"

    ///hk/v260/getvhalllivedetail.json 根据微吼活动id查询直播实时状态接口-10s轮训
    const val HK_V260_GETVHALLLIVEDETAIL = "HK_V260_GETVHALLLIVEDETAIL"

    ///hk/v260/liveremindswitch.json 直播预约短信提醒开关接口
    const val HK_V260_LIVEREMINDSWITCH = "HK_V260_LIVEREMINDSWITCH"

    ///hk/v260/addliveappoint.json 直播预约接口
    const val HK_V260_ADDLIVEAPPOINT = "HK_V260_ADDLIVEAPPOINT"

    ///hk/v260/livepwdcheck.json 私募直播密码校验接口
    const val HK_V260_LIVEPWDCHECK = "HK_V260_LIVEPWDCHECK"

    ///hk/v260/vhalllivedetail.json 私募直播详情介绍区
    const val HK_V260_VHALLLIVEDETAIL = "HK_V260_VHALLLIVEDETAIL"

    ///hk/v260/discussionlist.json 全部评论列表
    const val HK_V210_DISCUSSIONLIST = "HK_V210_DISCUSSIONLIST"

    ///hk/v260/discussionadd.json 增加评论
    const val HK_V260_DISCUSSIONADDENC = "HK_V260_DISCUSSIONADDENC"

    ///hk/v260/smvaudioplayrecordadd.json 音视频播放时长添加
    const val HK_V260_SMAUDIOPLAYRECORDADD = "HK_V260_SMAUDIOPLAYRECORDADD"
    /*******************************vhall直播**************************************/

    /*******************************持牌人**************************************/
    ///hk/v290/tgmp/maininfo.json-投顾名片信息查询
    const val HK_V290_TGMP_MAININFO = "HK_V290_TGMP_MAININFO"

    ///hk/v290/tgmp/getrelationcontent.json 投顾名片关联内容查询
    const val HK_V290_TGMP_GETRELATIONCONTENT = "HK_V290_TGMP_GETRELATIONCONTENT"

    ///hk/v290/tgmp/updatemoduleopenflag.json 更新投顾名片模块开关状态
    const val HK_V290_TGMP_UPDATEMODULEOPENFLAG = "HK_V290_TGMP_UPDATEMODULEOPENFLAG"

    ///hk/v290/tgmp/updatemodulesort.json 更新投顾名片模块顺序
    const val HK_V290_TGMP_UPDATEMODULESORT = "HK_V290_TGMP_UPDATEMODULESORT"

    ///hk/v290/tgmp/updatestyletype.json 投顾名片风格数据提交
    const val HK_V290_TGMP_UPDATESTYLETYPE = "HK_V290_TGMP_UPDATESTYLETYPE"

    ///hk/v290/tgmp/deletemodulecontent.json 一键清空推荐产品或内容数据接口
    const val HK_V290_TGMP_DELETEMODULECONTENT = "HK_V290_TGMP_DELETEMODULECONTENT"

    ///hk/v290/tgmp/quickaddcontent.json 快捷添加热点或产品信息接口
    const val HK_V290_TGMP_QUICKADDCONTENT = "HK_V290_TGMP_QUICKADDCONTENT"

    ///hk/v290/tgmp/addordeletecontent.json 单条内容添加删除接口
    const val HK_V290_TGMP_ADDORDELETECONTENT = "HK_V290_TGMP_ADDORDELETECONTENT"

    ///hk/v290/tgmp/statisticenc.json 投顾名片模板点赞
    const val HK_V290_TGMP_STATISTICENC = "HK_V290_TGMP_STATISTICENC"
    /*******************************持牌人**************************************/

}