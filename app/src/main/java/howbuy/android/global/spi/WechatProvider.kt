package howbuy.android.global.spi

import android.app.Activity
import android.content.Context
import android.text.TextUtils
import androidx.core.util.Consumer
import androidx.fragment.app.Fragment
import com.alibaba.android.arouter.facade.annotation.Route
import com.howbuy.account.UserDataHelper
import com.howbuy.fund.base.IWechatProvider
import com.howbuy.fund.base.config.SpConfig
import com.howbuy.fund.base.mvp.rx.RxDisposableObserverEN
import com.howbuy.fund.base.storage.CommonStorageUtils
import com.howbuy.fund.net.entity.http.ReqNetOpt
import com.howbuy.fund.net.entity.http.ReqResult
import com.howbuy.global.data_api.DataIds
import com.howbuy.global.user.entity.FollowWeChatBean
import com.howbuy.lib.compont.GlobalApp
import com.howbuy.lib.utils.SysUtils
import howbuy.android.global.businesscard.dialog.WechatReBindConfirmDialog
import howbuy.android.global.request.GlobalRequest
import com.howbuy.global.login_impl.wechatlogin.AuthListener
import com.howbuy.global.login_impl.wechatlogin.WechatAuthUtil

/**
 * author: gen.li
 * date  : 2025/4/28
 * desc  :
 */
@Route(path = "/global/main/WechatProvider")
class WechatProvider : IWechatProvider {

    override fun wechatInstalled(): Boolean {
        val wechatInstall = SysUtils.checkAPK("com.tencent.mm", GlobalApp.getApp())
        return wechatInstall
    }

    override fun wechatEnable(): Boolean {
        val result: Boolean
        val wechatInstall = wechatInstalled()
        val wechatConfig = CommonStorageUtils.getString(SpConfig.CMS_THIRD_LOGIN_SWITCH, "0")
        //0是关,其他开`
        val wechatShow = wechatConfig != "0"
        result = wechatInstall && wechatShow
        return result
    }

    override fun showBindDialog(
        content: CharSequence,
        bind: Boolean,
        fragment: Fragment?,
        consumer: Consumer<Boolean>?
    ) {
        fragment?.host?.let {
            val wechatReBindConfirmDialog = WechatReBindConfirmDialog.newInstance(content, bind)
            wechatReBindConfirmDialog.show(
                fragment.childFragmentManager,
                "WechatReBindConfirmDialog"
            )
            wechatReBindConfirmDialog.setOnConfirmListener(object :
                WechatReBindConfirmDialog.OnConfirmListener {
                override fun onConfirm() {
                    consumer?.accept(true)
                }

                override fun onCancel() {
                    consumer?.accept(false)
                }
            })
        }
    }

    override fun deleteAuth(activity: Activity?, consumer: Consumer<Boolean>?) {
        WechatAuthUtil.deleteAuth(activity, object : AuthListener<MutableMap<String, String>?> {
            override fun onSuccess(data: MutableMap<String, String>?) {
                consumer?.accept(true)
            }

            override fun onFailed() {
                consumer?.accept(false)
            }

            override fun onCancelled() {
                consumer?.accept(false)
            }
        })
    }

    override fun init(context: Context?) {

    }

    override fun wechatBindState(): Boolean {
        return !TextUtils.isEmpty(getObj()?.bindWeChatUnionId)
    }

    override fun getUnionId(): String? {
        return getObj()?.bindWeChatUnionId
    }

    override fun wechatLogin(activity: Activity?, consumer: Consumer<ReqResult<ReqNetOpt>?>) {
        val executeWeChatLoginTask = Consumer<MutableMap<String, String>?> { authMapData ->
            if (authMapData == null) {
                //auth map 不存在
                consumer.accept(null)
                return@Consumer
            }
            val unionId = authMapData["unionid"]
            if (unionId.isNullOrEmpty()) {
                //union id 不存在
                consumer.accept(null)
                return@Consumer
            }
            GlobalRequest.wechatLogin(unionId)
                .subscribe(object : RxDisposableObserverEN<ReqResult<ReqNetOpt>?>() {
                    override fun onNext(t: ReqResult<ReqNetOpt>) {
                        //val wechatLoginResult = t.mData as? WeChatLoginResult
                        consumer.accept(t)
                    }

                    override fun onError(e: Throwable) {
                        super.onError(e)
                        consumer.accept(null)
                    }
                })

        }
        WechatAuthUtil.auth(activity, object : AuthListener<MutableMap<String, String>?> {
            override fun onSuccess(data: MutableMap<String, String>?) {
                executeWeChatLoginTask.accept(data)
            }

            override fun onFailed() {
                executeWeChatLoginTask.accept(null)
            }

            override fun onCancelled() {
                executeWeChatLoginTask.accept(null)
            }
        })
    }

    private fun getObj(): FollowWeChatBean? {
        return UserDataHelper.getDataManager(DataIds.ID_USER_WECHAT_BIND)
            .getSync<FollowWeChatBean>().data
    }
}