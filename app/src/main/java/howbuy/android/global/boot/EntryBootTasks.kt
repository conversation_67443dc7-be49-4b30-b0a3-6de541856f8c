package howbuy.android.global.boot

import android.content.Context
import android.text.TextUtils
import androidx.fragment.app.Fragment
import com.howbuy.account.UserDataHelper
import com.howbuy.account.remote.FetchAction
import com.howbuy.analytics.common.DevicesId
import com.howbuy.fund.base.aty.AtyEmpty
import com.howbuy.fund.base.config.ApkConfig
import com.howbuy.fund.base.config.SpConfig
import com.howbuy.fund.base.perttask.PertTaskManager.Companion.getInstance
import com.howbuy.fund.base.perttask.task.Project
import com.howbuy.fund.base.perttask.task.Task
import com.howbuy.fund.base.perttask.task.lock.LockableAnchor.LockListener
import com.howbuy.fund.base.router.AtyRouterPath
import com.howbuy.fund.base.router.RouterHelper
import com.howbuy.fund.base.storage.CommonStorageUtils
import com.howbuy.fund.logupload.CrashHandler
import com.howbuy.fund.logupload.guard.GuardVisitorImpl
import com.howbuy.fund.net.base.RetrofitHelper
import com.howbuy.gesture.model.ForeObserver
import com.howbuy.gesture.model.ForeObservers
import com.howbuy.global.data_api.DataIds
import com.howbuy.global.data_api.apiUserInfo
import com.howbuy.global.login_api.ILoginProvider
import com.howbuy.h5.H5FileHelper
import com.howbuy.h5.h5config.Configs
import com.howbuy.lib.aty.AtyMgr
import com.howbuy.lib.compont.GlobalApp
import com.howbuy.lib.compont.Receiver
import com.howbuy.lib.interfaces.IShareProvider
import com.howbuy.lib.utils.LogUtils
import com.howbuy.lib.utils.SysUtils
import com.howbuy.router.proxy.Invoker
import com.umeng.umcrash.UMCrash
import howbuy.android.global.cgiurl.UpdateCgiUrlsService
import howbuy.android.global.entry.EntryActivity
import howbuy.android.global.entry.FragUserPrivateDialog
import howbuy.android.global.optional.SmOptionalUtils
import howbuy.android.global.utils.CmsMgr
import howbuy.android.global.utils.CommonInfoMgr
import io.reactivex.Observable
import io.reactivex.schedulers.Schedulers

/**
 * class description.
 * AtyEntry页面的启动任务
 * <AUTHOR>
 * @date 2022/4/26
 */
class EntryBootTasks(val activity: EntryActivity?) {


    //是否已同意私聊合规
    var hasAgreenSecurity: Boolean = false

    //停机公告--海外暂无
    var isServiceAnnounce: Boolean = false

    //是否会跳转到广告页面
    var canGotoSplashPager: Boolean = false

    //是否强制升级---灰度控制--海外暂无
    var mForceUpdateApp: Boolean = false

    /**
     * AtyEntry中的启动任务链
     */
    fun startEntryBootTask() {
        val anchorsManager = getInstance()
        anchorsManager.debuggable(ApkConfig.getDebugLog())
        val builder = Project.Builder("atyentry", null)
        //创建Task实例
        val updateDeviceId = UpdateDeviceId()
        val handlePrivacyAuth = HandlePrivacyAuth()
        val uploadCrashLog = UploadCrashLog()
        val initSingleUserReportLoop = InitSingleUserReportLoop()
        val initUmeng = InitUmeng()
        val initTxPush = InitTxPush()
        val entryCreate = EntryCreate()
        val initEntryUI = InitEntryUI()
        val waitAppInitEnd = WaitAppInitEnd()
        val checkAppUpgrade = CheckAppUpgrade()
        val launchSplashOrMain = LaunchSplashOrMain()
//        val requestDNS = RequestDNS()
        val fetchEmergencyHosts = FetchEmergencyHosts()
        val reqUserInfo = ReqUserInfo()
        val reqUserMessage = ReqUserMessage()
        val updateCgiUrls = UpdateCgiUrls()
        val syncOptionData = SyncOptionData()
        val fetchForgroundRequestCms = FetchForgroundRequestCms()
        val fetchForgroundRequestGray = FetchForgroundRequestGray()
        val initFingerGestureLock = InitFingerGestureLock()
        val toggleLocalBroadcast = ToggleLocalBroadcast()
        val toggleNetChangeReceiver = ToggleNetChangeReceiver()
        val loadLocalUserInfo = LoadLocalUserInfo()
        val installH5ResPkgNotForce = InstallH5ResPkgNotForce()
        //添加Task任务链
        builder.add(updateDeviceId).dependOn(handlePrivacyAuth)
        //网络请求中,有很多与用户信息相关
        builder.add(uploadCrashLog).dependOn(updateDeviceId)
        builder.add(initSingleUserReportLoop).dependOn(updateDeviceId)
        builder.add(initUmeng).dependOn(handlePrivacyAuth)
        builder.add(initTxPush).dependOn(handlePrivacyAuth)
        builder.add(entryCreate)
        builder.add(initEntryUI).dependOn(entryCreate)
        builder.add(initFingerGestureLock).dependOn(initEntryUI)
        builder.add(toggleLocalBroadcast).dependOn(initEntryUI)
        builder.add(toggleNetChangeReceiver).dependOn(initEntryUI)
        builder.add(loadLocalUserInfo).dependOn(entryCreate)
        builder.add(waitAppInitEnd).dependOn(initEntryUI)
        builder.add(handlePrivacyAuth).dependOn(waitAppInitEnd, loadLocalUserInfo)
        builder.add(checkAppUpgrade).dependOn(handlePrivacyAuth)
        builder.add(launchSplashOrMain).dependOn(checkAppUpgrade)
//        builder.add(requestDNS).dependOn(updateDeviceId)
        builder.add(fetchEmergencyHosts).dependOn(updateDeviceId)
        builder.add(reqUserInfo).dependOn(updateDeviceId)
        builder.add(reqUserMessage).dependOn(updateDeviceId)
        builder.add(updateCgiUrls).dependOn(updateDeviceId)
        builder.add(installH5ResPkgNotForce)
        builder.add(fetchForgroundRequestCms).dependOn(updateDeviceId)
        builder.add(syncOptionData).dependOn(updateDeviceId)
        builder.add(fetchForgroundRequestGray).dependOn(updateDeviceId)
        //添加隐私弹框的锚点
        val securityAnchor = anchorsManager.requestBlockWhenFinish(handlePrivacyAuth)
        securityAnchor.setLockListener(object : LockListener {
            override fun lockUp() {
                if (!hasAgreenSecurity) {
                    activity?.supportFragmentManager?.let {
                        LogUtils.d("Guard-AtyEntry", "checkUserSecurity, show privacy dialog")
                        val dialog = FragUserPrivateDialog()
                        dialog.setAction {
                            //继续执行后续任务: smash()是终断当前流程
                            securityAnchor.unlock()
                            GuardVisitorImpl.getInstance().setLogEnable()
                        }
                        dialog.show(it, null)
                    } ?: securityAnchor.unlock()
                } else {
                    securityAnchor.unlock()
                }
            }
        })
        //启动页面进入停机公告页面(如果是Lock导致的,这个节点不会影响其它节点执行[没有任何依赖])
        val announceAnchor = anchorsManager.requestBlockWhenFinish(fetchEmergencyHosts)
        announceAnchor.setLockListener(object : LockListener {
            override fun lockUp() {
                announceAnchor.unlock()
            }
        })
        //启动任务链
        anchorsManager.start(builder.build())
    }

    /**
     * 把它放在AtyEntry中的启动任务中执行了,并且在同意了隐私弹框之后执行
     * 更新网络参数-deviceId的值(网络请求公共参数字段)
     */
    class UpdateDeviceId : Task(UpdateDeviceId::class.java.name) {
        override fun run(name: String) {
            //能执行到这个task, 说明隐私声明用户已经同意了
            var deviceId = ApkConfig.getDeviceId(GlobalApp.getApp())
            if (deviceId != null && deviceId.length > 64) {
//                val sf: SharedPreferences = GlobalApp.getApp().getSharedPreferences(DevicesId.SF_ROOT_NAME, Context.MODE_PRIVATE)
                DevicesId.clearDeviceIdSp(GlobalApp.getApp())
                deviceId = ApkConfig.getDeviceId(GlobalApp.getApp())
            }
            RetrofitHelper.getInstance().httpConfig?.publicParams?.put("deviceId", deviceId)
            DevicesId.setIDeviceValid { oldDeviceId, newDeviceId ->
                // 不合法的设备id监控
                if (oldDeviceId.isNullOrEmpty()) {
                    return@setIDeviceValid
                }
//                get(ElkReport::class.java).reportAction(
//                    "business_trace",
//                    "不合法的设备ID",
//                    "old=$oldDeviceId 替换之后的-$newDeviceId",
//                    false,
//                    true
//                )

            }
//            CertUtils.initXaGuoMiSdk();
        }
    }

    /**
     * 孤岛任务
     * 进入AtyEntry 第一个任务(暂无)
     */
    inner class EntryCreate : Task(EntryCreate::class.java.name) {
        override fun run(name: String) {

        }
    }

    /**
     * 依赖: [EntryCreate]
     * 进入AtyEntry UI相关处理(暂无)
     */
    inner class InitEntryUI : Task(InitEntryUI::class.java.name) {
        override fun run(name: String) {
        }
    }

    /**
     * 依赖: [InitEntryUI]
     * 进入AtyEntry后,必要参数初始完成
     */
    inner class WaitAppInitEnd : Task(WaitAppInitEnd::class.java.name) {
        override fun run(name: String) {
            activity?.initLauncherTypeWithIntent()
            //记录app启动次数(不能放在AppFrame中的全局生命周期中,因为前后台切换,AtyTabMain不一定执行)
//            var startTimes = CommonStorageUtils.getInt(SpConfig.SF_APP_START_TIMES, 0)
//            if (startTimes <= 5) {
//                startTimes++
//                //app第5次打开,弹出通知打开提示,只需要记录到第6次就可以
//                CommonStorageUtils.putInt(SpConfig.SF_APP_START_TIMES, startTimes)
//            }
        }
    }

    /**
     * 依赖: [InitEntryUI]
     * 注册事件/消息广播
     */
    class ToggleLocalBroadcast : Task(ToggleLocalBroadcast::class.java.name) {
        override fun run(name: String) {
            Receiver.instance(null).toggleLocalBroadcast(true)
        }
    }

    /**
     * 依赖: [InitEntryUI]
     * 注册网络广播
     * 注意:android 14上,会抛出:
     * java.lang.SecurityException: howbuy.android.global: One of RECEIVER_EXPORTED or RECEIVER_NOT_EXPORTED
     * should be specified when a receiver isn't being registered exclusively for system broadcasts
     */
    class ToggleNetChangeReceiver : Task(ToggleNetChangeReceiver::class.java.name) {
        override fun run(name: String) {
            Receiver.instance(null).toggleNetReceiver(true)
        }
    }

    /**
     * 依赖: [WaitAppInitEnd], [LoadLocalUserInfo]
     * 用户隐私合规
     */
    inner class HandlePrivacyAuth : Task(HandlePrivacyAuth::class.java.name) {
        override fun run(name: String) {
            hasAgreenSecurity =
                CommonStorageUtils.getBoolean(SpConfig.SF_TYPE_USER_PRIVATE_SHOWED, false)
        }
    }

    /**
     * 依赖:[HandlePrivacyAuth]
     **/
    inner class CheckAppUpgrade : Task(CheckAppUpgrade::class.java.name) {
        override fun run(name: String) {
            //App更新页面(拦截进入主页面[主页面进入就崩溃容错处理])[待实现]
//            mForceUpdateApp = CommonStorageUtils.getBoolean(DownloadService.SF_FORCE_APP_UPDATE, false)
//            //如果需要强制更新app,就进入更新页面,不能进入主页面,更新app成功后
//            if (mForceUpdateApp) {
//                if (activity == null || activity.isFinishing) return
//                RouterHelper.launchFrag(activity, AtyRouterPath.PATH_FORCE_UPGRADE, null)
//            }
        }
    }


    /**
     * 依赖: [CheckAppUpgrade]
     * todo
     * 跳转到FragSplash或者AtyTbMain
     */
    inner class LaunchSplashOrMain : Task(LaunchSplashOrMain::class.java.name) {
        override fun run(name: String) {
            //不需要升级,直接跳转进入splashd页面/主页面
//            if (!mForceUpdateApp) {
            canGotoSplashPager = activity?.canGotoSplashPage() ?: false
            //跳转到FragSplash页面后,倒计时完成,自动跳转到首页
            if (canGotoSplashPager) {
                try {
                    activity?.addFragment()
                } catch (e: Exception) {
                    e.printStackTrace()
                    //异常情况: 挂载fragment异常时,直接进入首页
                    gotoMain()
                }
            } else {
                //不需要进入广告Splash页面,直接跳转进入main
                gotoMain()
            }
//            }
        }
    }

    /**
     * 跳转到主页面
     */
    private fun gotoMain() {
        if (isServiceAnnounce) {
            return
        }
        activity?.intentMain()
    }

    /**
     * 依赖: [InitEntryUI]
     * 用户设置的指纹解锁/手势解锁
     */
    inner class InitFingerGestureLock : Task(InitFingerGestureLock::class.java.name) {
        override fun run(name: String) {
            if (activity?.hasAtyMain() != true) {
                ForeObservers.initWithFlag(ForeObserver.FORE_LEAVE_FOREBACKGROUND, 0)
            }
        }
    }

    /**
     * 依赖: [[AppBootTasks.InitHttp]
     * 获取是否配置了停机公告相关内容,阻止用户使用app
     */
    inner class FetchEmergencyHosts : Task(FetchEmergencyHosts::class.java.name, true) {
        override fun run(name: String) {

        }
    }

    /**
     * 依赖: [EntryCreate]
     * 用户信息
     * 防止每次都从sf读取用户登录信息,可以先从内存对象中获取,效率更高一些
     */
    class LoadLocalUserInfo : Task(LoadLocalUserInfo::class.java.name, true) {
        override fun run(name: String) {
            //优先从内存对象中获取 初始化用户登录信息,再从sf文件中获取
            val loginInfo =
                Invoker.getInstance().navigation(ILoginProvider::class.java).getLoginInfo(true)
            if (loginInfo == null || TextUtils.isEmpty(loginInfo.hkCustNo)) {
                //杀进程进入或者无客户号时,需要从sf文件中读取用户信息(也可以直接调用false参数)
                Invoker.getInstance().navigation(ILoginProvider::class.java).getLoginInfo(false)
            }
        }
    }

    /**
     * 依赖: [[AppBootTasks.InitHttp]
     * 请求用户信息
     */
    class ReqUserInfo : Task(ReqUserInfo::class.java.name, true) {
        override fun run(name: String) {
            if (apiUserInfo().isLogined()) {
                //请求用户信息
                UserDataHelper.scheduleRemoteFetch(DataIds.ID_USER_INFO, FetchAction.boot)
                UserDataHelper.scheduleRemoteFetch(DataIds.ID_USER_HOLD, FetchAction.boot)
            }
        }
    }

    /**
     * 依赖: [[AppBootTasks.InitHttp]
     * 请求消息中心
     */
    class ReqUserMessage : Task(ReqUserMessage::class.java.name, true) {
        override fun run(name: String) {
            if (apiUserInfo().isLogined()) {
                UserDataHelper.scheduleRemoteFetch(DataIds.ID_USER_MESSAGE, FetchAction.boot)
            }
        }
    }

    /**
     * 依赖: [[AppBootTasks.InitHttp]
     * 更新cgi_urls文件内容
     */
    class UpdateCgiUrls : Task(UpdateCgiUrls::class.java.name, true) {
        override fun run(name: String) {
            UpdateCgiUrlsService.getIntance().reqUpdateUrls(false, true, null)
        }
    }

    /**
     * 依赖: [AppBootTasks.InitHttp]
     * 上报app 的crash相关异常数据信息
     */
    class UploadCrashLog : Task(UploadCrashLog::class.java.name, true) {
        override fun run(name: String) {
            //网络上报逻辑，在这里执行
            CrashHandler.getInstance().uploadCrashLog()
        }
    }

    /**
     * 依赖: [AppBootTasks.InitHttp]
     * 单用户日志上传(与用户是否登录不强相关,主要是上报用户信息,手机信息,操作信息等)
     */
    class InitSingleUserReportLoop : Task(InitSingleUserReportLoop::class.java.name, true) {
        override fun run(name: String) {

        }
    }

    /**
     * 依赖: [AppBootTasks.InitHttp]
     * 同步用户自选数据
     */
    class SyncOptionData : Task(SyncOptionData::class.java.name, true) {
        override fun run(name: String) {
            if (apiUserInfo().isLogined()) {
                LogUtils.d("EntryBoot", "SyncOptionData 步同自选数据到db中")
                SmOptionalUtils.requestSyncSmOptData()
            }
        }
    }

    /**
     * 依赖: [AppBootTasks.InitHttp]
     * 从CMS中获取配置数据
     */
    inner class FetchForgroundRequestCms : Task(FetchForgroundRequestCms::class.java.name, true) {
        override fun run(name: String) {
            CmsMgr.reqCmsConfigData()
            CmsMgr.reqCmsQrCodeData()
            CommonInfoMgr.reqLoginExpiredStatus()
        }
    }

    /**
     * 依赖: [AppBootTasks.InitHttp]
     *  读取功能灰度列表
     *
     */
    inner class FetchForgroundRequestGray : Task(FetchForgroundRequestGray::class.java.name, true) {
        override fun run(name: String) {
            CommonInfoMgr.requestAppGrayFunctionList()
            CommonInfoMgr.requestBlackIpConfig()
        }
    }

    /**
     * 依赖: [HandlePrivacyAuth]
     * 初始化umeng sdk, 这里涉及隐私合规项
     */
    class InitUmeng : Task(InitUmeng::class.java.name) {
        override fun run(name: String) {
            umengAnalyticsNShare()
        }

        /**
         * 1. 在Applicaiton.onCreate函数中调用预初始化函数UMConfigure.preInit()，预初始化函数不会采集设备信息，也不会向友盟后台上报数据。
         *
         *
         * 2. 确保App首次冷启动时，在用户阅读您的《隐私政策》并取得用户授权之后，才调用正式初始化函数UMConfigure.init()初始化统计SDK，
         * 此时SDK才会真正采集设备信息并上报数据。反之，如果用户不同意《隐私政策》授权，则不能调用UMConfigure.init()初始化函数。
         * 请注意调用正式初始化函数UMConfigure.init()之前，不要调用UMShareAPI接口类的任何API方法。
         */
        private fun umengAnalyticsNShare() {
//            if (TextUtils.equals("debug", ApkConfig.getBuildType())) {
//                return
//            }
            val alreadyPrivateShowed =
                CommonStorageUtils.getBoolean(SpConfig.SF_TYPE_USER_PRIVATE_SHOWED, false)
            //手动配置了不初始化umeng，则直接返回
            val umengApmOff = ApkConfig.getUmengApmOff()
            LogUtils.d("DebugUmApmOff", "umengApmOff:$umengApmOff")
            if (alreadyPrivateShowed && !umengApmOff) {
                val shareProvider = Invoker.getInstance().navigation(IShareProvider::class.java)
                if (shareProvider != null) {
                    shareProvider.initUmengSocial(
                        GlobalApp.getApp(),
                        SysUtils.getMetaData(GlobalApp.getApp())["UMENG_APPKEY"].toString() + "",
                        ApkConfig.getChannelName()
                    )
                    shareProvider.initConfig(
                        ApkConfig.getWXKey(),
                        ApkConfig.getWWKey(),
                        GlobalApp.getApp().packageName
                    )
                }
                //将异常发生的Fragment上报
                UMCrash.registerUMCrashCallback {
                    val atyStack = AtyMgr.getAtys()
                    if (atyStack.isEmpty()) return@registerUMCrashCallback null
                    val activity = AtyMgr.getAtys().peek()
                    if (activity is AtyEmpty) {
                        val fragment: Fragment? = activity.currentFragment
                        if (fragment != null) {
                            return@registerUMCrashCallback "fragment:" + fragment.javaClass.name
                        }
                    }
                    null
                }
            }
        }
    }

    /**
     * 依赖: [HandlePrivacyAuth]
     */
    class InitTxPush : Task(InitTxPush::class.java.name) {
        override fun run(name: String) {
//            PushHelper.init(GlobalApp.getApp())
        }
    }

}

/**
 * H5资源包安装(非强制)
 */
class InstallH5ResPkgNotForce : Task(InstallH5ResPkgNotForce::class.java.name, true) {
    override fun run(name: String) {
        installH5NotForcePkg(inWorkThread = true)
    }
}

/**
 * 启动页，执行H5资源包非强制更新安装.内部在子线程中执行具体的逻辑
 * 1. 灰度开关"启动页执行H5非强制更新"打开，那么执行
 * 2. 开关关闭，则不执行
 */
fun installH5NotForcePkg(inWorkThread: Boolean = false) {
    var observable = Observable.create<Boolean> {
        // 判断：启动页执行H5非强制更新安装开关是否打开
        val graySwitchOn: Boolean =
            CommonStorageUtils.getBoolean(SpConfig.INSTALL_H5_PKG_IN_ENTRY, false)
        LogUtils.d(
            "Debug-h5",
            "installH5NotForcePkg-entry, start in thread id:" + (Thread.currentThread().id)
        )
        //仅在开关打开的状态下才执行安装
        if (graySwitchOn) {
            it.onNext(true)
        }
        it.onComplete()
    }
    //如果当前是子线程，无需再次切换线程，直接在当前线程中同步执行整个流程；否则切换线程执行。
    if (!inWorkThread) {
        observable = observable.subscribeOn(Schedulers.io())
    }
    observable.flatMap {
        val hasUnzipSuc =
            !H5FileHelper.getInstance().getsF().getBoolean(Configs.SF_ZNZIP_SUCCESS, false)
        //如果之前有未安装成功的资源包，清除下载好的非强制更新包
        //防止安装包种的版本号----->非强制更新的安装包之间有多个增量更新版本被跳过，本次安装成功后，导致被跳过的版本对应的资源文件找不到
        LogUtils.d("Debug-h5", "installH5NotForcePkg, in entry, hasUnzipSuc:$hasUnzipSuc")
        if (hasUnzipSuc) {
            //如果之前有未安装成功的资源包，清除下载好的非强制更新包
            LogUtils.d("Debug-h5", "installH5NotForcePkg, in entry, clear Uninstall H5 pkg")
//            Invoker.getInstance().navigation(IMainProvider::class.java).clearNotForceUpdatePkg()
        } else {
            LogUtils.d("Debug-h5", "installH5NotForcePkg, in entry, installNotForceUpdateH5Pkg")
//            Invoker.getInstance().navigation(IMainProvider::class.java).installNotForceUpdateH5Pkg(true)
        }
        Observable.just(true)
    }
        .subscribe()
}