package howbuy.android.global.search

import android.annotation.SuppressLint
import android.graphics.Rect
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.widget.TextView
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.howbuy.analytics.PvReportUtils
import com.howbuy.analytics.entity.HBAnalyticsBean
import com.howbuy.android.analytics.annotation.pv.PvInfo
import com.howbuy.fund.base.analytics.HbAnalytics
import com.howbuy.fund.base.arch.ShareViewModelProvider
import com.howbuy.fund.base.arch.ViewModelHelper
import com.howbuy.fund.base.frag.AbsHbFrag
import com.howbuy.fund.base.utils.FundTextUtils
import com.howbuy.hbrefresh.layout.SmartRefreshLayout
import com.howbuy.hbrefresh.layout.api.RefreshLayout
import com.howbuy.hbrefresh.layout.listener.OnRefreshLoadmoreListener
import com.howbuy.lib.utils.DensityUtils
import howbuy.android.global.R
import howbuy.android.global.businesscard.VmBusinessCardProducts
import howbuy.android.global.utils.LauncherFundDetailsMgr
import howbuy.android.global.widgets.HbLoadMoreFooter

/**
 *@desc 搜索结果页
 *<AUTHOR>
 *@date 2024/03/20
 **/
@SuppressLint("SetTextI18n")
class FragSearchResult : AbsHbFrag() {

    private var refreshView: SmartRefreshLayout? = null
    private var loadMoreFooter: HbLoadMoreFooter? = null
    private var rvList: RecyclerView? = null
    private var tvSearchResultCount: TextView? = null
    private var tvEmpty: TextView? = null
    private val searchVm by lazy {
        ViewModelHelper.createViewModel(activity, GlobalSearchViewModel::class.java)
    }

    // 是否处于编辑模式
    private var isEditMode = false

    // 持牌人名片产品ViewModel
    private val vmProducts by lazy {
        ShareViewModelProvider.get(this, VmBusinessCardProducts::class.java)
    }

    private val adpSearchResult by lazy {
        AdpSearchResult(isEditMode) { item ->
            if (item.hasAdd) {
                vmProducts.removeProduct(item.tscode)
            } else {
                HbAnalytics.onClick("613840")
                vmProducts.addProduct(item.tscode)
            }
        }
    }

    override fun getFragLayoutId(): Int {
        return R.layout.frag_search_result
    }

    override fun stepAllViews(root: View?, savedInstanceState: Bundle?) {
        isEditMode = (parentFragment as? FragGlobalSearch)?.isEditMode() == true
        refreshView = root?.findViewById(R.id.smart_refresh)
        loadMoreFooter = root?.findViewById(R.id.footer_search)
        rvList = root?.findViewById(R.id.rv_list)
        tvSearchResultCount = root?.findViewById(R.id.tv_search_count)
        tvEmpty = root?.findViewById(R.id.tv_empty)

        refreshView?.isEnableRefresh = true
        refreshView?.isEnableLoadmore = true
        refreshView?.setDisableContentWhenRefresh(true)
        refreshView?.setDisableContentWhenLoading(true)
        refreshView?.setEnableLoadmoreWhenContentNotFull(true)

        refreshView?.setOnRefreshLoadmoreListener(object : OnRefreshLoadmoreListener {
            override fun onRefresh(refreshlayout: RefreshLayout?) {
                searchVm.searchPage = 1
                searchVm.requestSearchData()

            }

            override fun onLoadmore(refreshlayout: RefreshLayout?) {
                searchVm.requestSearchData(true)
            }
        })

        rvList?.layoutManager = LinearLayoutManager(context)
        rvList?.adapter = adpSearchResult
        rvList?.addItemDecoration(object : RecyclerView.ItemDecoration() {
            override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
                val childLayoutPosition = parent.getChildLayoutPosition(view)
                if (childLayoutPosition != 0) {
                    outRect.top = DensityUtils.dp2px(10F)
                }
            }
        })
        adpSearchResult.setOnItemClickListener { _, _, position ->
            //1.点击跳转档案页
            //2.点击记录浏览历史
            val hkSearchFundContent = adpSearchResult.getItemOrNull(position)
            launchArchivePage(hkSearchFundContent)
            recordClickHistory(hkSearchFundContent)
        }


    }

    private fun launchArchivePage(hkSearchFundContent: HkSearchFundContent?) {
        val fundCode = hkSearchFundContent?.tscode.orEmpty()
        LauncherFundDetailsMgr.launcherFundDetails(context, fundCode, TextUtils.equals("1", hkSearchFundContent?.piType))
    }

    private fun recordClickHistory(hkSearchFundContent: HkSearchFundContent?) {
        searchVm.recordSearchHistory(hkSearchFundContent)
    }

    override fun parseArgment(arg: Bundle?) {
        observerLiveData()
        pvAnalysis()
    }

    private fun observerLiveData() {
        observeLoadingState()
        searchVm.searchResultData.observe(this) {
            refreshView?.finishLoadmore(0)
            refreshView?.finishRefresh(0)
            adpSearchResult.searchKey = searchVm.requestSearchKey
            val emptyResult = it?.hkFundContent.isNullOrEmpty() && searchVm.searchPage == 1
            setViewState(emptyResult)
            if (emptyResult) {
                tvSearchResultCount?.visibility = View.GONE
                refreshView?.isEnableLoadmore = false
                loadMoreFooter?.setViewFooterState(activity, adpSearchResult, "", false)
            } else {
                if (searchVm.searchPage == 1) {
                    adpSearchResult.setList(it?.hkFundContent)
                } else {
                    adpSearchResult.addData(it?.hkFundContent ?: emptyList())
                }

                //是否有更多
                val hasMore = it?.hkFundContentSfgd == "1"
                refreshView?.isEnableLoadmore = hasMore
                loadMoreFooter?.setViewFooterState(activity, adpSearchResult, "", !hasMore)
                tvSearchResultCount?.visibility = View.VISIBLE
                tvSearchResultCount?.text = "共${FundTextUtils.showTextEmpty(it?.hkFundContentTotalCount)}条搜索结果"
                handleAddList()
            }

        }

        // 如果处于编辑模式，观察产品列表变化
        if (isEditMode) {
            vmProducts.productList.observe(this, Observer { products ->
                handleAddList()
            })
            vmProducts.loading.observe(this, Observer {
                if (isVisible && isResumed) {
                    if (it) {
                        showAlermDlg("加载中...", false, false)
                    } else {
                        showAlermDlg(null, 0)
                    }
                }
            })
        }
    }

    /**
     * 更新列表中产品的添加状态
     */
    private fun handleAddList() {
        if (isEditMode) {
            adpSearchResult.data.forEach { item ->
                item.hasAdd = vmProducts.productHasAdd(item.tscode ?: "")
            }
            adpSearchResult.notifyDataSetChanged()
        }
    }

    private fun observeLoadingState() {
        searchVm.searchLoading.observe(this) {
            if (it) {
                showProgress()
            } else {
                hideProgress()
            }
        }
    }

    private fun setViewState(emptyPage: Boolean) {
        tvEmpty?.visibility = if (emptyPage) View.VISIBLE else View.GONE
        rvList?.visibility = if (emptyPage) View.GONE else View.VISIBLE
    }

    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        if (!hidden) {
            pvAnalysis()
        }
    }

    override fun onResumeSecond() {
        super.onResumeSecond()
        if (!isHidden) {
            pvAnalysis()
        }
    }

    private fun pvAnalysis() {
        if (isEditMode) {
            PvReportUtils.reportPvManually(
                HBAnalyticsBean("FragSearchResult", "推荐产品编辑页-搜索结果", "622230", "3"),
                context, null, null, null
            )
        } else {
            PvReportUtils.reportPvManually(
                HBAnalyticsBean("FragSearchResult", "搜索结果页", "351840", "2"),
                context, null, null, null
            )
        }
    }
}