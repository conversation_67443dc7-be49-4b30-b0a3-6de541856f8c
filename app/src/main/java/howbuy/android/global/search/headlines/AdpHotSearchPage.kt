package howbuy.android.global.search.headlines

import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.howbuy.fund.base.config.ValConfig
import com.howbuy.fund.base.nav.NavHelper
import howbuy.android.global.entity.HotRearchData

/**
 * description.
 * 大家都在搜-vp2 页面
 * tao.liang
 * 2024/5/14
 */
class AdpHotSearchPage(frag: Fragment, val size: Int, private val hotData: HotRearchData?) : FragmentStateAdapter(frag) {

    override fun getItemCount(): Int {
        return size
    }

    override fun createFragment(position: Int): Fragment {
        val fragment = FragSubHeadLinesHotSearchList()
        fragment.arguments = NavHelper.obtainArg("", ValConfig.IT_ENTITY, hotData?.hotSearchResultTabDTOS?.getOrNull(position))
        return fragment
    }
}