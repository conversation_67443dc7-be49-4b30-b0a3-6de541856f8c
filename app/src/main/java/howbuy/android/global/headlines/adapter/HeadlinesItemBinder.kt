package howbuy.android.global.headlines.adapter

import android.graphics.Color
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.chad.library.adapter.base.binder.BaseItemBinder
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.howbuy.fund.base.push.PushDispatchHelper
import com.howbuy.fund.base.utils.FundTextUtils
import com.howbuy.fund.base.utils.ShapeCreator
import com.howbuy.fund.base.utils.dp2px
import com.howbuy.lib.utils.ColorUtils
import com.howbuy.lib.utils.DensityUtils
import howbuy.android.global.R
import howbuy.android.global.headlines.entity.HeadlinesItem
import howbuy.android.global.search.renderHighLight
import howbuy.android.global.widgets.tag.BaseTagAdapter
import howbuy.android.global.widgets.tag.TagLayout
import java.util.Arrays

/**
 * @Description 资讯Item
 * <AUTHOR>
 * @Date 2024/5/14
 * @Version v2.1.0
 *
 * @param pageType 1:首页; 2:头条专栏; 3:搜索 4:资讯-推荐-近期热门 其他： 头条主页面
 */
class HeadlinesItemBinder(private val pageType: Int, private val analytics: ((HeadlinesItem) -> Unit)? = null) :
    BaseItemBinder<HeadlinesItem, BaseViewHolder>() {

    companion object {
        //头条主页样式--标题、摘要、标签、一级分类tag、二级分类tag、时间
        const val UI_TYPE_HEADLINE = 0

        //首页样式--标题、摘要、标签、一级分类tag、二级分类tag、时间
        const val UI_TYPE_HOME = 1

        //专栏详情页样式---改为H5
        const val UI_TYPE_COLUMN = 2

        //搜索--标题、标签、一级分类tag、二级分类tag、时间
        const val UI_TYPE_SEARCH = 3

        //资讯-推荐-近期热门--序号、标题、一级分类tag、二级分类tag、时间
        const val UI_TYPE_HOT_LIST = 4
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseViewHolder {
        return BaseViewHolder(LayoutInflater.from(parent.context).inflate(R.layout.item_headlines_list, parent, false)).also {
            if (pageType == UI_TYPE_HOME) {
                it.getView<View>(R.id.container).background =
                    ContextCompat.getDrawable(context, R.drawable.bg_solid_fff_stroke_f3f3f3_10)
                (it.getView<View>(R.id.container).layoutParams as? MarginLayoutParams)?.marginStart = 0
                (it.getView<View>(R.id.container).layoutParams as? MarginLayoutParams)?.marginEnd = 0
            } else if (pageType == UI_TYPE_COLUMN) {
                it.getView<View>(R.id.container).background =
                    ShapeCreator.createRoundRectangle(ColorUtils.parseColor("#FFFFFF"), 10f)
                (it.getView<View>(R.id.container).layoutParams as? MarginLayoutParams)?.marginStart = 5f.dp2px()
                (it.getView<View>(R.id.container).layoutParams as? MarginLayoutParams)?.marginEnd = 5f.dp2px()
            } else if (pageType == UI_TYPE_SEARCH) {
                it.getView<View>(R.id.container).setBackgroundColor(ColorUtils.parseColor("#FFFFFF"))
                it.getView<View>(R.id.container).setPadding(12.5f.dp2px(), 10f.dp2px(), 12.5f.dp2px(), 10f.dp2px())
                (it.getView<View>(R.id.container).layoutParams as? MarginLayoutParams)?.marginStart = 0
                (it.getView<View>(R.id.container).layoutParams as? MarginLayoutParams)?.marginEnd = 0
            } else if (pageType == UI_TYPE_HOT_LIST) {
                it.getView<View>(R.id.container).setBackgroundColor(Color.TRANSPARENT)
                it.getView<View>(R.id.container).setPadding(0, 10f.dp2px(), 0, 10f.dp2px())
                (it.getView<View>(R.id.container).layoutParams as? MarginLayoutParams)?.marginStart = 0
                (it.getView<View>(R.id.container).layoutParams as? MarginLayoutParams)?.marginEnd = 0
            } else {
                it.getView<View>(R.id.container).background =
                    ShapeCreator.createRoundRectangle(ColorUtils.parseColor("#FFFFFF"), 8f)
                (it.getView<View>(R.id.container).layoutParams as? MarginLayoutParams)?.marginStart = 7.5f.dp2px()
                (it.getView<View>(R.id.container).layoutParams as? MarginLayoutParams)?.marginEnd = 7.5f.dp2px()
            }
        }
    }

    override fun convert(holder: BaseViewHolder, data: HeadlinesItem) {
        //序号
        if (pageType == UI_TYPE_HOT_LIST) {
            holder.setGone(R.id.tv_index, false)
                .setText(R.id.tv_index, (holder.bindingAdapterPosition + 1).toString())
        } else {
            holder.setGone(R.id.tv_index, true)
        }
        //标题
        if (pageType == UI_TYPE_SEARCH) {
            //搜索
            holder.setText(R.id.tv_title, FundTextUtils.showTextEmpty(data.title).renderHighLight(data.tsshortNameIK, ColorUtils.parseColor("#C51D25")))
        } else {
            holder.setText(R.id.tv_title, FundTextUtils.showTextEmpty(data.title))
        }
        //摘要
        if (pageType == UI_TYPE_SEARCH || pageType == UI_TYPE_HOT_LIST) {
            holder.setGone(R.id.tv_des, true)
        } else {
            holder.setText(R.id.tv_des, data.desc)
                .setGone(R.id.tv_des, TextUtils.isEmpty(data.desc))
        }
        //标签
        if (pageType == UI_TYPE_HOT_LIST) {
            holder.setGone(R.id.type_tag, true)
        } else {
            renderTypeTag(holder, data.labelType ?: "", holder.getView(R.id.type_tag))
        }
        //一级分类tab、二级分类tab
        renderTag(data.firstType, data.secondType, holder.getView(R.id.tag))
        //日期
        holder.setText(R.id.tv_date, data.publishTime)
            .setGone(R.id.tv_date, TextUtils.isEmpty(data.publishTime))
        holder.itemView.setOnClickListener {
            if (!TextUtils.isEmpty(data.eventUrl)) {
                PushDispatchHelper.pushDispatch(context, data.eventUrl)
            }
            analytics?.invoke(data)
        }
    }

    /**
     * 文章标签
     * 【热点】、【最新】
     * 仅显示 热点 最新--v2.6.0
     */
    private fun renderTypeTag(holder: BaseViewHolder, type: String, tagLayout: TagLayout) {
        val tagList = type.split(",").toMutableList()
        tagList.removeIf { !TextUtils.equals("2", it) && !TextUtils.equals("3", it) }
        if (tagList.isEmpty()) {
            holder.setGone(R.id.type_tag, true)
        } else {
            holder.setGone(R.id.type_tag, false)
            tagLayout.apply {
                tagAdapter = object : BaseTagAdapter<String>(tagList) {
                    override fun getTagView(parent: ViewGroup?, position: Int): View {
                        val tvLabel = TextView(context)
                        tvLabel.textSize = 11f
                        tvLabel.setPadding(
                            DensityUtils.dp2px(4f),
                            DensityUtils.dp2px(1.5f),
                            DensityUtils.dp2px(4f),
                            DensityUtils.dp2px(1.5f)
                        )
                        when (tagList[position]) {
//                        "1" -> {
//                            //置顶
//                            tvLabel.background =
//                                ShapeCreator().stokeWidth(1f).stokeColor(ColorUtils.parseColor("#DE2424")).radius(3f).create()
//                            tvLabel.setTextColor(ColorUtils.parseColor("#DE2424"))
//                            tvLabel.text = "置顶"
//                        }

                            "2" -> {
                                //热点
                                tvLabel.background = ShapeCreator().color(ColorUtils.parseColor("#DE2424")).radius(3f).create()
                                tvLabel.setTextColor(ColorUtils.parseColor("#ffffff"))
                                tvLabel.text = "热点"
                            }

                            "3" -> {
                                //最新
                                tvLabel.background = ShapeCreator().color(ColorUtils.parseColor("#1ADE2424")).radius(3f).create()
                                tvLabel.setTextColor(ColorUtils.parseColor("#DE2424"))
                                tvLabel.text = "最新"
                            }

//                        "4" -> {
//                            //原创
//                            tvLabel.background = ShapeCreator().color(ColorUtils.parseColor("##27518F")).radius(3f).create()
//                            tvLabel.setTextColor(ColorUtils.parseColor("#ffffff"))
//                            tvLabel.text = "原创"
//                        }
                        }
                        return tvLabel
                    }
                }
                notifyDataSetChanged()
            }
        }
    }

    /**
     * 分类tag
     */
    private fun renderTag(firstType: String?, secondType: String?, tagLayout: TagLayout) {
        val tagList = mutableListOf<String>()
        if (!TextUtils.isEmpty(firstType)) {
            tagList.add(firstType ?: "")
        }
        if (!TextUtils.isEmpty(secondType)) {
//            tagList.add(secondType?.split(",")?.firstOrNull() ?: "")
            tagList.add(secondType ?: "")
        }
        tagLayout.apply {
            tagAdapter = object : BaseTagAdapter<String>(tagList) {
                override fun getTagView(parent: ViewGroup?, position: Int): View {
                    val labelView = TextView(context)
                    labelView.textSize = 12f
                    if (position == 0 && !TextUtils.isEmpty(firstType)) {
                        labelView.setTextColor(ColorUtils.parseColor("#2e578a"))
                    } else {
                        labelView.setTextColor(ColorUtils.parseColor("#e2a452"))
                    }
                    labelView.text = "#" + tagList[position]
                    return labelView
                }
            }
            notifyDataSetChanged()
        }
    }
}