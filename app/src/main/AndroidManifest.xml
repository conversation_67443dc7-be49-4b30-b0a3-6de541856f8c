<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES"/>
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <application
        android:name=".GlobalApplication"
        android:allowBackup="false"
        android:allowNativeHeapPointerTagging="false"
        android:extractNativeLibs="true"
        android:fullBackupContent="@xml/backup_rules"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:networkSecurityConfig="@xml/network_security_config"
        android:resizeableActivity="true"
        android:supportsRtl="true"
        android:theme="@style/Theme.HowbuyGlobal"
        tools:replace="android:allowBackup">

        <activity
            android:name=".entry.EntryActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|smallestScreenSize|screenLayout"
            android:exported="true"
            android:label="@string/app_name"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:theme="@style/_Theme_FullScreen">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="m.howbuy.com"
                    android:scheme="howbuyglobal" />
            </intent-filter>

<!--            <intent-filter>-->
<!--                <action android:name="android.intent.action.VIEW" />-->
<!--                <category android:name="android.intent.category.DEFAULT" />-->
<!--                <data-->
<!--                    android:host="com.howbuy.fund"-->
<!--                    android:path="/bridge"-->
<!--                    android:scheme="push" />-->
<!--            </intent-filter>-->
        </activity>

        <activity
            android:name=".MainActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|smallestScreenSize|screenLayout"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustNothing|stateAlwaysHidden" />
        <activity
            android:name=".headlines.vhall.AtySmVhall"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:exported="false"
            android:windowSoftInputMode="adjustResize"
            android:screenOrientation="portrait"/>

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileProvider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />

        </provider>

        <!-- Umeng分享-微信分享回调 -->
        <activity
            android:name="howbuy.android.global.wxapi.WXEntryActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:exported="true"
            android:taskAffinity="howbuy.android.global"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <receiver
            android:name="howbuy.android.global.wxapi.AppRegister"
            android:exported="true"
            android:permission="com.tencent.mm.plugin.permission.SEND" >
            <intent-filter>
                <action android:name="com.tencent.mm.plugin.openapi.Intent.ACTION_REFRESH_WXAPP" />
            </intent-filter>
        </receiver>
    </application>

    <queries>
        <package android:name="com.tencent.mm" />          <!--  // 指定微信包名-->
        <package android:name="com.tencent.mobileqq" />   <!-- //指定qq包名-->
<!--        <package android:name="com.sina.weibo" />         &lt;!&ndash;    //指定微博包名&ndash;&gt;-->
        <package android:name="com.tencent.wework" />     <!-- //指定企业微信包名-->
<!--        <package android:name="com.qzone" />              &lt;!&ndash;     //指定QQ空间包名&ndash;&gt;-->
<!--        <package android:name="com.alibaba.android.rimet" /> &lt;!&ndash;// 指定钉钉包名&ndash;&gt;-->
<!--        <package android:name="com.eg.android.AlipayGphone" /> &lt;!&ndash;// 指定支付宝包名&ndash;&gt;-->
<!--        <package android:name="com.instagram.android" />       &lt;!&ndash;  // 指定instagram包名&ndash;&gt;-->
<!--        <package android:name="com.ss.android.ugc.aweme" />  &lt;!&ndash;// 指定抖音包名&ndash;&gt;-->
    </queries>

</manifest>