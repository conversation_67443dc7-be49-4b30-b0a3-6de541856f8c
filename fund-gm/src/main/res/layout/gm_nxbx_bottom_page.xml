<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    tools:layout_height="114dp"
    android:layout_height="match_parent"
    tools:background="#E5EBFF"
    android:paddingBottom="15dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <TextView
        android:id="@+id/tvPageTitle"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="熊市行情"
        android:layout_marginTop="12dp"
        android:layout_marginLeft="15dp"
        android:textColor="#ff2a3050"
        android:textSize="16dp"
        />

    <TextView
        android:id="@+id/tvDateRegion"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/tvPageTitle"
        app:layout_constraintLeft_toLeftOf="@id/tvPageTitle"
        tools:text="2004/11/14-2005/10/16 "
        android:textColor="#ff2a3050"
        android:textSize="12dp"
        />

    <TextView
        android:id="@+id/tvZoomIn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:minWidth="74dp"
        android:paddingTop="5dp"
        android:paddingBottom="5dp"
        android:paddingLeft="13dp"
        android:paddingRight="13dp"
        android:text="放大区间"
        tools:background="#555a6efa"
        android:gravity="center"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginRight="14dp"
        android:layout_marginTop="18dp"
        android:textColor="#ff5a6efa"
        android:textSize="12dp"
        />

    <!--区间收益-->
    <include
        android:id="@+id/vQjsy"
        android:layout_width="wrap_content"
        tools:layout_width="60dp"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="@id/tvPageTitle"
        app:layout_constraintTop_toBottomOf="@id/tvDateRegion"
        android:layout_marginTop="8dp"
        layout="@layout/gm_item_bjjz_item"/>

    <!--同类平均-->
    <include
        android:id="@+id/vTlpj"
        android:layout_width="wrap_content"
        tools:layout_width="80dp"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@id/vQjsy"
        app:layout_constraintRight_toRightOf="@id/tvZoomIn"
        android:layout_marginRight="10dp"
        layout="@layout/gm_item_bjjz_item"/>

    <!--比较基准-->
    <include
        android:id="@+id/vBjjz"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constrainedWidth="true"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        app:layout_constraintTop_toTopOf="@id/vQjsy"
        app:layout_constraintLeft_toRightOf="@id/vQjsy"
        app:layout_constraintRight_toLeftOf="@id/vTlpj"
        layout="@layout/gm_item_bjjz_item"/>
</androidx.constraintlayout.widget.ConstraintLayout>