<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/cl_f2f5f9"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_transfer_in_out"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/fd_bg_item_bottom_no_pressed"
        android:text="份额转入/转出"
        android:padding="15dp"
        android:gravity="center"
        android:textSize="18sp"
        android:textColor="@drawable/xml_text_hold_bt1"/>


    <TextView
        android:id="@+id/tv_cancel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:background="@color/white"
        android:gravity="center"
        android:padding="15dp"
        android:text="取消"
        android:textColor="@drawable/xml_text_hold_bt1"
        android:textSize="18sp"
        app:layout_constraintTop_toBottomOf="@+id/constraint3" />
</LinearLayout>