package com.howbuy.fund.archive.valuechart;

import android.app.Activity;
import android.content.Context;
import android.content.res.Configuration;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.util.Consumer;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelStore;
import androidx.lifecycle.ViewModelStoreOwner;

import com.howbuy.fund.base.arch.ClearViewModel;
import com.howbuy.fund.base.arch.ViewModelHelper;
import com.howbuy.lib.utils.ContextUtil;


/**
 * 轻量级子页面基类，用于替换Fragment
 */
public abstract class PageView implements ViewModelStoreOwner, LifecycleOwner {
    protected View mRootView;
    protected ViewModelStoreOwner mViewModelStoreOwner;
    protected LifecycleOwner mLifecycleOwner;
    /**
     * 页面状态渲染
     */
    private PageStateRender expRender;

    /**
     * 创建子页面View回调
     * @param parent 当前子页面的父View
     * @return 子页面View
     */
    @NonNull
    protected abstract View createView(@NonNull ViewGroup parent);


    /**
     * 页面创建回调
     * @param view 当前子页面的View
     * @param args 子页面入口参数
     */
    protected abstract void onViewCreate(@NonNull View view, @Nullable Bundle args);

    /**
     * {@link Activity#onConfigurationChanged(Configuration)}回调分发
     * @param newConfig 参数
     */
    protected abstract void onConfigurationChanged(@NonNull Configuration newConfig);

    /**
     * 当前子页面显示回调
     * @param isInit true:初始化显示，false:非初始化显示
     */
    protected abstract void onShow(boolean isInit);

    /**
     * 当前子页面隐藏回调
     */
    protected abstract void onHide();

    /**
     * 是否正在显示：如果View已经在父View中，则为显示；否则未显示
     * @return true-显示；false-未显示
     */
    public boolean isShow() {
        return this.mRootView != null && this.mRootView.getParent() instanceof ViewGroup;
    }

    /**
     * 是否已初始化，如果View已经创建则视为已经初始化
     * @return true-已初始化；false-未初始化
     */
    public boolean isInit() {
        return this.mRootView != null;
    }

    @Nullable
    public LifecycleOwner getLifecycleOwner() {
        return mLifecycleOwner;
    }

    @NonNull
    @Override
    public ViewModelStore getViewModelStore() {
        return this.mViewModelStoreOwner.getViewModelStore();
    }

    @NonNull
    @Override
    public Lifecycle getLifecycle() {
        return this.mLifecycleOwner.getLifecycle();
    }

    /**
     * 创建ViewModel
     */
    @Nullable
    protected <T extends ViewModel> T createViewModel(@NonNull Class<T> type) {
        if (!isInit() ||
                this.mViewModelStoreOwner == null ||
                this.mLifecycleOwner == null)
            return null;
        if (mLifecycleOwner.getLifecycle().getCurrentState() == Lifecycle.State.DESTROYED)
            return null;
        try {
            T vm = ViewModelHelper.createViewModel(this.mViewModelStoreOwner, type);
            if (vm instanceof ClearViewModel) {
                ((ClearViewModel) vm).setLifecycleOwner(mLifecycleOwner);
            }
            return ViewModelHelper.createViewModel(this.mViewModelStoreOwner, type);
        } catch (Exception e) {
            return null;
        }
    }

    public View getRootView() {
        return mRootView;
    }

    public Context getContext() {
        return mRootView.getContext();
    }

    public Activity getActivity() {
        return ContextUtil.getActivity(mRootView);
    }

    public boolean isDetached() {
        return this.isShow();
    }

    /**
     * 显示页面状态: loading\refresh\empty... 详细参考{@link PageStateType}
     * @param type 页面状态类型
     */
    protected void showPageState(@NonNull PageStateType type) {
        if (expRender == null) {
            expRender = new PageStateRender((ViewGroup) mRootView);
        }
        expRender.show(type);
    }

    /**
     * 隐藏页面状态View
     */
    protected void hideStateView() {
        if (expRender == null) {
            expRender = new PageStateRender((ViewGroup) mRootView);
        }
        expRender.hide();
    }

}
