package com.howbuy.fund.archive.quota

import android.content.Context
import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import com.alibaba.android.arouter.facade.annotation.Route
import com.google.android.material.tabs.TabLayout
import com.howbuy.fund.R
import com.howbuy.fund.base.analytics.HbAnalytics
import com.howbuy.fund.base.config.ValConfig
import com.howbuy.fund.base.frag.AbsHbFrag
import com.howbuy.fund.base.router.fragpath.GmRouterPath
import com.howbuy.lib.adp.AbsFragPageAdp
import kotlinx.android.synthetic.main.frag_common_tab_viewpager_layout.*
import com.howbuy.android.analytics.annotation.pv.PvInfo


/**
 * @Description 指标解读
 * <AUTHOR>
 * @Date 2021/12/13 16:21
 * @Version V774
 */
@Route(path = GmRouterPath.PATH_FRAG_GM_FUND_QUOTA_EXPLAIN)
@PvInfo(pageId = "375350", level = "3", name = "指标解读页面", className = "FragFundQuotaExplain")
class FragFundQuotaExplain : AbsHbFrag() {

    private var mTabAdapter: TabAdapter? = null
    //是否初次进入页面
    private var isInitTabSelect = true

    override fun getFragLayoutId() = R.layout.frag_fund_quota_explain

    override fun stepAllViews(root: View?, savedInstanceState: Bundle?) {
    }

    override fun parseArgment(arg: Bundle?) {
        if (arg == null) return
        val position = arg.getInt(ValConfig.IT_TYPE, 0)
        if (mTabAdapter == null) {
            mTabAdapter = TabAdapter(context!!, childFragmentManager, arg)
            canscroll_viewpager.adapter = mTabAdapter
            tab_layout.setupWithViewPager(canscroll_viewpager)
            configAnalytics()
        }
        canscroll_viewpager.currentItem = position
    }

    private fun configAnalytics() {
        tab_layout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                tab?.let {
                    if (!isInitTabSelect) {
                        if (0 == it.position) {
                            HbAnalytics.onClick(it.view.context, "336740")
                        } else if (1 == it.position) {
                            HbAnalytics.onClick(it.view.context, "336750")
                        }
                    }
                    isInitTabSelect = false
                }
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {}
            override fun onTabReselected(tab: TabLayout.Tab?) {}
        })
    }

    class TabAdapter(val context: Context, val fm: FragmentManager, val bundle: Bundle) : AbsFragPageAdp(fm) {

        val PAGE_TITLE = mutableListOf("基金指标分析", "行情解读")

        override fun getCount() = PAGE_TITLE.size

        override fun getItem(position: Int): Fragment {
            val fragmentName = when (position) {
                0 -> FragFundQuotaDetail::class.java.name
                else -> FragFundSpecialDetail::class.java.name
            }
            val frag = fm.fragmentFactory.instantiate(
                context.classLoader, fragmentName
            )
            frag.arguments = bundle
            return frag
        }

        override fun getTag(position: Int) = PAGE_TITLE[position]

        override fun getPageTitle(position: Int) = PAGE_TITLE[position]
    }
}