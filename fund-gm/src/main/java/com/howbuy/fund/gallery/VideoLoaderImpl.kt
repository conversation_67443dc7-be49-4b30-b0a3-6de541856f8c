package com.howbuy.fund.gallery

import android.content.ContentResolver
import android.graphics.Bitmap
import android.net.Uri
import android.os.Build
import android.provider.MediaStore
import android.util.Size
import androidx.core.content.ContentResolverCompat
import androidx.core.os.CancellationSignal
import com.howbuy.fund.base.FundApp
import com.howbuy.imgpick.MediaInfo
import com.howbuy.lib.utils.LogUtils

/**
 * 视频加载实现
 */
class VideoLoaderImpl : MediaLoader {
    override fun loadAllInfoList(): List<MediaInfo> {
        return this.query()
    }

    override fun loadInfoListByCategory(name: String): List<MediaInfo> {
        val selection = ("("
                + MediaStore.Files.FileColumns.MEDIA_TYPE
                + "=?"
//                + MediaStore.Files.FileColumns.MEDIA_TYPE_VIDEO
                + " AND " +
                MediaStore.Files.FileColumns.BUCKET_DISPLAY_NAME
                + "=?"
//                + name
                + ")")
        return this.query(
            selection = selection,
            selectionArgs = arrayOf(
                "" + MediaStore.Files.FileColumns.MEDIA_TYPE_VIDEO,
                name
            )
        )
    }

    override fun loadInfo(id: Long): MediaInfo? {
        val selection = ("("
                + MediaStore.Files.FileColumns.MEDIA_TYPE
                + "=?"
//                + MediaStore.Files.FileColumns.MEDIA_TYPE_VIDEO
                + " AND "
                +  MediaStore.MediaColumns._ID
                + "=?"
//                + id
                + ")")
        return this.query(selection = selection,
            selectionArgs = arrayOf(
                "" + MediaStore.Files.FileColumns.MEDIA_TYPE_VIDEO,
                "" + MediaStore.MediaColumns._ID
            )
        ).firstOrNull()
    }

    override fun decodeThumbnail(id: Long, size: Size): Bitmap? {
        LogUtils.d("DebugGallery", "[video] decodeThumbnail")
        val path = "content://media/external/videos/media/" + id
        if (!path.startsWith("content://media/")) {
            return null
        }
        var bitmap: Bitmap? = null
        try {
            val uri = Uri.parse(path)
            val contentResolver: ContentResolver = FundApp.getApp().contentResolver
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                try {
                    bitmap = contentResolver.loadThumbnail(
                        uri,
                        size,
                        null
                    )
                } catch (ignore: java.lang.Exception) {
                    LogUtils.e("DebugGallery", "加载失败:${ignore.message}")
                }
            }
            if (bitmap == null) {
                val index = path.lastIndexOf('/')
                if (index > 0) {
                    val mediaId = path.substring(index + 1).toLong()
                    bitmap = MediaStore.Video.Thumbnails.getThumbnail(
                        contentResolver,
                        mediaId,
                        MediaStore.Video.Thumbnails.MINI_KIND,
                        null
                    )
                }
            }
        } catch (e: Throwable) {
            LogUtils.e("DebugGallery", e.message)
            throw RuntimeException(e)
        }
        return bitmap
    }

    private fun query(
        selection: String = MediaLoader.TYPE_SELECTION_VIDEO,
        selectionArgs: Array<String>? = null,
        sortOrder: String? = MediaStore.Video.Media.DATE_ADDED + " DESC",
        cancellationSignal: CancellationSignal? = null,
    ): MutableList<MediaInfo> {
        val cursor = ContentResolverCompat.query(
            FundApp.getApp().contentResolver,
            MediaLoader.CONTENT_URI,
            MediaLoader.PROJECTIONS,
            selection,
            selectionArgs,
            sortOrder,
            cancellationSignal
        ) ?: throw RuntimeException("query error")
        val list = mutableListOf<MediaInfo>()
        while (cursor.moveToNext()) {
            val id = cursor.getLong(MediaLoader.IDX_ID)
            val path = cursor.getString(MediaLoader.IDX_DATA)
            val type = cursor.getInt(MediaLoader.IDX_MEDIA_TYPE)
            val dateModified = cursor.getLong(MediaLoader.IDX_DATE_MODIFIED)
            val mimeType = cursor.getString(MediaLoader.IDX_MIME_TYPE)
            val duration = cursor.getLong(MediaLoader.IDX_DURATION)
            val size = cursor.getLong(MediaLoader.IDX_SIZE)
            val width = cursor.getInt(MediaLoader.IDX_WIDTH)
            val height = cursor.getInt(MediaLoader.IDX_HEIGHT)
            val orientation = cursor.getInt(MediaLoader.IDX_ORIENTATION)
            val albumName = cursor.getString(MediaLoader.IDX_BUCKET_DISPLAY_NAME)

            list.add(
                MediaInfo(
                    id,
                    path,
                    type,
                    dateModified,
                    mimeType,
                    duration,
                    size,
                    width,
                    height,
                    orientation,
                    albumName
                )
            )

            LogUtils.d("DebugGallery", "video info, id:${id}, path:${path}, w:${width}, h:${height}, duration:${duration}")
        }
        return list
    }
}