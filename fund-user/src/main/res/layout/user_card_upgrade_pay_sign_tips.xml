<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:id="@+id/paySignTipsContainer"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <!--建行直连-->
    <TextView
        android:id="@+id/tvTipsDirect"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="11dp"
        android:layout_marginRight="11dp"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:paddingTop="6dp"
        android:paddingBottom="6dp"
        android:lineSpacingExtra="2dp"
        android:background="@drawable/user_fff5e7_r5"
        android:textColor="#ff8f22"
        android:textSize="12sp"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="根据新规，建设银行需开通银联在线支付才能进行线上支付使用，请操作“银行直连”方式的升级开通在线支付" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/vPaySign"
        tools:layout_marginTop="100dp"
        android:layout_marginLeft="11dp"
        android:layout_marginRight="11dp"
        app:layout_constraintTop_toTopOf="parent"
        android:background="@drawable/user_fff5e7_r5"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">


        <ImageView
            android:id="@+id/iconTips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12.5dp"
            android:layout_marginTop="10dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            android:src="@drawable/img_reminder_light"/>

        <TextView
            android:id="@+id/tvTipsOther"
            app:layout_constraintLeft_toRightOf="@id/iconTips"
            app:layout_constraintTop_toTopOf="@id/iconTips"
            app:layout_constraintRight_toLeftOf="@id/tvGuide"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:lineSpacingExtra="2dp"
            app:layout_constraintVertical_bias="0"
            android:layout_width="0dp"
            android:layout_marginBottom="8dp"
            android:layout_height="wrap_content"
            tools:text="根据新规，建设银行需开通银联在线支付才能进行线上支付使用，请操作“银行直连”方式的升级开通在线支付"
            android:textColor="#ffff8f22"
            android:textSize="12sp"
            />

        <TextView
            android:id="@+id/tvGuide"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="查看操作手册"
            android:textSize="12sp"
            android:textColor="@color/white"
            android:paddingLeft="8dp"
            android:paddingRight="8dp"
            android:layout_marginRight="10dp"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:paddingTop="6dp"
            android:paddingBottom="6dp"
            tools:background="@color/cl_ff5736"
            />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!--<ImageView
        android:layout_width="17.5dp"
        android:layout_height="17.5dp"
        android:layout_marginRight="12dp"
        app:layout_constraintRight_toRightOf="parent"
        android:src="@drawable/user_card_upgrade_close"
        app:layout_constraintTop_toTopOf="@id/vPaySign"
        app:layout_constraintBottom_toTopOf="@id/vPaySign"
        />-->
</androidx.constraintlayout.widget.ConstraintLayout>