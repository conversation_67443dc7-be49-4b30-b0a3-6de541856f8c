package com.howbuy.fund.base.adapter.group;

import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.SparseArrayCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.viewholder.BaseViewHolder;
import com.howbuy.fund.base.adapter.AbsBusinessAdapter;

import java.util.List;


public class TypedAdapter<T> extends AbsBusinessAdapter<T> {
    @Nullable
    private HolderFactory holderFactory;
    @Nullable
    private HolderBinder<T, BaseViewHolder> holderBinder;
    @Nullable
    private ViewTypeConverter viewTypeConverter;

    private TypedAdapter() {
        super(0);
    }

    public TypedAdapter(int layResId, @NonNull HolderFactory holderFactory, @Nullable HolderBinder<T, BaseViewHolder> holderBinder, @Nullable ViewTypeConverter viewTypeConverter) {
        super(layResId);
        this.holderFactory = holderFactory;
        this.holderBinder = holderBinder;
        this.viewTypeConverter = viewTypeConverter;
    }

    @NonNull
    @Override
    protected BaseViewHolder onCreateDefViewHolder(@NonNull ViewGroup parent, int viewType) {
        if (null != holderFactory) {
            return holderFactory.create(parent, viewType);
        }
        return super.onCreateDefViewHolder(parent, viewType);
    }

    @Override
    protected void convert(@NonNull BaseViewHolder holder, T t) {
        if (null != holderBinder) {
            if (holder.getBindingAdapterPosition() == RecyclerView.NO_POSITION) {
                holderBinder.bind(holder, RecyclerView.NO_POSITION, t);
            } else {
                holderBinder.bind(holder, holder.getBindingAdapterPosition() - getHeaderLayoutCount(), t);
            }
        }
    }

    @Override
    public int getDefItemViewType(int position) {
        if (null == this.viewTypeConverter) return super.getDefItemViewType(position);
        return this.viewTypeConverter.getTypeByPosition(position);
    }

    public static class Builder<T> {
        private HolderFactory holderFactory;
        private HolderBinder<T, BaseViewHolder> holderBinder;
        private ViewTypeConverter viewTypeConverter;
        private int layResId;

        public Builder() {
        }

        public Builder<T> setLayResId(@NonNull int layResId) {
            this.layResId = layResId;
            return this;
        }

        public Builder<T> setHolderFactory(@NonNull HolderFactory holderFactory) {
            this.holderFactory = holderFactory;
            return this;
        }

        public Builder<T> setHolderBinder(@NonNull HolderBinder<T, BaseViewHolder> holderBinder) {
            this.holderBinder = holderBinder;
            return this;
        }

        public Builder<T> setViewTypeConverter(@NonNull ViewTypeConverter viewTypeConverter) {
            this.viewTypeConverter = viewTypeConverter;
            return this;
        }

        public TypedAdapter<T> build() {
            return new TypedAdapter<>(layResId, holderFactory, holderBinder, viewTypeConverter);
        }
    }

    public void setHolderBinder(@NonNull HolderBinder<T, BaseViewHolder> holderBinder) {
        this.holderBinder = holderBinder;
    }

    public void setViewTypeConverter(@NonNull ViewTypeConverter viewTypeConverter) {
        this.viewTypeConverter = viewTypeConverter;
    }

    public int getDataItemCount() {
        return getData().size();
    }

    @NonNull
    public List<T> getDataList() {
        return getData();
    }

    public void updateDataList(@NonNull List<T> dataList) {
        setList(dataList);
    }

    public void appendList(@NonNull List<T> dataList) {
        if (dataList.isEmpty()) return;
        addData(dataList);
    }

    public void append(@NonNull T dataItem) {
        addData(dataItem);
    }

    public void clearData(boolean notify) {
        getData().clear();
        if (notify) {
            notifyDataSetChanged();
        }
    }

    public void clear(boolean notify) {
        removeAllHeaderView();
        removeAllFooterView();
        clearData(notify);
    }

    protected SparseArrayCompat<View> headerViewList = new SparseArrayCompat<>();
    protected SparseArrayCompat<View> footerViewList = new SparseArrayCompat<>();

    public void addHeaderViewWithType(View header, int type) {
        if (getHeaderViewByType(type) == null) {
            headerViewList.put(type, header);
            addHeaderView(header, headerViewList.indexOfKey(type));
        } else {
            headerViewList.put(type, header);
            setHeaderView(header, headerViewList.indexOfKey(type));
        }
        notifyDataSetChanged();
    }

    public View getHeaderViewByType(int type) {
        return headerViewList.get(type);
    }

    public void removeHeaderViewByType(int type) {
        View header = getHeaderViewByType(type);
        if (header != null) {
            headerViewList.remove(type);
            removeHeaderView(header);
            notifyDataSetChanged();
        }
    }

    public void addFooterViewWithType(View footer, int type) {
        if (getFooterViewByType(type) == null) {
            footerViewList.put(type, footer);
            addFooterView(footer, footerViewList.indexOfKey(type));
        } else {
            footerViewList.put(type, footer);
            setFooterView(footer, footerViewList.indexOfKey(type));
        }
        notifyDataSetChanged();
    }

    public View getFooterViewByType(int type) {
        return footerViewList.get(type);
    }

    public void removeFooterViewByType(int type) {
        View footer = getFooterViewByType(type);
        if (footer != null) {
            footerViewList.remove(type);
            removeFooterView(footer);
            notifyDataSetChanged();
        }
    }
}