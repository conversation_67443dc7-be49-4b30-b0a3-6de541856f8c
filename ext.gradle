ext {
    commonVersions = [
            compileSdkVersion         : 30,
            buildToolsVersion         : "30.0.3",
            minSdkVersion             : 23,
            targetSdkVersion          : 30,
            flavorDimensions          : 'default',
            sourceCompatibilityVersion: JavaVersion.VERSION_1_8,
            targetCompatibilityVersion: JavaVersion.VERSION_1_8,
    ]
    dependencies = [
            /**
             * 好买库
             */
            'hb-imgloader'                         : "com.howbuy.android.imgloader:imgloader:*******", //支持overide()方法设置
            'hb-gson'                              : "com.howbuy.android.gson:gson:1.0.1",
            'hb-scanner'                           : "com.howbuy.android.scanner:scanner:3.0.1",
                                                    //hb-toast库在 build.gradle中加了版本对齐脚本, 改版本号时,注意要一并修改
            'hb-toast'                             : "com.howbuy.android.toast:hbtoast:*******",
            'hb-h5zip'                             :"com.howbuy.android.h5zip:h5zip:1.1.7",
            //TODO:注意hb-net库,4.0.8以上,统一海外库,支持304缓存,且需要在AppBootTask中设置 3Des,DH请求uriKey/urlpath等参数(储蓄罐还未升级)
            'hb-net'                               : "com.howbuy.android.hbnet:net:*******", //支持304缓存机制
            'hb-analysis'                          : "com.howbuy.android.analysis:hb_Analysis:*******", //修改umeng上DbSotre事务异常
            'analytics-annotation'                 : "com.howbuy.android.analysis:annotation:0.0.1", //埋点注解
            'hb-pay'                               : "com.howbuy.android.pay:paydialog:3.0.7",
            'hb-idcardocr'                         : "com.howbuy.android:idcardocr:3.0.6",
            'hb-utils'                             : "com.howbuy.android:utils:*******",
            'hb-gesture'                           : "com.howbuy.android.gesture:gesture:*******", //修改IllegalArgumentException: width and height must be > 0
            'hb-dialog'                            : "com.howbuy.android:dialog:3.0.3",
            'permission'                           : "com.howbuy.android:permission:3.0.2",
            'loginobserver'                        : "com.howbuy.android:loginobserver:3.0.9",//优化分发逻辑，防止并发修改异常
            'push_hub'                             : "com.howbuy.android.push:hub:3.0.5",
            'hb-refresh'                           : 'com.howbuy.android:refresh:3.0.0',
            'hb-indexbar'                          : 'com.howbuy.android:indexbar:3.0.0',
            'hb-bankcard-ocr'                      : 'com.howbuy.android.bankcard:ocr:3.0.4',
            'hb-bankcard-auth-fund'                : 'com.howbuy.android.bankcard:auth_fund:3.0.0',
            'hb-shadow'                            : 'com.howbuy.android.ui:shadow:3.0.0',
            'hb-log'                               : 'com.howbuy.android.hlog:api:0.0.7',
            'activity-guard-api'                   : 'com.howbuy.android.activity-guard:guard-api:1.0.4.4',
            'activity-guard-core'                  : 'com.howbuy.android.activity-guard:guard-core:1.0.6',
            'user_data_api'                        : 'com.howbuy.android.userdata:api:0.0.9.1',
            'user_data_core'                       : 'com.howbuy.android.userdata:core:0.0.9.4',
            "mpandroid-chart"                      : "com.howbuy.android.chart:chart:3.1.1",
            "modify-url"                           : "com.howbuy.android.modifyurl:modifyurl:1.0.7",
            'delayclick_annotation'                : 'com.howbuy.android.delayclick:annotation:1.0.1',
            "elkreport_api"                        : 'com.howbuy.android.elkreport:api:1.0.1',
            "hb_shortvideo"                        : 'com.howbuy.android.shortvideo:shortvideo:2.0.2-SNAPSHOT',
            "hbBanner": 'com.howbuy.android.banner:bannerview:1.0.3', //增加了loopStart回调
            'net_diagnose'                         : 'com.howbuy.android.net:diagnose:1.0.0',

            /**
             * 第三方库
             */
            "wire-runtime"                         : "com.squareup.wire:wire-runtime:2.1.2",
            "rxjava"                               : "io.reactivex.rxjava2:rxjava:2.1.2",
            "rxandroid"                            : "io.reactivex.rxjava2:rxandroid:2.1.0",
            "debugLeakCanary"                      : "com.squareup.leakcanary:leakcanary-android:2.13",
            "arouter-api"                          : "com.alibaba:arouter-api:1.5.2",
            "arouter-compiler"                     : "com.alibaba:arouter-compiler:1.5.2",
            "picasso"                              : "com.squareup.picasso:picasso:2.71828",
            "glide"                                : "com.github.bumptech.glide:glide:4.10.0",
            "glide-compiler"                       : "com.github.bumptech.glide:compiler:4.10.0",
            "glide-webpdecoder"                    : "com.github.zjupure:webpdecoder:2.0.4.10.0",
            "fresco"                               : "com.facebook.fresco:fresco:2.3.0",
            'fresco-animated-webp'                 : "com.facebook.fresco:animated-webp:2.3.0",
            'fresco-webpsupport'                   : "com.facebook.fresco:webpsupport:2.3.0",
            'fresco-animated-gif'                  : "com.facebook.fresco:animated-gif:2.3.0",
            'joda-time'                            : "joda-time:joda-time:2.10.1",
            'walle'                                : "com.meituan.android.walle:library:1.1.6",
            'bindingcollectionadapter'             : "me.tatarka.bindingcollectionadapter2:bindingcollectionadapter:4.0.0",
            'bindingcollectionadapter-recyclerview': "me.tatarka.bindingcollectionadapter2:bindingcollectionadapter-recyclerview:4.0.0",
            'recyclerView-adapter-helper'          : "com.github.CymChad:BaseRecyclerViewAdapterHelper:3.0.4",// 万能适配器
            'wheelpicker'                          : 'com.github.gzu-liyujiang.AndroidPicker:WheelPicker:4.1.4',// 单项/数字、二三级联动、日期/时间等滚轮选择器
            "mzCoreLibrary"                        : "com.howbuy.thirdsdk:mzCoreLibrary:2.6.0-fix-202306161824",
            "mzLiveSdk"                            : "com.howbuy.thirdsdk:mzLiveSdk:2.6.2-fix",
            "mzPlayer"                             : "com.howbuy.thirdsdk:mzPlayer:2.7.3-fix",
            "liteavsdk"                            : "com.howbuy.thirdsdk:liteavsdk:2.0.0-fix", //在build.gradle中对齐了另一个版本


            /**
             * 官方基础库
             */
            "support-design"                       : "com.google.android.material:material:1.2.1",
            "support-appcompat"                    : "androidx.appcompat:appcompat:1.2.0",
            "support:recyclerview"                 : "androidx.recyclerview:recyclerview:1.2.0",
            'constraint-layout'                    : "androidx.constraintlayout:constraintlayout:2.0.4",
            "support-multidex"                     : "androidx.multidex:multidex:2.0.1",
            'lifecycle-extensions'                 : "androidx.lifecycle:lifecycle-extensions:2.1.0",
            'kotlinx_coroutines'                   : "org.jetbrains.kotlinx:kotlinx-coroutines-android:1.5.2",
            'kotlinx_lifecycleScope'               : "androidx.lifecycle:lifecycle-runtime-ktx:2.2.0", //添加自动绑定lifecycle使用的协程
            'kotlinx_viewModelScope'               : "androidx.lifecycle:lifecycle-viewmodel-ktx:2.2.0", //添加viewmodel使用场景lifecycle使用的协程
            'localbroadcastmanager'                : "androidx.localbroadcastmanager:localbroadcastmanager:1.0.0",
            'legacy'                               : "androidx.legacy:legacy-support-core-ui:1.0.0",
            'kotlin-stdlib'                        : 'org.jetbrains.kotlin:kotlin-stdlib:1.5.20',
    ]

    //组件的project path映射
    componentPathMap = [
            'hBRouter'              : ':common-libs:hBRouter',
            'hBLCommon'             : ':common-libs:hBLCommon',
            'hBComponent'           : ':common-libs:hBComponent',
            'logupload'             : ':business_component:logupload',
            'share_dialog'          : ':business_component:share_dialog',
            'upgrade'               : ':business_component:upgrade',
            'fund-base'             : ':fund-base',
            'fund-agentweb'         : ':fund-agentweb',
            'card_api'              : ':card_api',
            'fund-user'             : ':fund-user',
            'fund-common'           : ':fund-common',
            'fund-chart'            : ':fund-chart',
            'fund-sm'               : ':fund-sm',
            'fund-gm'               : ':fund-gm',
            'fund-main'             : ':fund-main',
            'fund-setting'          : ':fund-setting',
            'account_api'           : ':account_api',
            'arch'                  : ':arch',
            'login_api'             : ':login_api',
            'login_impl'            : ':login_impl',
            'share_api'             : ':business_component:share_api',
            'gm_api'                : ':gm_api',
            'sm_api'                : ':sm_api',
            'agentweb_api'          : ':agentweb_api',
            'guomi_api'             : ':guomi_api',
            'annonce_api'           : ':annonce_api',
            'analytics_api'         : ':analytics_api',
            'cmd_api'               : ':cmd_api',
            'setting_api'           : ':setting_api',
            'arouter_intercept_api' : ':common-libs:arouter_intercept_api',
            'arouter_intercept_impl': ':common-libs:arouter_intercept_impl',
    ]

    //组件库的maven库版本，配置已经迁移至'runtime.version.properties'

    //是否为全量源码编译，
    allSourceBuild = true
    //依赖管理工具
    depUtil = new DepUtils(rootProject)
}


class DepUtils {
    private Project rootProject
    private String[] sourceModules = null
    private Properties properties = new Properties()
    public Map<String, String> runtimeVersions = new HashMap<>()

    DepUtils(Project rootProject) {
        this.rootProject = rootProject
        this.parseRuntimeVersion()
        parseSourceModules()
    }

    Map<String, String> getRuntimeVersions() {
        return this.runtimeVersions
    }

    /**
     * 添加依赖(根据配置，动态处理库与源码的切换)
     * @param id 库的name
     * @param handler DependencyHandler
     */
    void addDependency(String id, DependencyHandler handler) {
        addDepInternal("implementation", id, handler)
    }

    void api(String id, DependencyHandler handler) {
        addDepInternal("api", id, handler)
    }

    void implementation(String id, DependencyHandler handler) {
        addDepInternal("implementation", id, handler)
    }

    void compileOnly(String id, DependencyHandler handler) {
        addDepInternal("compileOnly", id, handler)
    }

    private void addDepInternal(String type, String id, DependencyHandler handler) {
        if (type != "api" && type != "implementation" && type != "compileOnly") {
            throw new IllegalArgumentException("only support api or implementation or compileOnly, but found '$type'!")
        }

        String projectPath = rootProject.ext.componentPathMap.get(id)
        String externalRepository = this.runtimeVersions.get(id)
        boolean foundProject = null != projectPath && projectPath.length() > 0
        //如果配置了全量源码编译，优先生效
        boolean buildSource = rootProject.ext.allSourceBuild
        if (!buildSource) {
            //如果库对应的模块配置为源码编译（local.properties）
            buildSource = isModuleConfigForSource(id)
        }

        //源码编译
        if (foundProject && buildSource) {
            def projectFind = rootProject.findProject(projectPath)
            if (null != projectFind) {
                println("local project dependency: '$projectPath'")
                handler.add(type, projectFind)
                return
            }
        }

        //使用库进行编译
        if (null == externalRepository || externalRepository.length() > 0) {
//            println("external dependency: '$externalRepository'")
            ExternalModuleDependency dep = handler.add(type, externalRepository)
            if (id == "fund-gm" || id == "fund-sm") { //因为使用平台构建，该库时覆盖形式，因此需要保证每次都是使用最新的包
                dep.setChanging(true)
            }
            return
        }

        println("repsitory for id:'$id', not found!!")
    }

    /**
     * 模块是否配置为源码编译
     * @param moduleName 模块名称
     * @return true-源码编译；false-非源码编译
     */
    boolean isModuleConfigForSource(String moduleName) {
        return null != sourceModules && sourceModules.contains(moduleName)
    }

    public String[] getSourceModules() {
        return this.sourceModules
    }

    private void parseRuntimeVersion() {
        File propertyFile = rootProject.file('runtime.version.properties')
        if (propertyFile.exists()) {
            def properties = new Properties()
            properties.load(propertyFile.newDataInputStream())
            properties.entrySet().forEach { entry ->
                this.runtimeVersions.put(entry.key, entry.value)
            }
        }
        println("runtimeVersions: $runtimeVersions")
    }

    /**
     * 解析根目录下的local.properties文件中的“source_module”属性，并保存到域变量
     */
    private void parseSourceModules() {
        File localProperties = rootProject.file('local.properties')
        if (localProperties.exists()) {
            properties.load(localProperties.newDataInputStream())
        }
        sourceModules = getConfig("source_module").split(",")
    }

    /**
     * 从根目录的local.properties中读取配置，如果没有则返回空字符串
     * @param key 配置项名称
     * @return 配置的值（字符串）
     */
    public String getConfig(String key) {
        return properties.get(key, "")
    }

    public String execCmdGetOutput(String cmd) {
        try {
            return readStream(Runtime.runtime.exec(cmd).getInputStream())
        } catch (Exception e) {
            e.printStackTrace()
            return ""
        }
    }
    /**
     * 判断指定目录下是否有修改为提交
     */
    boolean hasModifyNotCommit(String path) {
        return execCmdGetOutput("git status -s $path").length() > 0
    }

    static String readStream(InputStream is) {
        if (null == is) return null
        List<String> lines = is.readLines()
        StringBuilder sb = new StringBuilder()
        lines.forEach {
            String line = it.trim()
            if (line.length() > 0) {
                sb.append(line).append("\n")
            }
        }
        String output = sb.toString().trim()
        is.close()
        return output
    }

    String getTreeHash(Project prj) {
        String prjPath = prj.projectDir.absolutePath
        String rootPrjPath = prj.rootProject.projectDir.absolutePath
        if (!rootPrjPath.endsWith(File.separator)){
            rootPrjPath += File.separator
        }
        //使用相对路径，否则git命令在windows环境中会执行异常
        prjPath = prjPath.replace(rootPrjPath, "")
        if (prjPath.startsWith(File.separator)) {
            prjPath = prjPath.substring(1)
        }
        if ("/" != File.separator) {
            prjPath = prjPath.replace(File.separator, "/")
        }

        def cmd = "git rev-parse HEAD:$prjPath"
        def is = Runtime.runtime.exec(cmd).getInputStream()
        return readStream(is)
    }
}
