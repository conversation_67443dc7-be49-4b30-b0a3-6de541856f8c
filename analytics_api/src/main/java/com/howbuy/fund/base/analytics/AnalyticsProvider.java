package com.howbuy.fund.base.analytics;

import android.content.Context;
import android.text.TextUtils;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.howbuy.analytics.Configs;
import com.howbuy.analytics.EventCreate;
import com.howbuy.analytics.HbAnalyticsConfigs;
import com.howbuy.analytics.builder.EventCache;
import com.howbuy.analytics.common.ACTIVE_TYPE;
import com.howbuy.analytics.entity.TagBuild;
import com.howbuy.fund.impl.IAnalyticsProvider;
import com.howbuy.lib.compont.GlobalApp;
import com.howbuy.lib.utils.LogUtils;

import java.util.HashMap;

import androidx.fragment.app.FragmentActivity;

import static com.howbuy.fund.base.analytics.Analytics.KEY_STAY_TIME;

/**
 * class description.
 * 埋点实现
 * 1. umeng 埋点上报实现;
 * 2. 好买大数据 埋点上报实现
 * <AUTHOR>
 * @date 2023/8/9
 */
@Route(path = "/analytics/AnalyticsProvider")
public class AnalyticsProvider implements IAnalyticsProvider {
    private static String TAG = "AnalyticsProvider";

    public void onEvent(Context cx, String eventId) {
//        MobclickAgent.onEvent(cx, eventId);
        LogUtils.d(TAG, "onEvent-->" + eventId);
    }

    public void onEvent(Context cx, String eventId, HashMap<String, String> map) {
//        MobclickAgent.onEvent(cx, eventId, map);
        LogUtils.d(TAG, "onEvent-->" + eventId + ": " + map);
    }

    /*
     * map_key_value 一定是2的倍数，且满足kay,value的迭代。
     */
    public void onEvent(Context cx, String eventId, String... map_key_value) {
        int n = map_key_value == null ? 0 : map_key_value.length;
        if (n == 0) {
            onEvent(cx, eventId);
        } else {
            HashMap<String, String> map = new HashMap<String, String>(n / 2 + 1);
            for (int i = 0; i < n; i += 2) {
                map.put(map_key_value[i], map_key_value[i + 1]);
            }
            onEvent(cx, eventId, map);
        }
    }


    public void onPageStart(String title) {
//        MobclickAgent.onPageStart(title);
        LogUtils.d(TAG, "onPageStart-->title=" + title);
    }

    public void onPageEnd(String title) {
//        MobclickAgent.onPageEnd(title);
        LogUtils.d(TAG, "onPageEnd-->title=" + title);
    }

    public void onResume(Context cx) {
//        MobclickAgent.onResume(cx);
        LogUtils.d(TAG, "onResume-->aty=" + cx.getClass().getSimpleName());
    }

    public void onPause(Context cx) {
//        MobclickAgent.onPause(cx);
        LogUtils.d(TAG, "onPause-->aty=" + cx.getClass().getSimpleName());
    }

    public void pushLastTrade(String code, String from) {
        if (TextUtils.isEmpty(code)) {
            Analytics.INSTANCE.getMapObj().put("LAST_TRADE_CODE", code);
            Analytics.INSTANCE.getMapObj().put("LAST_TRADE_FROM", from);
        }
    }

    /**
     * 统计计时
     */
    public void countBrowseTime(Context cx, String eventName, long startTime) {
        long end = System.currentTimeMillis();
        String stayTime = "";
        long time = (end - startTime) / 1000;
        if (time < 5) {
            stayTime = "小于5秒";
        } else if (time >= 5 && time < 30) {
            stayTime = "5-30秒";
        } else if (time >= 30 && time < 60) {
            stayTime = "30-1分钟";
        } else {
            stayTime = "大于1分钟";
        }
        onEvent(cx, eventName, KEY_STAY_TIME, stayTime);
    }

    @Override
    public void clickHbShareIconReport(Context context, int platformType) {
        if (platformType == 1) {
            //微信
            onClick(context, "91010");
        } else if (platformType == 2) {
            //朋友圈
            onClick(context, "91020");
        } else if (platformType == 5) {
            //QQ
            onClick(context, "91030");
        } else if (platformType == 9) {
            //企业微信
            onClick(context, "91050");
        }
    }

    //product id
    private static final String HB_PRODUCT_ID = "3002";
    /**
     * FundApp的map种保存的oaid的参数key
     */
    private static final String KEY_OAID = "KeyOaid";

    @Override
    public void initHbAnalytics(boolean enable, String proId, String hboneNo, String patternFragName, String uacUrl, boolean debugType, boolean debugLog) {
        HbAnalyticsConfigs.init(proId, patternFragName);
        HbAnalyticsConfigs.setHboneNo(hboneNo);
        Configs.enableDebug(debugType);
        Configs.enableLog(debugLog);
        Configs.enableReport(enable);
        Configs.setHostUrl(uacUrl);
    }

    @Override
    public void setHbAnalyticsEnable(boolean enable) {
        Configs.enableReport(enable);
    }


    /**
     * active
     * 登录后,需要对hboneNo进行赋值操作
     *
     * @param activeType ACTIVE_TYPE
     */
    @Override
    public void onActive(ACTIVE_TYPE activeType, String hboneNo, boolean isPrivacyGranted) {
        doReportActive(activeType, hboneNo, "");
    }

    @Override
    public void setCoopId(String coopId) {
        Analytics.INSTANCE.setCoopId(coopId);
    }

    private void doReportActive(ACTIVE_TYPE activeType, String hboneNo, String oaid) {
        //call acctChange
        if (!TextUtils.isEmpty(hboneNo)) {
            HbAnalyticsConfigs.setHboneNo(hboneNo);
        }
        EventCreate.onActive(GlobalApp.getApp(), activeType, Analytics.INSTANCE.getCoopId(), oaid);
    }

    /**
     * non default create tag
     *
     * @param hTag clickId
     */
    @Override
    public void cacheTag(String hTag) {
        EventCache.getInstance().cacheHTag(hTag);
    }


    /**
     * 手动统计tag
     */
    @Override
    public void onPageEventEventTag(FragmentActivity activity, TagBuild tagBuild, String fId, String url) {
        EventCreate.onPageEvent(activity, tagBuild, fId, url);
    }

    /**
     * 统计点击事件
     */
    @Override
    public void onClick(Context context, String clickId, String... ext) {
        if (!TextUtils.isEmpty(clickId)) {
            EventCreate.onClick(context, clickId, ext);
        }
    }

    @Override
    public void init(Context context) {

    }
}
