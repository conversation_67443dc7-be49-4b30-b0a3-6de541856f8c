package com.howbuy.h5;


/**
 * Created by tao.liang on 2016/1/29.
 * h5 资源包中配置的 h5页面对应的key
 */
public class H5UrlKeyConfig {
    public static final int FROM_H5_UPDATE = 1;
    public static final String ACTION_COMMON_HTML_UPDATE = "ACTION_COMMON_HTML_UPDATE";
    public static final String H5_UPDATE_NEED_CLEAN_CACHE = "cleanH5Cache";

    public static final String URL_WXCB = "/subject/wxcb210120/index.html"; // 微信引号页 绑定和关注公众号
    //fund-main, fund-setting
    public static final String URL_USER_SERVICE_PROTOCOL = "subject/zjh5/agreement201601/dzjyfwxy.html"; //用户服务协议
    //fund-main, fund-setting, login_impl
    public static final String URL_USER_PRIVATE_PROTOCOL = "/subject/zjh5/agreement201601/hmjjyszczy.html"; //用户隐私协议

    public final static String TI_BUY_FUND = "TI_BUY_FUND"; //买基金
    public final static String TI_SELL_FUND = "TI_SELL_FUND"; //卖基金
    public final static String TI_MODIFY_BONUS_METHOD = "TI_MODIFY_BONUS_METHOD"; //修改分红方式
    public final static String TI_MODIFY_AIP_CONTRACT = "TI_MODIFY_AIP_CONTRACT"; //修改定投合约
    public final static String TI_FIXED_INVESTMENT_NEW = "TI_FIXED_INVESTMENT_NEW";
    public final static String TI_FIXED_INVESTMENT = "TI_FIXED_INVESTMENT"; //普通定投
    public final static String TI_CREATE_OBJSURPLUS = "TI_CREATE_OBJSURPLUS"; //  创建目标止盈
    public final static String TI_CREATE_MOVSURPLUS = "TI_CREATE_MOVSURPLUS"; //  创建移动止盈
    public final static String TI_CHNANGE_MOVSURPLUS = "TI_CHNANGE_MOVSURPLUS"; //  修改移动止盈
    public final static String TI_CHANGE_OBJSURPLUS = "TI_CHANGE_OBJSURPLUS"; //  修改目标止盈

    public final static String TI_SAVE_MONEY = "TI_SAVE_MONEY"; //存钱
    public final static String TI_DRAW_MONEY = "TI_DRAW_MONEY"; //取钱
    public final static String TI_CURRENT_HOLD_NEW = "CURRENT_HOLD_NEW"; //储蓄罐活期资产页
    public final static String TI_FUND_EXCHANGE = "TI_FUND_EXCHANGE"; //转换
    public final static String FORWARD_PAGE = "FORWARD_PAGE";// 新的转投
    public final static String TI_RISK_EVALUATION = "TI_RISK_EVALUATION"; //风险评测
    public final static String TI_HBFD_TRADE_RECORD = "TI_HBFD_TRADE_RECORD"; //掌机交易记录
    public final static String TI_PGBK_TRADE_RECORD = "TI_PGBK_TRADE_RECORD"; //储蓄罐交易记录
    public final static String TI_PGBK_TRADE_DETAIL = "TI_PGBK_TRADE_DETAIL"; //储蓄罐交易详情
    public final static String TI_QUICK_SELL = "TI_QUICK_SELL"; //快速卖出
    public final static String TI_MY_SIMU_FUND = "TI_MY_SIMU_FUND"; //私募持仓首页
    public final static String TI_BUY_SIMU_FUND = "TI_BUY_SIMU_FUND"; //私募买
    public final static String TI_BATCH_EXCHANGE = "TI_BATCH_EXCHANGE"; //调仓
    public final static String NEW_TI_FIXED_PRODUCT_LIST = "NEW_TI_FIXED_PRODUCT_LIST"; //固收产品列表
    public final static String TI_ZH_TRADE_RECORD_NEW = "TI_ZH_TRADE_RECORD_NEW"; //组合交易记录(交易记录)
    public final static String TI_ORDER_SIMU_FUND = "TI_ORDER_SIMU_FUND"; //私募 我的预约
    public final static String NEW_DT_COMPUTE_STATEMENT = "NEW_DT_COMPUTE_STATEMENT"; //智定投6.0.9add;
    public final static String PORTFOLIO_CHANGEPLAN = "PORTFOLIO_CHANGEPLAN"; //货币组合修改
    public final static String SHORTBUND_FIXEDINVEST = "SHORTBUND_FIXEDINVEST"; //短债组合修改
    public final static String T0_CURRENCY_SPECIAL = "T0_CURRENCY_SPECIAL";//H5-T+0货币专区
    public final static String BANKCARD_DAIKOU = "BANKCARD_DAIKOU";//银行自动转账授权书 V7.6.0

    /**
     * 用户信息确认（反洗钱）
     */
    public static final String PAGE_USERINFO_CONFIRM = "TI_USERINFO_CONFIRM";


    //资产页面持仓item
    public final static String REGULAR_ASSIST = "REGULAR_ASSIST"; //定期持仓
    public final static String HQPLUS_ASSIST = "HQPLUS_ASSIST"; //活期持仓
    public final static String HAODOU_NEW = "HAODOU_NEW"; //好豆

    //协议
    public final static String HELP_NEW = "HELP_NEW"; //帮助中心

    public final static String INVITE_FRIENDS = "INVITE_FRIENDS"; //邀请好友
    public final static String RISK_HIGH_PROTOCOL = "RISK_HIGH_PROTOCOL"; //高风险提示H5页面
    public final static String NEW_PUBLIC_NOTICE = "NEW_PUBLIC_NOTICE";//主备机房上线预埋公告页h5配置地址

    public final static String NEW_POPUP_TIP = "NEW_POPUP_TIP"; //H5弹出框
    public final static String COUPON = "COUPON"; //优惠券的url
    public final static String MY_MEDAL = "MEDAL";//我的勋章url
    public final static String REGULAR_ZERO = "REGULAR_ZERO"; //定投专区

    public static final String ROBOT_HOME = "ROBOT_HOME";//机器人详情页
    public static final String MEMBER = "MEMBER";//会员中心
    public static final String NEWUSER = "NEWUSER";//新手专区
    public static final String NEW_NEWUSER_DIALOG = "NEW_NEWUSER_DIALOG";//红包弹框
    public static final String NEW_RECOMMED_DIALOG = "NEW_RECOMMED_DIALOG";//好买推荐更新弹框

    public static final String NEW_APPOINT_LIST = "NEW_APPOINT_LIST"; //预约记录
    public static final String NEW_APPOINT_SALE_FUND = "NEW_APPOINT_SALE_FUND"; //新增或预约卖出
    public static final String MY_RESERVATION = "MY_RESERVATION"; //我的预约
    public static final String MY_POLICY_SIMU_FUND = "MY_POLICY_SIMU_FUND";//我的保单
    public final static String MEDAL_DETAIL = "MEDAL_DETAIL";//勋章详情
    public static final String NEW_REGULAR_LIST = "NEW_REGULAR_LIST";//定投合约
    public static final String TI_MY_SIMU_ASSET = "TI_MY_SIMU_ASSET";//上传资产证明
    public static final String TI_MY_SIMU_CONTRACT = "TI_MY_SIMU_CONTRACT";//合同管理
    public static final String LAST_INCOME_DIALOG = "LAST_INCOME_DIALOG";//我的页面显示最新收益用


    // 通用的h5页面 包括机器人 货币赢+ 等这个是客户端自定义的，具体交易key由服务端下发
    public static final String PORTFOLIO_ASSET = "PORTFOLIO_ASSET";
    // 我的页面-跳转到H5页面，不检查用户信息
    public static final String PORTFOLIO_ASSET_NO_ACCESS = "PORTFOLIO_ASSET_NO_ACCESS";
    //开户和修改税收这两个key新增了使用新规则的版本，需要app调用新key名方便测试
    public static final String OPEN_ACCOUNT = "NEW_OPEN_ACCOUNT";
    public static final String ACCOUNT_TAXATION_ANNOUNCEMENT = "NEW_SELECT_FISCAL_RESIDENT";
    //定期产品档案页面
    public static final String REGULAR_DETAIL = "REGULAR_DETAIL";
    //活期+ 产品档案页面
    public static final String HQPLUS_DETAIL = "HQPLUS_DETAIL";
    //主动平衡
    public static final String SUPER_HOUSE = "SUPER_HOUSE";

    public static final String SHORT_H5_KEY = "SHORT_H5_KEY";

    public static final String ONLY_FOR_APP = "ONLY_FOR_APP"; //判断js方法是否存在


    public static final String HBENV_SETTINGS = "HBENV_SETTINGS"; //打开地址修改H5文件路径

    /**
     * 牛基宝用户留言
     */
    public static final String NJB_COMMENTS = "SUPER_FUND_USER_COMMENTS";

    // 自建组合档案页
    public static final String ZJZH_PRODUCT_DETAIL = "ZJZH_PRODUCT_DETAIL";
    // 自建组合持仓页
    public static final String ZJZH_ASSET_DETAIL = "ZJZH_ASSET_DETAIL";
    // 牛基宝档案页
    public static final String TZZH_PRODUCT_DETAIL = "TZZH_PRODUCT_DETAIL";
    // 牛基宝持仓页
    public static final String TZZH_ASSET_DETAIL = "TZZH_ASSET_DETAIL";
    // 创建自建组合
    public static final String ZJZH_CREATE_PROCESS = "ZJZH_CREATE_PROCESS";
    // 创建定投页面
    public static final String CREATE_CONTRACT = "CREATE_CONTRACT";
    // 定投合约详情页
    public static final String CONTRACT_DETAIL = "CONTRACT_DETAIL";
    // 买入页
    public static final String ADVISER_BUY = "ADVISER_BUY";
    // 卖出页
    public static final String ADVISER_SELL = "ADVISER_SELL";
    public static final String BUSINESSCOLLEGE_STUDY_CENTER = "BUSINESSCOLLEGE_STUDY_CENTER"; // 学习中心
    public static final String BUSINESSCOLLEGE_SEARCH = "BUSINESSCOLLEGE_SEARCH"; // 搜索

    /**
     * 储蓄罐冻结纪录
     */
    public static final String CXG_FROZEN_RECORD = "FREEZING_RECORDS";

    /**更换银行卡*/
    public static final String EXCHANGE_CHRD_INDEX = "EXCHANGE_CHRD_INDEX";
    /**换卡详情*/
    public static final String EXCHANGE_CHRD_DETAIL = "EXCHANGE_CHRD_DETAIL";
    /**换卡记录*/
    public static final String EXCHANGE_CHRD_NOTES = "EXCHANGE_CHRD_NOTES";
    /**常见问题*/
    public static final String EXCHANGE_CHRD_QUESTION = "EXCHANGE_CHRD_QUESTION";
    /**税延换卡页面*/
    public static final String TAX_FUND_UPDATE_CARD = "TAX_FUND_UPDATE_CARD";
    /**税延养老-银行卡修改预留手机号H5页面*/
    public static final String TAX_FUND_UPDATE_PHONE = "TAX_FUND_UPDATE_PHONE";

    // 持仓份额列表页替换为H5后
    public static final String FUND_HOLD_SHARES = "FUND_HOLD_SHARES";
    // 卖出key
    public static final String SELL_FUND_CONVERT = "SELL_FUND_CONVERT";
    // 转投key
    public static final String SELL_CONVERT_FUND = "SELL_CONVERT_FUND";
    // 余额统计的key
    public static final String OTHER_INCREASEBALANCE = "OTHER_INCREASEBALANCE";

    //定投计算器H5的key
    public static final String REGULAR_CALCULATOR = "REGULAR_CALCULATOR";

    //份额转移 V7.6.0
    public static final String TO_EXCHANGE_PORTION = "TO_EXCHANGE_PORTION";

    /**
     * 微信公众号促绑页h5key
     * @since 7.7.0
     * */
    public static final String TO_WXPROMOTE_ACTIVITY_HOME= "TO_WXPROMOTE_ACTIVITY_HOME";
    /**章节详情页-分享*/
    public static final String COLLEGE_LAST_CHECK_ON = "COLLEGE_LAST_CHECK_ON";

    /**
     * 建行直连
     */
    public static final String CCB_SIGN = "CCB_SIGN";
    /**
     * 定投扣款顺序设置页面
     */
    public static final String ORDER_OF_DEDUCTION = "ORDER_OF_DEDUCTION";

    /**
     * v8.1.0-止盈合约
     */
    public static final String FUND_FIX_STOP = "FUND_FIX_STOP";

    /**
     * v8.1.0-普通定投合约
     */
    public static final String FUND_FIX_NORMAL = "FUND_FIX_NORMAL";

    /**
     *
     */
    public static final String TI_ABOUT_HOWBUY = "TI_ABOUT_HOWBUY";
    /**
     * 我的页面-养老专区
     */
    public static final String ELDERLY_CARE_ZONE = "ELDERLY_CARE_ZONE";
    /**
     * 个人养老基金卖出
     */
    public static final String SELL_FUND_TAX = "SELL_FUND_TAX";

    /**
     * 研习社-等级说明页
     * @since 8.1.8
     */
    public static final String COLLEGE_USER_LEVEL = "COLLEGE_USER_LEVEL";

    /**
     * pk添加页
     */
    public static final String FUND_PK_INDEX = "FUND_PK_INDEX";

    /**
     * pk详情页
     */
    public static final String FUND_PK_RESULT = "FUND_PK_RESULT";

    /**
     * 基金诊断
     */
    public static final String FUND_DIAGS_DETAIL = "FUND_DIAGS_DETAIL";

    /**
     * 私募授权页h5 key
     */
    public static final String TI_MY_SIMU_EMPOWER = "TI_MY_SIMU_EMPOWER";

    /**好臻个人中心H5 key*/
    public static final String HAOZHEN_ACCOUNT_MESSAGE = "HAOZHEN_ACCOUNT_MESSAGE";
    /**假开户页面H5 key*/
    public static final String HAOZHEN_OPEN_ACCOUNT = "HAOZHEN_OPEN_ACCOUNT";
    /**好臻合格投资者认定H5 key*/
    public static final String RISK_KYC_HZ = "RISK_KYC_HZ";
    /**好臻风险测评引导页H5 key*/
    public static final String RISK_GUIDE_HZ = "RISK_GUIDE_HZ";
    /**好臻风险测评授权页H5 key*/
    public static final String RISK_AUTH_HZ = "RISK_AUTH_HZ";
    /**批量买入*/
    public static final String BATCH_BUY_INDEX = "BATCH_BUY_INDEX";
    /**本地跳转到H5身份证OCR*/
    public static final String UPLOAD_IDENTITY_CARD = "UPLOAD_IDENTITY_CARD";
    /**
     * 私募单基金持仓页-阳光私募
     * fundCode：基金code；
     * productsubtype：基金子类型；4 阳光私募
     * hkSaleFlag：是否香港基金；1 是 0 否
     * sfhwcxg：是否海外储蓄罐；1 是 0 否
     * disCode：基金分销渠道    HB000A001-好买   HZ开头-好臻
     */
    public static final String TI_MY_SIMU_FUNDDETAIL = "TI_MY_SIMU_FUNDDETAIL";

    /**
     * 好臻下单页
     * productCode：基金code
     */
    public static final String HAOZHEN_TRADE_ORDER = "HAOZHEN_TRADE_ORDER";
    /**
     * 收货地址
     */
    public static final String ACCOUNT_MAIL_ADDRESS = "ACCOUNT_ADDRESS";

    /**章节详情页-课程简介*/
    public static final String SXY_LESSON_PROFILE = "SXY_LESSON_PROFILE";

    /**
     * 设置提醒
     * @since 8.5.0
     */
    public static final String SIGNAL_REMINDER_SETTING = "SIGNAL_REMINDER_SETTING";
    /**
     * 持仓分析
     *
     * @since 8.5.4
     */
    public static final String HOLD_ANALYSIS_PAGE = "HOLD_ANALYSIS_PAGE";

    /**份额升级说明页*/
    public static final String FUND_UPGRADE_NOTES = "FUND_UPGRADE_NOTES";
    /**
     * 资产收益分析-收益明细-更多明细
     */
    public static final String ASSETS_INCOME_LIST = "ASSETS_INCOME_LIST";
    /**
     * 已清仓列表
     * active: CL(策略组合),ZJ(自建组合,JJ(公募基金)
     */
    public static final String ASSETS_INCOME_CLEAR_LIST = "ASSETS_INCOME_CLEAR_LIST";

    /**
     * 单基金资产收益明细
     * productType:JJ
     * fundCode
     */
    public static final String ASSETS_INCOME_DETAIL = "ASSETS_INCOME_DETAIL";
    /**
     * 单基金持仓页冻结明细
     * fundCode
     */
    public static final String FUND_DETAIL_FROZEN = "FUND_DETAIL_FROZEN";

    /**
     * 循环锁定期的卖出页面
     * 参数：fundCode 基金代码
     */
    public static final String FUND_SELL_FOR_LOCKER = "FUND_SELL_FOR_LOCKER";

    /**
     * 支付签约-银行直连，操作手册H5页面
     * @since 支付签约-银行直连，2025-02-20
     */
    public static final String BOC_OPERATE_MANUAL = "BOC_OPERATE_MANUAL";
}