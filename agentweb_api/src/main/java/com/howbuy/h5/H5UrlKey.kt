package com.howbuy.h5

/**
 * @Description H5 url key
 * <AUTHOR>
 * @Date 2024/2/28
 * @Version V1.0
 */
object H5UrlKey {

    /**好买全球隐私政策指引*/
    const val PRI_POLICY_IDX = "PRI_POLICY_IDX"

    /**开户引导页*/
    const val OPEN_ACCOUNT_IDX = "OPEN_ACCOUNT_IDX"

    /**开户资料填写?isEdit=1&stepFlag=6*/
    const val OPEN_ACCOUNT = "OPEN_ACCOUNT"

    /**开户结果页*/
    const val OPEN_ACCOUNT_STATUS = "OPEN_ACCOUNT_STATUS"

    /**入金引导页*/
    const val DEPOSITS_IDX = "DEPOSITS_IDX"

    /**打款凭证查看页*/
    const val DEPOSITS_DETAIL = "DEPOSITS_DETAIL"

    /**打款凭证修改页*/
    const val DEPOSITS_UPDATE = "DEPOSITS_UPDATE"

    /**我的资产页*/
    const val INFO_ASSET_LIST = "INFO_ASSET_LIST"

    /**一账通授权管理*/
    const val ONE_ACCOUNT_AUTH = "ONE_ACCOUNT_AUTH"

    /**交易记录详情页*/
    const val TRADE_LIST_DETAIL = "TRADE_LIST_DETAIL"

    /**交易记录页（交易记录列表页）?tradeStatus=1(在途tab)*/
    const val TRADE_LIST_IDX = "TRADE_LIST_IDX"

    /**首页(签约列表页)（tab选中筛选）*/
    const val SIGN_PAGE = "SIGN_PAGE"

    /**实名信息页*/
    const val REAL_USER_INFO = "REAL_USER_INFO"

    /**风险测评引导页?from=person*/
    const val RISK_EVA_IDX = "RISK_EVA_IDX"

    /**风险测评结果页?from=person*/
    const val RISK_EVA_RESULT = "RISK_EVA_RESULT"

    /**专业投资者认证页(审核结果页)*/
    const val PRO_INVESTORS_CHECK = "PRO_INVESTORS_CHECK"

    /**专业投资者认证页(上传材料页)*/
    const val PRO_INVESTORS_IDX = "PRO_INVESTORS_IDX"

    /**修改手机号页面*/
    const val UPDATE_MOBILE = "UPDATE_MOBILE"

    /**修改邮箱页面*/
    const val UPDATE_EMAIL = "UPDATE_EMAIL"

    /**关于页面*/
    const val ABOUT_LIST = "ABOUT_LIST"

    /**银行卡列表*/
    const val USER_BANK_LIST = "USER_BANK_LIST"

    /**合同文件查询页*/
    const val CONTRACT_LIST = "CONTRACT_LIST"

    /**开户文件查询页*/
    @Deprecated("已废弃,无该功能入口")
    const val ACCOUNT_FILE_LIST = "ACCOUNT_FILE_LIST"

    /**产品合同查询页*/
    const val POCT_SEARCH = "POCT_SEARCH"

    /**
     * 专业投资者认证页(认证状态页)
     */
    const val PRO_INVESTORS_STATUS = "PRO_INVESTORS_STATUS"

    /**下单页*/
    const val ORDER_BUY = "ORDER_BUY"

    /**设置登录密码*/
    const val SET_LOGIN_PWD = "SET_LOGIN_PWD"

    /**修改登录密码*/
    const val UPDATE_LOGIN_PWD = "UPDATE_LOGIN_PWD"

    /**设置交易密码-邮箱*/
    const val SET_TRADE_PWD_EMAIL = "SET_TRADE_PWD_EMAIL"

    /**设置交易密码-手机*/
    const val SET_TRADE_PWD_PHONE = "SET_TRADE_PWD_PHONE"

    /**设置交易密码-手机+邮箱*/
    const val SET_TRADE_PWD_PHONE_EMAIL = "SET_TRADE_PWD_PHONE_EMAIL"

    /**修改交易密码*/
    const val UPDATE_TRADE_PWD = "UPDATE_TRADE_PWD"

    /**注销账户*/
    const val MEMBER_LOG_OFF = "MEMBER_LOG_OFF"


    /**
     * 储蓄罐签约详情页 --add 2.2
     */
    const val CXG_SIGN_DETAIL = "CXG_SIGN_DETAIL"

    /**
     * 储蓄罐专区详情页（海外储蓄罐服务）--add 2.2
     */
    const val CXG_ZONE_DETAIL = "CXG_ZONE_DETAIL"

    /**
     * 储蓄罐现金存入凭证查询列表页 --add 2.2
     */
    const val ASSET_PROOF_LIST = "ASSET_PROOF_LIST"

    /**
     * 上传打款凭证页（查看） --add 2.2
     */
    const val CXG_VIEW_ASSET_PROOF = "CXG_VIEW_ASSET_PROOF"

    /**
     * 上传打款凭证页（修改） --add 2.2
     */
    const val CXG_UPDATE_ASSET_PROOF = "CXG_UPDATE_ASSET_PROOF"

    /**
     * 上传打款凭证页（存一笔） --add 2.2
     */
    const val CXG_UPLOAD_ASSET_PROOF = "CXG_UPLOAD_ASSET_PROOF"

    /**
     * 衍生品 -- 2.5.2
     */
    const val DERIVATIVE_IDX = "DERIVATIVE_IDX"

    /**
     * 个人中心
     */
    const val PERSONAL_CENTER = "PERSONAL_CENTER"

    /**
     * 帮助中心
     */
    const val HELP_CENTER = "HELP_CENTER"

    /**
     * 意见反馈
     */
    const val FEEDBACK = "FEEDBACK"

    /**
     * 分享APP
     * https://mzt.howbuy.com/subject/xkzh1909/aboutus.html
     */
    const val SHARE_APP = "https://mzt.howbuy.com/subject/xkzh1909/aboutus.html"

}