plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'

    id 'com.alibaba.arouter' //arouter
}

apply from: "${rootProject.projectDir}/arouter-kotlin-config.gradle"

android {
    namespace 'com.howbuy.global.user'
    compileSdkVersion rootProject.ext.commonVersions.compileSdkVersion
    buildToolsVersion rootProject.ext.commonVersions.buildToolsVersion
    defaultConfig {
        minSdkVersion rootProject.ext.commonVersions.minSdkVersion
        targetSdkVersion rootProject.ext.commonVersions.targetSdkVersion
    }
}

dependencies {
    rootProject.ext.depUtil.implementation('data-api', dependencies)
    rootProject.ext.depUtil.implementation('login-api', dependencies)
    rootProject.ext.depUtil.implementation('user-api', dependencies)
    rootProject.ext.depUtil.implementation('global-common', dependencies)
    rootProject.ext.depUtil.implementation('global-base', dependencies)
    rootProject.ext.depUtil.implementation('cmd_api', dependencies)
    rootProject.ext.depUtil.implementation('analytics_api', dependencies)
    rootProject.ext.depUtil.implementation('hBRouter', dependencies)
    rootProject.ext.depUtil.implementation('agentweb_api', dependencies)
    rootProject.ext.depUtil.implementation('hBLCommon', dependencies)
    rootProject.ext.depUtil.implementation('hBComponent', dependencies)
    rootProject.ext.depUtil.implementation('arouter_intercept_api', dependencies)

    implementation rootProject.ext.dependencies['user_data_api']
    implementation(rootProject.ext.dependencies['loginobserver'])
    //PV埋点注解
    implementation rootProject.ext.dependencies['analytics-annotation']
//    rootProject.ext.depUtil.addDependency('analytics_api', dependencies)
    implementation rootProject.ext.dependencies['hb-analysis']
    //解除依赖传递---------------------------------------
    implementation(rootProject.ext.dependencies['hb-dialog'])
    implementation(rootProject.ext.dependencies['hb-net'])
    implementation rootProject.ext.dependencies['recyclerView-adapter-helper']
    implementation rootProject.ext.dependencies['hb-gesture']
    implementation rootProject.ext.dependencies['permission']
    implementation rootProject.ext.dependencies['hb-refresh']
    implementation rootProject.ext.dependencies['hbBanner']
    implementation rootProject.ext.dependencies['constraint-layout']

    implementation fileTree(include: ['*.jar'], dir: 'libs')
    testImplementation 'junit:junit:4.12'
}