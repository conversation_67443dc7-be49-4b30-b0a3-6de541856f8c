package com.howbuy.global.user.userinfo

import com.howbuy.account.api.ApiConfig
import com.howbuy.account.serialize.JsonSerializer
import com.howbuy.global.data_api.DataIds
import com.howbuy.global.user.entity.FollowWeChatBean
import com.howbuy.global.user.entity.MessageData
import com.howbuy.global.user.entity.UserLicensee
import com.howbuy.global.user.entity.UserHoldInfo
import com.howbuy.global.user.message.UserMessageFetcher

/**
 * Api配置工厂
 * <AUTHOR>
 * @date 2024/2/22
 * @version V1.0
 */
class ApiConfigFactory {

    /**
     * 用户信息配置
     * @return ApiConfig<*>
     */
    fun createUserInfoConfig(): ApiConfig<*> {
        return ApiConfig(
            DataIds.ID_USER_INFO,
            true,
            UserInfo::class.java,
            UserInfoFetcher(),
            JsonSerializer(UserInfo::class.java)
        )
    }

    /**
     * 用户资产
     */
    fun createUserAssetsConfig(): ApiConfig<*> {
        return ApiConfig(
            DataIds.ID_USER_ASSETS,
            true,
            UserAssets::class.java,
            UserAssetsFetcher(),
            JsonSerializer(UserAssets::class.java)
        )
    }

    /**
     * 用户持仓列表
     */
    fun createUserHoldConfig(): ApiConfig<*> {
        return ApiConfig(
            DataIds.ID_USER_HOLD,
            true,
            UserHoldInfo::class.java,
            UserHoldInfoFetcher(),
            JsonSerializer(UserHoldInfo::class.java)
        )
    }

    /**
     * 消息中心
     */
    fun createUserMessageConfig(): ApiConfig<*> {
        return ApiConfig(
            DataIds.ID_USER_MESSAGE,
            true,
            MessageData::class.java,
            UserMessageFetcher(),
            JsonSerializer(MessageData::class.java)
        )
    }

    /**
     * 持牌人
     */
    fun createSmTgConfig(): ApiConfig<*> {
        return ApiConfig(
            DataIds.ID_USER_LICENSEE,
            true,
            UserLicensee::class.java,
            UserLicenseeFetcher(),
            JsonSerializer(UserLicensee::class.java)
        )
    }

    fun createUserBindConfig(): ApiConfig<*> {
        return ApiConfig(
            DataIds.ID_USER_WECHAT_BIND,
            false,
            FollowWeChatBean::class.java,
            FollowWeChatFetcher(),
            JsonSerializer(FollowWeChatBean::class.java)
        )
    }
}