package com.howbuy.global.user

import android.app.Activity
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.text.Spanned
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.core.content.ContextCompat
import androidx.core.net.toUri
import androidx.recyclerview.widget.LinearLayoutManager
import com.howbuy.account.UserDataHelper
import com.howbuy.account.api.DataWrapper
import com.howbuy.account.api.UserDataObserver
import com.howbuy.account.remote.FetchAction
import com.howbuy.analytics.PvReportUtils
import com.howbuy.android.analytics.annotation.pv.PvInfo
import com.howbuy.arouter_intercept_api.IGlobalInterceptCode
import com.howbuy.fund.base.IWechatProvider
import com.howbuy.fund.base.analytics.HbAnalytics
import com.howbuy.fund.base.aty.AtyEmpty
import com.howbuy.fund.base.config.SpConfig
import com.howbuy.fund.base.config.ValConfig
import com.howbuy.fund.base.dialog.GlobalHintDialog
import com.howbuy.fund.base.entity.WeChatLoginResult
import com.howbuy.fund.base.frag.AbsFragViewBinding
import com.howbuy.fund.base.nav.NavHelper
import com.howbuy.fund.base.push.PushDispatchHelper
import com.howbuy.fund.base.router.RouterHelper
import com.howbuy.fund.base.storage.CommonStorageUtils
import com.howbuy.fund.base.utils.FundTextUtils
import com.howbuy.fund.base.utils.ShapeCreator
import com.howbuy.fund.base.utils.span.SpannableItem
import com.howbuy.fund.base.utils.span.SpannableUtils
import com.howbuy.fund.base.widget.CenterImageSpan
import com.howbuy.fund.base.widget.RadiusSpan
import com.howbuy.fund.base.widget.xrecyclerdivider.builder.XLinearBuilder
import com.howbuy.fund.common.floatwindow.FloatWindow
import com.howbuy.fund.common.floatwindow.ITag
import com.howbuy.fund.net.error.WrapException
import com.howbuy.fund.net.util.HandleErrorMgr
import com.howbuy.global.common.banner.BannerImageAdp
import com.howbuy.global.data_api.DataIds
import com.howbuy.global.data_api.apiUserInfo
import com.howbuy.global.data_api.get
import com.howbuy.global.data_api.getHkCustNo
import com.howbuy.global.data_api.isLogined
import com.howbuy.global.login_api.ILoginProvider
import com.howbuy.global.login_api.LoginCallback
import com.howbuy.global.login_api.LoginParams
import com.howbuy.global.login_api.LoginResult
import com.howbuy.global.login_api.LoginRouterPath
import com.howbuy.global.login_api.LoginType
import com.howbuy.global.user.ad.AdItem
import com.howbuy.global.user.ad.AdResult
import com.howbuy.global.user.amtdesc.AmtDescTitle
import com.howbuy.global.user.amtdesc.MineAmtDescDlg
import com.howbuy.global.user.amtdesc.QxnProdBody
import com.howbuy.global.user.announcement.Announcement
import com.howbuy.global.user.announcement.AnnouncementHelper
import com.howbuy.global.user.announcement.AnnouncementResult
import com.howbuy.global.user.databinding.FragMineBinding
import com.howbuy.global.user.entity.AboutUsItemInfo
import com.howbuy.global.user.entity.MessageData
import com.howbuy.global.user.entity.MessageItem
import com.howbuy.global.user.message.MessageLauncher
import com.howbuy.global.user.settings.AmtType
import com.howbuy.global.user.userinfo.ApiImplUserInfo
import com.howbuy.global.user.userinfo.UserAssets
import com.howbuy.h5.H5UrlKey
import com.howbuy.lib.compont.GlobalApp
import com.howbuy.lib.utils.ColorUtils
import com.howbuy.lib.utils.DateUtils
import com.howbuy.lib.utils.DensityUtils
import com.howbuy.lib.utils.LogUtils
import com.howbuy.lib.utils.MathUtils
import com.howbuy.lib.utils.StatusBarUtil
import com.howbuy.lib.utils.SysUtils
import com.howbuy.lib.utils.TradeUtils
import com.howbuy.lib.utils.ViewUtils
import com.howbuy.login.LoginManagerImpl
import com.howbuy.login.LoginObserver
import com.howbuy.login.LogoutObserver
import com.howbuy.router.provider.IWebProvider
import com.howbuy.router.proxy.Invoker
import java.math.BigDecimal

/**
 * @Description 我的页面
 * <AUTHOR>
 * @Date 2024/2/18
 * @Version V1.0
 */
@PvInfo(pageId = "351250", level = "1", name = "个人中心页", className = "FragMine")
class FragMine : AbsFragViewBinding<FragMineBinding>(), AnnouncementHelper.INoticeInvoke {
    override fun createViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragMineBinding {
        return FragMineBinding.inflate(inflater, container, false)
    }

    override fun getFragLayoutId() = R.layout.frag_mine

    //弹框说明中:千喜年购买产品数据
    private var mQxnProdBody: QxnProdBody? = null

    /**
     * 未读消息和待办消息 数量
     */
    private var todoNum: Int = 0
    private var unReadNum: Int = 0
    private val mAnnounceHelper = AnnouncementHelper(this)

    //微信登录成功刷新
    private var refreshByWeChatLogin = false

    /**
     * 开户引导弹窗描述
     */
    private var dlgDesc = ""

    /**
     * 开户引导弹窗按钮文案
     */
    private var btnTxt = ""

    /**
     * 开户引导弹窗跳转
     */
    private var btnLink = ""
    private var linkParam: HashMap<String, String>? = null

    private var mUserReqFinish = false
    private var mAssetsReqFinish = false

    //当前币种, 无值时默认: 美元
    private var mCurAmtType: AmtType? = AmtType.DOLLAR

    //控制小眼睛与资产接口时间差,导致的状态不及时问题
    private var userAssetsReqing = false
    private var eyeShowWhenUserClick = true

    //只有当正在请求过程中, 点击了眼睛,才会变成2
    private var userAssetCallbackCount = 0


    override fun stepAllViews(root: View?, savedInstanceState: Bundle?) {
        super.stepAllViews(root, savedInstanceState)
        (activity as AtyEmpty?)?.setToolbarVisibility(false)
        StatusBarUtil.translucentStatusBar(activity, true)
        binding.vStatusbar.minimumHeight = SysUtils.getStatusBarHeight(activity)

        binding.refreshLayout.setOnRefreshListener {
            refreshData()
        }
        initUnlogin()

        binding.layoutLogin.banner.registerLifecycleObserver(lifecycle)
            .setIndicatorMargin(0, 0, 0, DensityUtils.dp2px(5f))
            .setAdapter(object : BannerImageAdp<AdItem>(
                binding.layoutLogin.banner.context,
                null,
                object : DataTransform<AdItem> {
                    override fun transform(data: AdItem): BaseBannerItem {
                        return BaseBannerItem(data.adImg, data.onClick)
                    }
                }
            ) {
                override fun analytics(position: Int, data: AdItem) {
                    HbAnalytics.onClick("610310", data?.adTitle, data?.onClick)
                }

                override fun onItemClick(position: Int, data: AdItem): Boolean {
                    BannerClickMgr.onBannerClick(this@FragMine, data)
                    return true
                }
            })


        UserDataHelper.getDataManager(DataIds.ID_USER_INFO)
            .addObserver(this, true, object : UserDataObserver<Any> {
                override fun onChanged(t: DataWrapper<Any>) {
                    //用户数据
                    mUserReqFinish = true
                    if (isLogined()) {
                        renderUserInfo()
                        renderAssets()
                    }
                    initViews()
                }
            })
        //监听资产数据更新
        UserDataHelper.getDataManager(DataIds.ID_USER_ASSETS)
            .addObserver(this, false, object : UserDataObserver<UserAssets> {
                override fun onChanged(t: DataWrapper<UserAssets>) {
                    //用户资产
                    userAssetsReqing = false
                    mAssetsReqFinish = true
                    if (t.error is WrapException) {
                        val errorMsg = HandleErrorMgr.handErrorMsg(t.error as WrapException, true)
                        LogUtils.pop(errorMsg)
                    } else if (t.error != null) {
                        LogUtils.pop(t.error?.message ?: "")
                    }
                    if (userAssetCallbackCount == 2) {
                        //用户点击 眼睛操作, 触发的返回, 通过内存缓存数据刷新UI
                        renderAssets()
                        userAssetCallbackCount = 1
                    } else {
                        if (userAssetCallbackCount == 1) {
                            //接口网络触发的返回(比较滞后), 不要刷新数据,因为网络回来的是错误的, 将当前用户操作的状态,再设置回去
                            //apiUserInfo().saveShowUserAssetsAmt(eyeShowWhenUserClick)
                            ApiImplUserInfo.getUserAssets()?.let {
                                it.showAsset = if (eyeShowWhenUserClick) "0" else "1"
                            }
                            userAssetCallbackCount = 0
                        } else {
                            LogUtils.d("CCC", "正常状态,接口网络触发的返回, 刷新数据")
                            renderAssets()
                        }
                    }
                }
            })
        //监听消息数据更新
        UserDataHelper.getDataManager(DataIds.ID_USER_MESSAGE).addObserver(this, true, object :
            UserDataObserver<MessageData> {
            override fun onChanged(t: DataWrapper<MessageData>) {
                if (activity == null || activity?.isFinishing == true) {
                    return
                }
                if (isLogined() && t.data != null) {
                    //若{未读消息数量} = 0，则隐藏该窗口；若{未读消息数量}>0，则显示该窗口；
                    unReadNum = MathUtils.forValI(t.data?.unReadCount, 0)
                    todoNum = MathUtils.forValI(t.data?.todoCount, 0)
                    //设置toolbar上的消息红点状态
                    setToolbarMsgRedIconStatus()
                    if (unReadNum > 0) {
                        binding.layoutLogin.layMessageModule.root.visibility = View.VISIBLE
                        //最近一条未读消息
                        setLastedMsgModuleUIData(unReadNum, t.data?.messageInfo)
                    } else {
                        binding.layoutLogin.layMessageModule.root.visibility = View.GONE
                    }
                }
            }
        })

        initViews()
        initClick()
    }

    private fun initClick() {
        binding.layoutLogin.layAssets.layAssetsInfo.setOnClickListener {
            //资产模块
            val bundle = Bundle()
            bundle.putIntArray(
                ValConfig.IT_INTERCEPT_CODE,
                intArrayOf(IGlobalInterceptCode.GLOBAL_CHECK_TRADE_UNACTIVE_DLG)
            )
            Invoker.getInstance().navigation(IWebProvider::class.java).launchWebView(
                this, "", H5UrlKey.INFO_ASSET_LIST, null,
                bundle, null
            )
            HbAnalytics.onClick("611160")
        }
        binding.layoutLogin.layAssets.ivShowAssets.setOnClickListener {
            //资产小眼睛
            val isShow = apiUserInfo().showUserAssetsAmt()
            LogUtils.d(
                "CCC",
                "缓存中:" + apiUserInfo().showUserAssetsAmt() + ",当前点击=" + eyeShowWhenUserClick
            )
            //调用这个方法,资产的回调监听会回来一次
            apiUserInfo().saveShowUserAssetsAmt(!isShow)
            eyeShowWhenUserClick = !isShow
            //如果是资产接口请求中,点击的 眼睛状态,则以当前点击的结果为准,添加一个标识
            if (userAssetsReqing) {
                userAssetCallbackCount = 2
            }
            UserRequest.saveShowUserAssetsAmt(!isShow, 0) {}
        }
        binding.layoutLogin.layRealName.setOnClickListener {
            //实名信息 客户状态 = 开户/休眠，跳转<实名信息页>  否则显示开户引导弹窗
            if (apiUserInfo().finishOpenAccount()) {
                Invoker.getInstance().navigation(IWebProvider::class.java)
                    .launchWebView(this, "", H5UrlKey.REAL_USER_INFO, null, null)
            } else {
                showOpenAccountDialog()
            }
        }
        binding.layoutLogin.layRisk.setOnClickListener {
            HbAnalytics.onClick("610300")
            //风险等级
            if (apiUserInfo().finishOpenAccount()) {
                //1. 客户状态 = 开户/休眠
                if (TextUtils.isEmpty(apiUserInfo().riskLevel())) {
                    //风险等级无值，跳转至<风险测评引导页>
                    Invoker.getInstance().navigation(IWebProvider::class.java).launchWebView(
                        this,
                        "",
                        H5UrlKey.RISK_EVA_IDX,
                        hashMapOf(Pair("from", "person")),
                        null
                    )
                } else {
                    //风险等级有值，跳转至<风险测评结果页> H5页面里面会判断是否过期
                    Invoker.getInstance().navigation(IWebProvider::class.java).launchWebView(
                        this,
                        "",
                        H5UrlKey.RISK_EVA_RESULT,
                        hashMapOf(Pair("from", "person")),
                        null
                    )
                }
            } else {
                //客户状态≠开户/休眠，显示开户引导弹窗
                showOpenAccountDialog()
            }
        }
        binding.layoutLogin.layInvestor.setOnClickListener {
            if (TextUtils.equals("PRO", apiUserInfo().investorType())) {
                if (apiUserInfo().investorAuditStatus()) {
                    Invoker.getInstance().navigation(IWebProvider::class.java)
                        .launchWebView(this, "", H5UrlKey.PRO_INVESTORS_CHECK, null, null)
                } else {
                    //专业投资者认证页(认证状态页)”（新增页面见下文说明）。
                    Invoker.getInstance().navigation(IWebProvider::class.java)
                        .launchWebView(this, "", H5UrlKey.PRO_INVESTORS_STATUS, null, null)
                }
            } else {
                if (apiUserInfo().investorAuditStatus()) {
                    Invoker.getInstance().navigation(IWebProvider::class.java)
                        .launchWebView(this, "", H5UrlKey.PRO_INVESTORS_CHECK, null, null)
                } else {
                    Invoker.getInstance().navigation(IWebProvider::class.java)
                        .launchWebView(this, "", H5UrlKey.PRO_INVESTORS_IDX, null, null)
                }
            }
        }
        binding.layoutLogin.layMobile.setOnClickListener {
            if (apiUserInfo().hasTradePwd()) {
                val bundle = Bundle()
                bundle.putIntArray(
                    ValConfig.IT_INTERCEPT_CODE,
                    intArrayOf(IGlobalInterceptCode.GLOBAL_CHECK_TRADE_UNACTIVE_DLG)
                )
                //交易密码不为空,则跳转<修改手机号页面>
                Invoker.getInstance().navigation(IWebProvider::class.java)
                    .launchWebView(this, "", H5UrlKey.UPDATE_MOBILE, null, bundle, null)
            } else {
                LogUtils.pop("您已开户，请先前往安全中心设置交易密码")
            }
        }
        binding.layoutLogin.layEmail.setOnClickListener {
            if (apiUserInfo().hasTradePwd()) {
                val bundle = Bundle()
                bundle.putIntArray(
                    ValConfig.IT_INTERCEPT_CODE,
                    intArrayOf(IGlobalInterceptCode.GLOBAL_CHECK_TRADE_UNACTIVE_DLG)
                )
                //交易密码不为空,则跳转<修改邮箱页面>
                Invoker.getInstance().navigation(IWebProvider::class.java)
                    .launchWebView(this, "", H5UrlKey.UPDATE_EMAIL, null, bundle, null)
            } else {
                LogUtils.pop("您已开户，请先前往安全中心设置交易密码")
            }
        }
        binding.layoutLogin.laySafeCenter.setOnClickListener {
            RouterHelper.launchFrag(this, PATH_FRAG_SAFE_CENTER, NavHelper.obtainArg("安全中心"))
//            Invoker.getInstance().navigation(IWebProvider::class.java).launchWebView(this, "", H5UrlKey.SAFETY_CENTER, null, null)
        }
        binding.layoutLogin.layAbout.setOnClickListener {
            Invoker.getInstance().navigation(IWebProvider::class.java).launchWebView(this, "", H5UrlKey.ABOUT_LIST, null, null)
        }
        binding.layoutLogin.logout.setOnClickListener {
            showLogoutDlg()
        }

        binding.layMineMsg.setOnClickListener {
            if (unReadNum > 0) {
                //有未读消息, 定位消息tab
                launcherToMsgPage(0)
            } else {
                //无未读消息, 有待办,定位待办tab,否则, 默认定位消息tab
                launcherToMsgPage(if (todoNum > 0) 1 else 0)
            }
            HbAnalytics.onClick("611150")
        }
        binding.ivMineSettings.setOnClickListener {
            //跳转到设置页
            RouterHelper.launchFragWithCallback(
                activity,
                PATH_FRAG_MINE_SETTINGS,
                NavHelper.obtainArg("设置", ValConfig.IT_VALUE_1, mCurAmtType?.code)
            ) { resultCode, intent ->
                if (resultCode == Activity.RESULT_OK) {
                    intent?.extras?.let { it ->
                        val amtTypeCode = it.getString(ValConfig.IT_VALUE_1)
                        mCurAmtType = AmtType.values().find { p ->
                            p.code == amtTypeCode
                        }
                    }
                    //页面在onResume会刷新接口,所以这里回调后, 不需要重复调用接口刷新数据
                    // refreshData()
                }
                true
            }
            HbAnalytics.onClick("611140")
        }
        binding.layoutLogin.layAssets.tvHwPiggyGoSign.setOnClickListener {
            //点击跳转至“海外储蓄罐签约页”
            Invoker.getInstance().navigation(IWebProvider::class.java)
                .launchWebView(it.context, "", H5UrlKey.CXG_SIGN_DETAIL, null, null)
            HbAnalytics.onClick("611120")
        }
        binding.layoutLogin.layAssets.tvHwCashGoBuy.setOnClickListener {
            val userAssets = ApiImplUserInfo.getUserAssets()
            if (TextUtils.equals("1", userAssets?.cashBalanceAssetLimit)) {
                GlobalHintDialog.getInstance(null, null)
                    .setTitle("提示")
                    .setMessage("审核中/审核不通过的打款凭证已达上限，不可继续上传。如有疑问，可咨询您的专属服务人员或联系客服进行线下上传。")
                    .setBtnCancelText("我知道了")
                    .show(childFragmentManager, null)
            } else {
                //存入现金：上传打款凭证”页，可以上传新的打款凭证
                Invoker.getInstance().navigation(IWebProvider::class.java)
                    .launchWebView(it.context, "", H5UrlKey.CXG_UPLOAD_ASSET_PROOF, null, null)
            }
            HbAnalytics.onClick("611130")
        }

        binding.layoutLogin.layAssets.ivAssetsHint.setOnClickListener {
            showAmtHintDlg(AmtDescTitle.TOTAL_AMT)
        }
        binding.layoutLogin.layAssets.ivFundHoldHint.setOnClickListener {
            showAmtHintDlg(AmtDescTitle.FUND_AMT)
        }
        binding.layoutLogin.layAssets.ivHwCashHint.setOnClickListener {
            showAmtHintDlg(AmtDescTitle.CASH_AMT)
        }
        binding.layoutLogin.layAssets.ivHwPiggyHint.setOnClickListener {
            showAmtHintDlg(AmtDescTitle.HW_PIGGY_AMT)
        }
        binding.layoutLogin.layAssets.ivOnPayHint.setOnClickListener {
            showAmtHintDlg(AmtDescTitle.ONWAY_AMT)
        }
        binding.layoutLogin.layDerivative.setOnClickListener {
            HbAnalytics.onClick("611860")
            //衍生工具知识评估 若用户已完成香港开户：点击入口，进入衍生工具知识评估页面 若用户未完成香港开户：点击入口，弹出开户引导弹窗：
            if (apiUserInfo().finishOpenAccount()) {
                Invoker.getInstance().navigation(IWebProvider::class.java)
                    .launchWebView(it.context, "", H5UrlKey.DERIVATIVE_IDX, null, null)
            } else {
                OpenAccountGuide.showOpenAccountDialog(this)
            }
        }
    }

    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        if (!hidden) {
            (activity as AtyEmpty?)?.setToolbarVisibility(false)
            StatusBarUtil.translucentStatusBar(activity, true)
            refreshData()

            pvAnalysis()
        }
    }

    override fun onResume() {
        super.onResume()
        refreshData()
    }

    override fun parseArgment(arg: Bundle?) {
        pvAnalysis()
        observableLoginStatus()
    }

    /**
     * 监听用户登录状态
     */
    private fun observableLoginStatus() {
        //直接使用lifecycle会移除不掉
        LoginManagerImpl.getInstance().addLogoutObserver(unLoginObservable)
        LoginManagerImpl.getInstance().addLoginObserver(loginObservable)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        LoginManagerImpl.getInstance().removeLogoutObserver(unLoginObservable)
        LoginManagerImpl.getInstance().removeLoginObserver(loginObservable)
    }

    private fun pvAnalysis() {
        PvReportUtils.reportPvIfPvInfoExists(
            javaClass,
            context, fundCode, alysContentId, null
        )
    }

    /**退出登录监听*/
    private val unLoginObservable: LogoutObserver = LogoutObserver {
        if (activity == null || activity?.isFinishing == true) {
            return@LogoutObserver
        }
        mAnnounceHelper.loginStateChange = true
        initViews()
        refreshData()
        LogUtils.d("LoginAccountMgr", "FragMin执行未登录逻辑")
    }

    /**登录监听*/
    private val loginObservable: LoginObserver = LoginObserver {
        if (activity == null || activity?.isFinishing == true) {
            return@LoginObserver
        }
        mAnnounceHelper.loginStateChange = true
        initViews()
        refreshData()
        LogUtils.d("LoginAccountMgr", "FragMin执行已登录逻辑")
    }

    private fun refreshData() {
        mUserReqFinish = false
        mAssetsReqFinish = false
        if (isLogined()) {
            mAnnounceHelper.queryAnnounce(AnnouncementHelper.REQ_ID_MINE_TOP)
            UserDataHelper.scheduleRemoteFetch(DataIds.ID_USER_INFO, FetchAction.dataChanged)
            UserDataHelper.scheduleRemoteFetch(DataIds.ID_USER_MESSAGE, FetchAction.dataChanged)
            UserDataHelper.scheduleRemoteFetch(DataIds.ID_USER_HOLD, FetchAction.dataChanged)
            UserDataHelper.scheduleRemoteFetch(DataIds.ID_USER_LICENSEE, FetchAction.dataChanged)
            userAssetsReqing = true
            UserDataHelper.scheduleRemoteFetch(DataIds.ID_USER_ASSETS, FetchAction.dataChanged)
            UserRequest.queryBanner("1", 0) {
                if (activity == null || activity?.isFinishing == true) return@queryBanner
                if (it.isSuccess && it.mData is AdResult && !(it.mData as AdResult).advertisings.isNullOrEmpty()) {
                    binding.layoutLogin.banner.visibility = View.VISIBLE
                    val data = it.mData as AdResult
                    binding.layoutLogin.banner.create(data.advertisings)
                } else {
                    binding.layoutLogin.banner.visibility = View.GONE
                }
            }
            UserRequest.reqFundQxnProdList(mCurAmtType?.code ?: AmtType.DOLLAR.code, 1) {
                if (activity == null || activity?.isFinishing == true) {
                    return@reqFundQxnProdList
                }
                if (it.isSuccess && it.mData != null) {
                    mQxnProdBody = it.mData as QxnProdBody
                }
            }
        }
    }

    private fun renderUserInfo() {
        renderAccountStatus()
        renderRisk()
        renderInvestor()
        renderBankCard()
        renderMobile()
        renderEmail()
        renderContractFile()
        renderTradeRecord()
        renderCashProtoQuery()
        renderHwPiggyServerQuery()
    }

    /**
     * 开户/入金引导区域
     * //01-去开户；02-继续开户；03-查看开户进度；04-修改开户资料；05-去入金；06-查看入金进度；07- 修改入金资料；08-隐藏开户入金区域
     */
    private fun renderAccountStatus() {
        val showAccountModule: Boolean
        val accountHintTxt: String?
        val errorMsgTxt: Spannable?
        if (apiUserInfo().finishOpenAccount()) {
            showAccountModule = false
            accountHintTxt = ""
            errorMsgTxt = null
            dlgDesc = ""
            btnTxt = ""
            btnLink = ""
            linkParam = null
        } else {
            when (apiUserInfo().openDepositsStatus()) {
                "1" -> {
                    //01-去开户
                    showAccountModule = true
                    accountHintTxt = "完成开户，开启您的全球投资之旅~"
                    errorMsgTxt = null
                    dlgDesc = "该功能需要完成开户后才能使用哦！"
                    btnTxt = "去开户"
                    btnLink = H5UrlKey.OPEN_ACCOUNT_IDX
                    linkParam = null
                }

                "2" -> {
                    //02-继续开户 最大填写资料页面
                    showAccountModule = true
                    accountHintTxt = "完成开户，开启您的全球投资之旅~"
                    errorMsgTxt = SpannableUtils.formatStr(
                        SpannableItem("开户资料填写中，当前进度: "),
                        SpannableItem(
                            "${apiUserInfo().openAcctStepProgress()}%",
                            ColorUtils.parseColor("#C51D25"),
                            true
                        ),
                    )
                    dlgDesc = "该功能需要完成开户后才能使用哦！"
                    btnTxt = "继续开户"
                    btnLink = H5UrlKey.OPEN_ACCOUNT
                    linkParam = hashMapOf(
                        Pair("isEdit", "1"),
                        Pair("stepFlag", apiUserInfo().openAcctStep() ?: "")
                    )
                }

                "3" -> {
                    //03-查看开户进度 开户申请结果页
                    showAccountModule = true
                    accountHintTxt = "开户资料待审核，请耐心等待..."
                    errorMsgTxt = SpannableString("预计1~2个工作日完成审核")
                    dlgDesc = "该功能需要完成开户后才能使用哦！开户资料待审核，请耐心等待..."
                    btnTxt = "查看进度"
                    btnLink = H5UrlKey.OPEN_ACCOUNT_STATUS
                    linkParam = null
                }

                "4" -> {
                    //04-修改开户资料 开户结果页
                    showAccountModule = true
                    accountHintTxt = "您的开户资料未通过审核"
                    errorMsgTxt = SpannableString("请修改后重新提交")
                    dlgDesc =
                        "该功能需要完成开户后才能使用哦！您的开户资料未通过审核，请修改后重新提交。"
                    btnTxt = "去修改"
                    btnLink = H5UrlKey.OPEN_ACCOUNT_STATUS
                    linkParam = null
                }

                "5" -> {
                    //05-去入金 入金引导页
                    showAccountModule = true
                    accountHintTxt = "开户资料已通过审核"
                    errorMsgTxt = SpannableString("入金后完成开户")
                    dlgDesc = "该功能需要完成开户后才能使用哦！开户资料已通过审核，请入金后完成开户"
                    btnTxt = "去入金"
                    btnLink = H5UrlKey.DEPOSITS_IDX
                    linkParam = null
                }

                "6" -> {
                    //06-查看入金进度 打款凭证查看页
                    showAccountModule = true
                    accountHintTxt = "入金凭证待审核，请耐心等待..."
                    errorMsgTxt = SpannableString("预计1~2个工作日完成审核")
                    dlgDesc = "该功能需要完成开户后才能使用哦！入金凭证待审核，请耐心等待..."
                    btnTxt = "查看"
                    btnLink = H5UrlKey.DEPOSITS_DETAIL
                    linkParam = null
                }

                "7" -> {
                    //07- 修改入金资料 打款凭证修改页
                    showAccountModule = true
                    accountHintTxt = "您的入金凭证未通过审核"
                    errorMsgTxt = SpannableString("请修改后重新提交")
                    dlgDesc =
                        "该功能需要完成开户后才能使用哦！您的入金凭证未通过审核，请修改后重新提交。"
                    btnTxt = "去修改"
                    btnLink = H5UrlKey.DEPOSITS_UPDATE
                    linkParam = null
                }

                else -> {
                    //08 以及其他状态都隐藏
                    showAccountModule = false
                    accountHintTxt = ""
                    errorMsgTxt = null
                    dlgDesc = ""
                    btnTxt = ""
                    btnLink = ""
                    linkParam = null
                }
            }
        }
        if (showAccountModule) {
            binding.layoutLogin.layAssets.root.visibility = View.GONE
            binding.layoutLogin.layAccountStatus.root.visibility = View.VISIBLE
            binding.layoutLogin.layAccountStatus.tvAccountHint.text = accountHintTxt
            if (errorMsgTxt == null) {
                binding.layoutLogin.layAccountStatus.tvAccountErrorMsg.visibility = View.GONE
            } else {
                binding.layoutLogin.layAccountStatus.tvAccountErrorMsg.visibility = View.VISIBLE
                binding.layoutLogin.layAccountStatus.tvAccountErrorMsg.text = errorMsgTxt
            }
            binding.layoutLogin.layAccountStatus.btnAccount.text = btnTxt
            binding.layoutLogin.layAccountStatus.btnAccount.setOnClickListener {
                if (!TextUtils.isEmpty(btnLink)) {
                    Invoker.getInstance().navigation(IWebProvider::class.java).launchWebView(this, "", btnLink, linkParam, null)
                }
            }
        } else {
            binding.layoutLogin.layAccountStatus.root.visibility = View.GONE
            binding.layoutLogin.layAssets.root.visibility = View.VISIBLE
        }
    }

    /**
     * 资产区域
     */
    private fun renderAssets() {
        if (!mUserReqFinish || !mAssetsReqFinish) return
        showAlermDlg(null, 0)
        binding.refreshLayout.finishRefresh()
        if (apiUserInfo().finishOpenAccount()) {
            //已开户显示
            binding.layoutLogin.layAssets.root.visibility = View.VISIBLE
            val userAssets = ApiImplUserInfo.getUserAssets()
            binding.layoutLogin.layAssets.ivShowAssets.visibility = View.VISIBLE
            binding.layoutLogin.layAssets.ivAssetsArrow.visibility = View.VISIBLE
            binding.layoutLogin.layAssets.layAssetsInfo.isEnabled = true
            val isShowOnPay = MathUtils.forBig(userAssets?.inTransitTradeAmt) > BigDecimal.ZERO
            //在途资金非0时才显示
            binding.layoutLogin.layAssets.layOnPay.visibility =
                if (isShowOnPay) View.VISIBLE else View.GONE
            //现金余额日期
            binding.layoutLogin.layAssets.tvHwCashDate.text =
                "(${
                    FundTextUtils.formatDate(
                        userAssets?.cashBalanceAssetDate,
                        DateUtils.DATEF_YMD
                    )
                })"
            //设置储蓄罐资产显示
            setHwPiggyUIData(userAssets?.piggyBankAsset)
            setAmtUIData(userAssets)
            setAmtUnit()
            val assetsMsgList = mutableListOf<MineAssetsMsg>()
            val inTransitTradeNums = MathUtils.forValI(userAssets?.inTransitTradeNums, 0)
            if (inTransitTradeNums > 0) {
                assetsMsgList.add(
                    MineAssetsMsg(
                        MineAssetsMsg.TYPE_IN_TRANSIT_TRADE_NUMS,
                        count = inTransitTradeNums,
                        inTransitDealNo = userAssets?.inTransitDealNo
                    )
                )
            }
            val fundReceivedNums = MathUtils.forValI(userAssets?.fundReceivedNums, 0)
            if (fundReceivedNums > 0) {
                assetsMsgList.add(
                    MineAssetsMsg(
                        MineAssetsMsg.TYPE_FUND_RECEIVED_NUMS,
                        count = fundReceivedNums
                    )
                )
            }
            //现金审核消息
            val shingNum = MathUtils.forValI(userAssets?.underReviewCount, 0)
            val shNotNum = MathUtils.forValI(userAssets?.notPassCount, 0)
            if (shingNum > 0 || shNotNum > 0) {
                //现金消息,要放在第一条
                assetsMsgList.add(
                    0,
                    MineAssetsMsg(
                        MineAssetsMsg.TYPE_CASH_HINT,
                        cashShenHeingNum = shingNum,
                        cashShenHeNotNum = shNotNum,
                        voucherNo = userAssets?.voucherNo
                    )
                )
            }
            if (assetsMsgList.isNotEmpty()) {
                binding.layoutLogin.layAssets.ivLinkLeft.visibility = View.VISIBLE
                binding.layoutLogin.layAssets.ivLinkRight.visibility = View.VISIBLE
                binding.layoutLogin.layAssets.rvAssetsMsg.visibility = View.VISIBLE
                if (binding.layoutLogin.layAssets.rvAssetsMsg.itemDecorationCount == 0) {
                    binding.layoutLogin.layAssets.rvAssetsMsg.addItemDecoration(
                        XLinearBuilder(
                            context
                        ).setDrawableRes(R.drawable.dash_line).build()
                    )
                }
                binding.layoutLogin.layAssets.rvAssetsMsg.adapter =
                    AdpMineAssetsMsg(assetsMsgList, apiUserInfo().showUserAssetsAmt())
            } else {
                binding.layoutLogin.layAssets.ivLinkLeft.visibility = View.GONE
                binding.layoutLogin.layAssets.ivLinkRight.visibility = View.GONE
                binding.layoutLogin.layAssets.rvAssetsMsg.visibility = View.GONE
            }
        } else {
            //未开户不显示
            binding.layoutLogin.layAssets.root.visibility = View.GONE
        }
    }

    /**
     * 设置资产数据相关
     */
    private fun setAmtUIData(userAssets: UserAssets?) {
        if (TextUtils.equals("1", userAssets?.showAsset)) {
            //资产不可见 ****
            binding.layoutLogin.layAssets.ivShowAssets.setImageResource(R.mipmap.icon_my_eye_close)
            //总资产
            binding.layoutLogin.layAssets.tvAssetsTotal.text = "****"
            //在途
            binding.layoutLogin.layAssets.tvOnPay.text = "****"
            //基金持仓
            binding.layoutLogin.layAssets.tvFundHoldAmt.text = "****"
            //海外piggy
            binding.layoutLogin.layAssets.tvHwPiggyAmt.text = "****"
            //海外现金
            binding.layoutLogin.layAssets.tvHwCashAmt.text = "****"
        } else {
            //资产可见
            binding.layoutLogin.layAssets.ivShowAssets.setImageResource(R.mipmap.icon_my_eye_open)
            //总资产
            binding.layoutLogin.layAssets.tvAssetsTotal.text =
                TradeUtils.forAmt(userAssets?.totalAsset, null, "--")
            //在途
            binding.layoutLogin.layAssets.tvOnPay.text =
                TradeUtils.forAmt(userAssets?.inTransitTradeAmt, null, "--")
            //基金持仓
            binding.layoutLogin.layAssets.tvFundHoldAmt.text =
                TradeUtils.forAmt(userAssets?.fundAsset, null, "--")
            //海外piggy
            binding.layoutLogin.layAssets.tvHwPiggyAmt.text =
                TradeUtils.forAmt(userAssets?.piggyBankAsset, null, "--")
            //海外现金
            binding.layoutLogin.layAssets.tvHwCashAmt.text =
                TradeUtils.forAmt(userAssets?.cashBalanceAsset, null, "--")
        }
        setOnwayAmtVerticalUI(userAssets?.inTransitTradeAmt)
    }

    private var leftTextWidth = 0

    /**
     * 计算在途资产是否能在水平布局方向上显示完整, 如果显示不下, 就设置为垂直布局
     * 默认布局为水平方向
     */
    private fun setOnwayAmtVerticalUI(inTransitTradeAmt: String?) {
        inTransitTradeAmt ?: return
        binding.layoutLogin.layAssets.tvOnPay.post {
            if (activity == null || activity?.isFinishing == true) {
                return@post
            }
            //水平布局时,剩余给在途资产显示的空间,只要计算一次
            if (leftTextWidth == 0) {
                leftTextWidth = binding.layoutLogin.layAssets.tvOnPay.measuredWidth
            }
            //由于存在数据刷新, 在途资产可能会变, 但剩余的空间是不会变的, 所以要计算金额的长度
            val tempStr = TradeUtils.forAmt(inTransitTradeAmt, null, "--")
            val onWayAmtWidth =
                binding.layoutLogin.layAssets.tvOnPay.paint.measureText(tempStr)
            //在途资产内容超过剩余空间,垂直布局,否则水平布局
            if (onWayAmtWidth > leftTextWidth) {
                binding.layoutLogin.layAssets.layOnPay.orientation = LinearLayout.VERTICAL
            } else {
                binding.layoutLogin.layAssets.layOnPay.orientation = LinearLayout.HORIZONTAL
            }
        }
    }

    /**
     * 设置海外piggy的UI数据状态
     */
    private fun setHwPiggyUIData(piggyBankAsset: String?) {
        val showPiggyAsset = MathUtils.forBig(piggyBankAsset) > BigDecimal.ZERO
        //已签署显示资产,或者 如果未签署但资产>0,则显示资产,否则显示"去签约"
        if (apiUserInfo().getHwPiggySigned() || showPiggyAsset) {
            binding.layoutLogin.layAssets.tvHwPiggyGoSign.visibility = View.GONE
            binding.layoutLogin.layAssets.tvHwPiggyAmt.visibility = View.VISIBLE
        } else {
            binding.layoutLogin.layAssets.tvHwPiggyGoSign.visibility = View.VISIBLE
            binding.layoutLogin.layAssets.tvHwPiggyAmt.visibility = View.GONE
        }
    }

    /**
     * 设置资产单位: 美元/人民币
     */
    private fun setAmtUnit() {
        val amtTypeCode =
            CommonStorageUtils.getString(
                SpConfig.USER_SELECT_AMT_TYPE + "_" + getHkCustNo(),
                AmtType.DOLLAR.code
            )
        mCurAmtType = if (amtTypeCode == AmtType.RMB.code) {
            AmtType.RMB
        } else {
            AmtType.DOLLAR
        }
        val unitText = "(${mCurAmtType?.unit})"
        binding.layoutLogin.layAssets.tvTotalAmtUnit.text = unitText
        binding.layoutLogin.layAssets.tvOnPayUnit.text = unitText
        binding.layoutLogin.layAssets.tvFundHoldTitle.text = "基金持仓${unitText}"
        binding.layoutLogin.layAssets.tvHwPiggyTitle.text = "海外储蓄罐${unitText}"
        binding.layoutLogin.layAssets.tvHwCashUnit.text = unitText
    }

    /**
     * 风险等级
     */
    private fun renderRisk() {
        if (TextUtils.isEmpty(apiUserInfo().riskLevel())) {
            binding.layoutLogin.tvRisk.text = ""
        } else {
            if (apiUserInfo().riskToleranceExpire()) {
                binding.layoutLogin.tvRisk.text = "已过期"
            } else {
                binding.layoutLogin.tvRisk.text = apiUserInfo().riskLevelTxt()
            }
        }
    }

    /**
     * 投资者资质
     * 仅当客户已完成开户时显示
     */
    private fun renderInvestor() {
        if (apiUserInfo().finishOpenAccount()) {
            binding.layoutLogin.layInvestor.visibility = View.VISIBLE
            val investorType = apiUserInfo().investorType()
            if (TextUtils.equals("PRO", investorType)) {
                //专业投资者
                if (TextUtils.equals("1", apiUserInfo().investorAssetsEffectiveStatus())) {
                    //过期状态处理
                    binding.layoutLogin.tvInvestor.text = SpannableUtils.formatStr(
                        SpannableItem("专业"),
                        SpannableItem(" 已过期", ColorUtils.parseColor("#C91212"))
                    )
                } else {
                    binding.layoutLogin.tvInvestor.text = "专业"
                }
                binding.layoutLogin.tvInvestor.setCompoundDrawablesWithIntrinsicBounds(
                    0,
                    0,
                    R.mipmap.ic_right_arrow_gray,
                    0
                )
                binding.layoutLogin.layInvestor.isEnabled = true
            } else if (TextUtils.equals("NORMAL", investorType)) {
                //普通投资者
                binding.layoutLogin.tvInvestor.text = SpannableUtils.formatStr(
                    SpannableItem("普通"),
                    SpannableItem(" 去认证", ColorUtils.parseColor("#5072D4"))
                )
                binding.layoutLogin.tvInvestor.setCompoundDrawablesWithIntrinsicBounds(
                    0,
                    0,
                    R.mipmap.ic_right_arrow_gray,
                    0
                )
                binding.layoutLogin.layInvestor.isEnabled = true
            } else {
                binding.layoutLogin.tvInvestor.text = "--"
                binding.layoutLogin.tvInvestor.setCompoundDrawablesWithIntrinsicBounds(
                    0,
                    0,
                    R.mipmap.ic_right_arrow_transparent,
                    0
                )
                binding.layoutLogin.layInvestor.isEnabled = false
            }
        } else {
            binding.layoutLogin.layInvestor.visibility = View.GONE
        }
    }

    /**
     * 银行卡
     */
    private fun renderBankCard() {
        if (apiUserInfo().bindCardCount() > 0) {
            binding.layoutLogin.tvCard.text = "${apiUserInfo().bindCardCount()}张"
            binding.layoutLogin.tvCard.setCompoundDrawablesWithIntrinsicBounds(
                0,
                0,
                R.mipmap.ic_right_arrow_gray,
                0
            )
            binding.layoutLogin.layCard.isEnabled = true
            binding.layoutLogin.layCard.setOnClickListener {
                if (apiUserInfo().finishOpenAccount()) {
                    //1. 客户状态 = 开户/休眠
                    if (apiUserInfo().hasTradePwd()) {
                        //交易密码不为空,则跳转<银行卡列表页>
                        val bundle = Bundle()
                        bundle.putIntArray(
                            ValConfig.IT_INTERCEPT_CODE,
                            intArrayOf(IGlobalInterceptCode.GLOBAL_CHECK_TRADE_UNACTIVE_DLG)
                        )
                        Invoker.getInstance().navigation(IWebProvider::class.java).launchWebView(
                            this,
                            "",
                            H5UrlKey.USER_BANK_LIST,
                            null,
                            bundle,
                            null
                        )
                    } else {
                        LogUtils.pop("您已开户，请先前往安全中心设置交易密码")
                    }
                } else {
                    //客户状态≠开户/休眠，显示开户引导弹窗
                    showOpenAccountDialog()
                }
            }
        } else {
            binding.layoutLogin.tvCard.text = "未绑定"
            binding.layoutLogin.tvCard.setCompoundDrawablesWithIntrinsicBounds(
                0,
                0,
                R.mipmap.ic_right_arrow_transparent,
                0
            )
            binding.layoutLogin.layCard.isEnabled = false
        }
    }

    /***
     * 手机号
     */
    private fun renderMobile() {
        if (apiUserInfo().finishOpenAccount()) {
            //开户完成 显示跳转箭头
            binding.layoutLogin.tvMobile.setCompoundDrawablesWithIntrinsicBounds(
                0,
                0,
                R.mipmap.ic_right_arrow_gray,
                0
            )
            binding.layoutLogin.layMobile.isEnabled = true
        } else {
            //未开户
            binding.layoutLogin.tvMobile.setCompoundDrawablesWithIntrinsicBounds(
                0,
                0,
                R.mipmap.ic_right_arrow_transparent,
                0
            )
            binding.layoutLogin.layMobile.isEnabled = false
        }
        binding.layoutLogin.tvMobile.text =
            FundTextUtils.showTextEmpty(apiUserInfo().mobileMask(), "未绑定")
    }

    /**
     * 邮箱
     */
    private fun renderEmail() {
        if (apiUserInfo().finishOpenAccount()) {
            //开户完成 显示跳转箭头
            binding.layoutLogin.tvEmail.setCompoundDrawablesWithIntrinsicBounds(
                0,
                0,
                R.mipmap.ic_right_arrow_gray,
                0
            )
            binding.layoutLogin.layEmail.isEnabled = true
        } else {
            //未开户
            binding.layoutLogin.tvEmail.setCompoundDrawablesWithIntrinsicBounds(
                0,
                0,
                R.mipmap.ic_right_arrow_transparent,
                0
            )
            binding.layoutLogin.layEmail.isEnabled = false
        }
        binding.layoutLogin.tvEmail.text =
            FundTextUtils.showTextEmpty(apiUserInfo().emailMask(), "未绑定")
    }

    /**
     * 合同文件查询
     * 校验是否已完成开户且开户方式=线上开户
     * a）若满足，入口按钮展示“合同文件查询”，点击跳转“合同文件查询页”
     * b）若不满足，入口按钮展示“产品合同查询”，点击跳转“产品合同查询页”
     */
    private fun renderContractFile() {
        binding.layoutLogin.layContractFile.visibility = View.VISIBLE
        if (apiUserInfo().finishOpenAccount() && apiUserInfo().onLineOpenAccount()) {
            binding.layoutLogin.tvContractFile.text = "合同文件查询"
        } else {
            binding.layoutLogin.tvContractFile.text = "产品合同查询"
        }
        binding.layoutLogin.layContractFile.setOnClickListener {
            val bundle = Bundle()
            if (apiUserInfo().finishOpenAccount()) {
                //已开户才有这个弹框逻辑,否则不会有
                bundle.putIntArray(
                    ValConfig.IT_INTERCEPT_CODE,
                    intArrayOf(IGlobalInterceptCode.GLOBAL_CHECK_TRADE_UNACTIVE_DLG)
                )
            }
            Invoker.getInstance().navigation(IWebProvider::class.java).launchWebView(
                this, "",
                if (apiUserInfo().finishOpenAccount() && apiUserInfo().onLineOpenAccount()) {
                    H5UrlKey.CONTRACT_LIST
                } else {
                    H5UrlKey.POCT_SEARCH
                }, null, bundle, null
            )
        }
    }

    /**
     * 交易记录
     * 若当前账号已登陆 且 已完成开户
     */
    private fun renderTradeRecord() {
        if (apiUserInfo().finishOpenAccount()) {
            binding.layoutLogin.layTradeRecord.visibility = View.VISIBLE
            binding.layoutLogin.layTradeRecord.setOnClickListener {
                val bundle = Bundle()
                bundle.putIntArray(
                    ValConfig.IT_INTERCEPT_CODE,
                    intArrayOf(IGlobalInterceptCode.GLOBAL_CHECK_TRADE_UNACTIVE_DLG)
                )
                Invoker.getInstance().navigation(IWebProvider::class.java)
                    .launchWebView(this, "", H5UrlKey.TRADE_LIST_IDX, null, bundle, null)
            }
        } else {
            binding.layoutLogin.layTradeRecord.visibility = View.GONE
        }
    }

    /**
     * 现金存入凭证的查询
     * 若当前账号已登陆 且 已完成开户 显示,否则,隐藏
     */
    private fun renderCashProtoQuery() {
        if (apiUserInfo().finishOpenAccount()) {
            binding.layoutLogin.layCashProofQuery.visibility = View.VISIBLE
            binding.layoutLogin.layCashProofQuery.setOnClickListener {
                val bundle = Bundle()
                bundle.putIntArray(
                    ValConfig.IT_INTERCEPT_CODE,
                    intArrayOf(IGlobalInterceptCode.GLOBAL_CHECK_TRADE_UNACTIVE_DLG)
                )
                Invoker.getInstance().navigation(IWebProvider::class.java)
                    .launchWebView(this, "", H5UrlKey.ASSET_PROOF_LIST, null, bundle, null)
                HbAnalytics.onClick("611250")
            }
        } else {
            binding.layoutLogin.layCashProofQuery.visibility = View.GONE
        }
    }

    /**
     * 海外储蓄罐服务
     * 若当前账号已登陆 则显示,否则隐藏
     */
    private fun renderHwPiggyServerQuery() {
        if (apiUserInfo().isLogined()) {
            binding.layoutLogin.layHwPiggyServer.visibility = View.VISIBLE
            binding.layoutLogin.layHwPiggyServer.setOnClickListener {
                val hasSignPiggyProtol = apiUserInfo().getHwPiggySigned()
                val piggyH5url = if (hasSignPiggyProtol) {
                    //若【客户储蓄罐协议状态】=已签署，则跳转至“海外储蓄罐服务”页
                    H5UrlKey.CXG_ZONE_DETAIL
                } else {
                    //若【客户储蓄罐协议状态】≠已签署 或 【客户储蓄罐协议状态】为空，则跳转至“海外储蓄罐签约页”
                    H5UrlKey.CXG_SIGN_DETAIL
                }
                Invoker.getInstance().navigation(IWebProvider::class.java).launchWebView(this, "", piggyH5url, null, null, null)
                HbAnalytics.onClick("611260")
            }
        } else {
            binding.layoutLogin.layHwPiggyServer.visibility = View.GONE
        }
    }

    /**
     * 公告接口回调
     */
    override fun onNoticeInvoke(notices: AnnouncementResult?, notice: Announcement?) {
        if (activity == null || activity?.isFinishing == true) return
        LogUtils.d(TAG, "公告返回---")
        renderAnnouncement(notice)
    }

    /**
     * 公告
     */
    private fun renderAnnouncement(notice: Announcement?) {
        if (notice == null || TextUtils.isEmpty(notice.desc)) {
            binding.layAnnouncement.root.visibility = View.GONE
        } else {
            val isOverWriteNotice = AnnouncementHelper.queryMsgIsClosed(notice)
            if (isOverWriteNotice) {
                binding.layAnnouncement.root.visibility = View.GONE
            } else {
                binding.layAnnouncement.root.visibility = View.VISIBLE
                var showClose = false
                var isStoreCloseAction = false
                if (TextUtils.equals(AnnouncementHelper.Notice_Type_INFO, notice.important)) {
                    showClose = true
                    isStoreCloseAction = true
                } else if (TextUtils.equals(
                        AnnouncementHelper.Notice_Type_WARN,
                        notice.important
                    )
                ) {
                    showClose = true
                }
                val sp = SpannableString(" ${notice.desc}")
                val drawable =
                    ContextCompat.getDrawable(GlobalApp.getApp(), R.mipmap.ic_announce)
                drawable?.let {
                    it.setBounds(0, 0, DensityUtils.dp2px(10f), DensityUtils.dp2px(10f))
                    sp.setSpan(
                        CenterImageSpan(it, DensityUtils.dp2px(5f)),
                        0,
                        1,
                        Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                }
                binding.layAnnouncement.tvAnnouncementContent.text = sp
                ViewUtils.setVisibility(
                    binding.layAnnouncement.ivAnnouncementClose,
                    if (showClose) View.VISIBLE else View.GONE
                )
                //关闭按钮显示才设置处关闭click事件
                if (showClose) {
                    val finalIsStoreCloseAction = isStoreCloseAction
                    binding.layAnnouncement.ivAnnouncementClose.setOnClickListener {
                        if (finalIsStoreCloseAction) {
                            AnnouncementHelper.storeClosedMsg(notice.productId)
                        }
                        binding.layAnnouncement.root.visibility = View.GONE
                    }
                }
                binding.layAnnouncement.tvAnnouncementContent.setOnClickListener {
                    if (TextUtils.isEmpty(notice.link)) {
                        val contentList = notice.desc?.split("\n")
                        AlertListStringDlg.getInstance(
                            "公 告",
                            ArrayList(contentList ?: arrayListOf())
                        ).show(childFragmentManager, "AnnouncementDlg")
                    } else {
                        PushDispatchHelper.pushDispatch(context, notice.link)
                    }
                }
            }
        }
    }

    /**
     * 开户提示弹框
     */
    private fun showOpenAccountDialog() {
        if (TextUtils.isEmpty(btnLink)) return
        GlobalHintDialog.getInstance(null, object : GlobalHintDialog.IGlobalHintDlgListener {
            override fun onClick(clickType: GlobalHintDialog.CLiCK_DLG_BTN_TYPE) {
                if (clickType == GlobalHintDialog.CLiCK_DLG_BTN_TYPE.CLICK_SURE) {
                    if (activity != null && !TextUtils.isEmpty(btnLink)) {
                        Invoker.getInstance().navigation(IWebProvider::class.java)
                            .launchWebView(this@FragMine, "", btnLink, linkParam, null)
                    }
                }
            }
        })
            .setTitle("提示")
            .setMessage(dlgDesc)
            .setBtnCancelText("下次再说")
            .setBtnSureText(btnTxt)
            .show(childFragmentManager, null)
    }

    /**
     * 退出登录
     */
    private fun showLogoutDlg() {
        HbAnalytics.onClick("610320")
        GlobalHintDialog.getInstance(null, object : GlobalHintDialog.IGlobalHintDlgListener {
            override fun onClick(clickType: GlobalHintDialog.CLiCK_DLG_BTN_TYPE) {
                if (clickType == GlobalHintDialog.CLiCK_DLG_BTN_TYPE.CLICK_SURE) {
                    activity?.let {
                        UserRequest.logout(1) {}
                        Invoker.getInstance().navigation(ILoginProvider::class.java).loginOut()
                        closeFloatViewAfterLoginOut()
                        LogUtils.pop("已退出")
                    }
                }
            }
        })
            .setMessage("确定退出登录吗？")
            .setBtnCancelText("取消")
            .setBtnSureText("确定")
            .setDlgCancelable(false)
            .show(childFragmentManager, null)
    }

    /**
     * 退出登录
     * a.关闭vhall的小窗播放,释放资源对象
     * b. 关闭腾讯小窗播放,释放资源对象
     */
    private fun closeFloatViewAfterLoginOut() {
        FloatWindow.destroyPlayer(ITag.SM_VHALL_VIDEO_FLOATWINDOW_TAG)
        FloatWindow.destroy(ITag.SM_VHALL_VIDEO_FLOATWINDOW_TAG)
        FloatWindow.destroyPlayer(ITag.SM_TX_VIDEO_FLOATWINDOW_TAG)
        FloatWindow.destroy(ITag.SM_TX_VIDEO_FLOATWINDOW_TAG)
    }

    private fun initViews() {
        val login = isLogined()
        binding.layUnlogin.root.visibility = if (login) View.GONE else View.VISIBLE
        binding.toolbar.visibility = if (login) View.VISIBLE else View.GONE
        binding.ivTop.visibility = if (login) View.VISIBLE else View.GONE
        binding.vStatusbar.visibility = if (login) View.VISIBLE else View.GONE
        binding.toolbar.visibility = if (login) View.VISIBLE else View.GONE
        if (!login) {
            binding.layAnnouncement.root.visibility = View.GONE
        }
        binding.layoutLogin.root.visibility = if (login) View.VISIBLE else View.GONE
        binding.layMineMsg.visibility = if (login) View.VISIBLE else View.GONE
        binding.ivMineSettings.visibility = if (login) View.VISIBLE else View.GONE
        if (!login) {
            //未登录,隐藏消息模块, 已登录,需要在消息回调中判断是否有未读消息,才显示
            binding.layoutLogin.layMessageModule.root.visibility = View.GONE
        }
        binding.refreshLayout.isEnableRefresh = login
    }

    private fun initUnlogin() {
        binding.layUnlogin.tvLogin.background = ShapeCreator()
            .color(ColorUtils.parseColor("#FFCE2424"))
            .radius(8F)
            .create()


        val contractUsInfoList = arrayListOf<AboutUsItemInfo>()

        contractUsInfoList.add(
            AboutUsItemInfo(
                "+852 37258088",
                R.color.cl_6372b0,
                "电话",
                object : View.OnClickListener {
                    override fun onClick(v: View?) {
                        dealPhone("+852 37258088")
                    }
                })
        )
        contractUsInfoList.add(AboutUsItemInfo("<EMAIL>", null, "邮箱"))
        contractUsInfoList.add(
            AboutUsItemInfo(
                "+852 37258086",
                R.color.cl_6372b0,
                "投诉电话",
                object : View.OnClickListener {
                    override fun onClick(v: View?) {
                        dealPhone("+852 37258086")
                    }
                })
        )

        contractUsInfoList.add(AboutUsItemInfo("<EMAIL>", null, "投诉邮箱"))
        //地址:香港尖沙咀广东道9号海港城港威大厦6座2907-8
        contractUsInfoList.add(
            AboutUsItemInfo(
                "香港尖沙咀广东道9号海港城港威大厦6座2907-8",
                null,
                "地址"
            )
        )
        val adpContactUs = AdpUnLoginAboutUs()
        adpContactUs.setList(contractUsInfoList)
        binding.layUnlogin.rvCompanyInfo.layoutManager =
            LinearLayoutManager(binding.layUnlogin.rvCompanyInfo.context)
        binding.layUnlogin.rvCompanyInfo.adapter = adpContactUs


        val loginPrivateDelegate =
            Invoker.getInstance().navigation(ILoginProvider::class.java).loginAgreementCheck(binding.layUnlogin.cbAgreement, {
                binding.layUnlogin.cbAgreement.isChecked = it
                if (it) {
                    binding.layUnlogin.layBindHint.visibility = View.GONE
                }
            }, {
                HbAnalytics.onClick("613610")
            })
        binding.layUnlogin.tvLogin.setOnClickListener {
            HbAnalytics.onClick("613600")
            login()
        }

        val showWechatLogin =
            Invoker.getInstance().navigation(IWechatProvider::class.java).wechatEnable()
        binding.layUnlogin.tvWechatLogin.visibility =
            if (showWechatLogin) View.VISIBLE else View.GONE
        binding.layUnlogin.tvWechatLogin.setOnClickListener {
            HbAnalytics.onClick("613590")
            if (binding.layUnlogin.cbAgreement.isChecked) {
                showAlermDlg("加载中", true, true)
                get(IWechatProvider::class.java).wechatLogin(
                    activity
                ) { t ->
                    showAlermDlg(null, 0)
                    val wechatLoginResult = t?.mData as? WeChatLoginResult
                    //LogUtils.pop("登录成功:${wechatLoginResult != null}")
                    //是否绑定手机,没绑定手机之前都算未登录
                    if (wechatLoginResult?.isBindHk == "1") {
                        //已绑定客户号,算登录成功
                        if (TextUtils.isEmpty(wechatLoginResult.hkCustNo)) {
                            //返回的香港客户号为空,不正常
                            return@wechatLogin
                        }
                        refreshByWeChatLogin = true
                        //先保存用户信息,后边查用户信息需要
                        get(ILoginProvider::class.java).saveLoginInfo(
                            wechatLoginResult.hkCustNo!!,
                            LoginType.wechat,
                            "",
                            "",
                            ""
                        )
                        //afterLoginSuccess最终会触发LoginObserver
                        get(ILoginProvider::class.java).afterLoginSuccess(this@FragMine)
                        //刷新微信绑定信息
                        UserDataHelper.getDataManager(DataIds.ID_USER_WECHAT_BIND)
                            .scheduleRemoteFetch(FetchAction.dataChanged)

                    } else {
                        //未绑定手机号,跳转绑定手机号
                        RouterHelper.launchFrag(this, LoginRouterPath.PATH_BIND_PHONE)
                    }


                }
            } else {
                binding.layUnlogin.layBindHint.visibility = View.VISIBLE
                loginPrivateDelegate.startAnimationAndShowTips(binding.layUnlogin.layBindHint)
            }
        }


    }


    private fun login() {
        Invoker.getInstance()
            .navigation(ILoginProvider::class.java)
            .login(
                LoginParams(activity ?: return, LoginType.captcha, null),
                "FragMine",
                object : LoginCallback {
                    override fun onComplete(result: LoginResult) {
                        LogUtils.d(
                            "LoginAccountMgr",
                            "FragMine监听回调,登录后,通过LoginObserver实现UI刷新,没有定位tab功能了,result=" + result.success
                        )
                    }
                })

    }


    /**
     * 设置最新一条未读消息,显示内容
     */
    private fun setLastedMsgModuleUIData(unReadNum: Int, messageInfo: MessageItem?) {
        binding.layoutLogin.layMessageModule.tvMineMsgModuleUnreadMsgCount.text =
            SpannableUtils.formatStr(
                SpannableItem("您有"),
                SpannableItem(
                    unReadNum.toString(),
                    DensityUtils.dip2px(21f),
                    ColorUtils.parseColor("#C51D25")
                ),
                SpannableItem("条", ColorUtils.parseColor("#C51D25")),
                SpannableItem("未读消息"),
            )
        val msgContent = FundTextUtils.showTextEmpty(messageInfo?.messageTitle) + " "
        binding.layoutLogin.layMessageModule.tvMineMsgModuleMsgTitle.text = msgContent
        binding.layoutLogin.layMessageModule.tvMineMsgModuleMsgDate.visibility = View.GONE
        val msgDate = FundTextUtils.formatDate(
            messageInfo?.messageDate,
            DateUtils.DATEF_YMD, DateUtils.DATEF_YMD_9, DateUtils.DATEF_YMD_8
        )
        binding.layoutLogin.layMessageModule.tvMineMsgModuleMsgDate.text = msgDate
        val leftWidth = SysUtils.getWidth(activity) - DensityUtils.dp2px(60f)
        val dateStrLen =
            binding.layoutLogin.layMessageModule.tvMineMsgModuleMsgDate.paint.measureText(msgDate)
        val leftSpace = leftWidth - dateStrLen
        val msgStrLen =
            binding.layoutLogin.layMessageModule.tvMineMsgModuleMsgTitle.paint.measureText(
                msgContent
            )
        if (leftSpace < msgStrLen) {
            binding.layoutLogin.layMessageModule.tvMineMsgModuleMsgDate.visibility = View.GONE
            val label = RadiusSpan.Builder()
                .setText(msgDate)
                .setTextSize(14f)
                .setBgColor(Color.TRANSPARENT)
                .setTxtColor(ColorUtils.parseColor("#999999"))
                .setRightMargin(DensityUtils.dp2px(1f))
                .build()
            val sp = SpannableString("$msgContent ")
            sp.setSpan(
                label,
                msgContent.length,
                msgContent.length + 1,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            binding.layoutLogin.layMessageModule.tvMineMsgModuleMsgTitle.text = sp
        } else {
            binding.layoutLogin.layMessageModule.tvMineMsgModuleMsgDate.visibility = View.VISIBLE
        }
        binding.layoutLogin.layMessageModule.tvMineMsgModuleMsgDesc.text =
            FundTextUtils.showTextEmpty(messageInfo?.messageDesc)
        //消息模块点击事件
        binding.layoutLogin.layMessageModule.layMineMsgTitle.setOnClickListener {
            //点击 "未读消息标题",跳转到消息列表页面
            launcherToMsgPage(0)
            HbAnalytics.onClick("611180")
        }
        //窗口消息摘要
        binding.layoutLogin.layMessageModule.root.setOnClickListener {
            if (unReadNum == 1) {
                //若{未读消息数量}=1，则跳转至各场景流程页面
                activity?.let {
                    MessageLauncher.launcherMsg(it, messageInfo, true, isMineClick = true)
                }
            } else {
                //若{未读消息数量}>1，则跳转消息列表页面，默认选中消息列表
                launcherToMsgPage(0)
            }
            HbAnalytics.onClick("611190")
        }
    }

    /**
     * 设置toolbar上的消息红点状态
     * @param unReadNum 未读消息数量
     * @param todoNum 待办消息数量
     */
    private fun setToolbarMsgRedIconStatus() {
        if (unReadNum > 0 || todoNum > 0) {
            binding.ivMineMsgRed.visibility = View.VISIBLE
        } else {
            binding.ivMineMsgRed.visibility = View.GONE
        }
    }

    /**
     * 跳转到消息列表页面(由于模块跳转,这里使用短命令方式跳转实现解耦)
     * @param pageIndex 跳转到消息中心,定位列表下标
     */
    private fun launcherToMsgPage(pageIndex: Int) {
        val url = "T=(HWMSG)&V=(${pageIndex})"
        PushDispatchHelper.pushDispatch(activity, url)
    }


    /**
     * 显示资产提示弹框
     */
    private fun showAmtHintDlg(titleType: AmtDescTitle) {
        MineAmtDescDlg.getInstance(
            NavHelper.obtainArg(
                "", ValConfig.IT_TYPE, titleType.type,
                ValConfig.IT_ENTITY, ApiImplUserInfo.getUserAssets(),
                ValConfig.IT_VALUE_1, mQxnProdBody
            )
        ).show(childFragmentManager, null)
    }

    private fun dealPhone(phone: String) {
        val intent = Intent(Intent.ACTION_DIAL, "tel:$phone".toUri())
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
        activity?.startActivity(intent)
    }

}