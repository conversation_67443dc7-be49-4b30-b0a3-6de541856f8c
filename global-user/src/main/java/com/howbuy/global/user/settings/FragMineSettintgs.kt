package com.howbuy.global.user.settings

import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.alibaba.android.arouter.facade.annotation.Route
import com.howbuy.android.analytics.annotation.pv.PvInfo
import com.howbuy.fund.base.config.SpConfig
import com.howbuy.fund.base.config.ValConfig
import com.howbuy.fund.base.frag.AbsFragViewBinding
import com.howbuy.fund.base.nav.NavHelper
import com.howbuy.fund.base.storage.CommonStorageUtils
import com.howbuy.fund.base.utils.FundTextUtils
import com.howbuy.global.data_api.getHkCustNo
import com.howbuy.global.user.PATH_FRAG_MINE_SETTINGS
import com.howbuy.global.user.R
import com.howbuy.global.user.databinding.FragMineSettingsLayoutBinding

/**
 * description.
 * 我的- 设置页面
 * tao.liang
 * 2024/7/10
 */
@PvInfo(pageId = "352730", level = "3", name = "设置页", className = "FragMineSettintgs")
@Route(path = PATH_FRAG_MINE_SETTINGS)
class FragMineSettintgs : AbsFragViewBinding<FragMineSettingsLayoutBinding>() {

    private var mCurAmtTypeCode: String? = ""
    private var mCurAmtType: AmtType? = AmtType.DOLLAR

    //标记用户是否操作选择了币种
    private var mLastAmtTypeCode: String? = ""

    override fun createViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragMineSettingsLayoutBinding {
        return FragMineSettingsLayoutBinding.inflate(inflater, container, false)
    }

    override fun getFragLayoutId(): Int {
        return R.layout.frag_mine_settings_layout
    }

    override fun stepAllViews(root: View?, savedInstanceState: Bundle?) {
        super.stepAllViews(root, savedInstanceState)
        binding.layAmtRmb.setOnClickListener {
            changeAmtType(AmtType.RMB.code)
        }

        binding.layAmtDollar.setOnClickListener {
            changeAmtType(AmtType.DOLLAR.code)
        }
    }

    override fun parseArgment(arg: Bundle?) {
        mCurAmtTypeCode = arg?.getString(ValConfig.IT_VALUE_1)
        changeAmtType(mCurAmtTypeCode)
    }

    override fun onKeyBack(fromBar: Boolean): Boolean {
        val bundle = NavHelper.obtainArg("", ValConfig.IT_VALUE_1, mCurAmtTypeCode)
        if (!TextUtils.equals(mLastAmtTypeCode, mCurAmtTypeCode)) {
            NavHelper.success(activity, bundle)
        } else {
            NavHelper.fail(activity, bundle)
        }
        return super.onKeyBack(fromBar)
    }

    private fun changeAmtType(amtType: String?) {
        if (amtType == AmtType.RMB.code) {
            mCurAmtType = AmtType.RMB
            mCurAmtTypeCode = AmtType.RMB.code
        } else {
            mCurAmtType = AmtType.DOLLAR
            mCurAmtTypeCode = AmtType.DOLLAR.code
        }
        initViewStatus()
        //保存用户选择的币种
        CommonStorageUtils.putString(
            SpConfig.USER_SELECT_AMT_TYPE + "_" + getHkCustNo(),
            mCurAmtTypeCode
        )
    }

    private fun initViewStatus() {
        if (mCurAmtTypeCode == AmtType.RMB.code) {
            //选中 人民币
            binding.ivDollarOutCircle.setImageResource(R.drawable.dot_afafaf_stroke)
            binding.ivDollarInnerCircle.visibility = View.GONE
            binding.ivRmbOutCircle.setImageResource(R.drawable.dot_ca3538_stroke)
            binding.ivRmbInnerCircle.visibility = View.VISIBLE
        } else {
            //选中美元
            binding.ivDollarOutCircle.setImageResource(R.drawable.dot_ca3538_stroke)
            binding.ivDollarInnerCircle.visibility = View.VISIBLE
            binding.ivRmbOutCircle.setImageResource(R.drawable.dot_afafaf_stroke)
            binding.ivRmbInnerCircle.visibility = View.GONE
        }
        val txt = FundTextUtils.showTextEmpty(mCurAmtType?.desc)
        binding.tvHintMsg.text =
            "*若您的持仓中包含非${txt}产品，计算汇总资产时会根据参考汇率换算成${txt}后加总。"
    }
}