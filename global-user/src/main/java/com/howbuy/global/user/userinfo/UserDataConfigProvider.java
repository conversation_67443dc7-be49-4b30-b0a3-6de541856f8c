package com.howbuy.global.user.userinfo;

import android.content.Context;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.howbuy.account.api.ApiConfig;
import com.howbuy.account.api.ConfigProvider;

import org.jetbrains.annotations.NotNull;

import java.util.LinkedList;
import java.util.List;

@Route(path = "/user/UserDataConfigProvider")
public class UserDataConfigProvider implements ConfigProvider {

    @NotNull
    @Override
    public List<ApiConfig<?>> getConfigList() {
        ApiConfigFactory configFactory = new ApiConfigFactory();

        List<ApiConfig<?>> configList = new LinkedList<>();
        configList.add(configFactory.createUserInfoConfig()); //用户信息
        configList.add(configFactory.createUserAssetsConfig()); //用户资产
        configList.add(configFactory.createUserMessageConfig()); //用户消息
        configList.add(configFactory.createUserHoldConfig()); //用户持仓列表
        configList.add(configFactory.createSmTgConfig()); //持牌人
        configList.add(configFactory.createUserBindConfig()); //用户微信绑定状态

        return configList;
    }

    @Override
    public void init(Context context) {
        doNothing();
    }

    private void doNothing() {
        //do nothing
    }
}
