package com.howbuy.global.user.userinfo

import com.howbuy.account.remote.FetchAction
import com.howbuy.account.remote.RemoteFetcher
import com.howbuy.account.remote.ResponseValue
import com.howbuy.fund.net.HttpCaller
import com.howbuy.global.data_api.apiUserInfo
import com.howbuy.global.user.UserRequest
import com.howbuy.global.user.entity.UserLicensee
import io.reactivex.Observable
import io.reactivex.schedulers.Schedulers

/**
 * 当前登录用户是否持牌人
 * <AUTHOR>
 * @Date 2025/5/8
 * @Version V2.9
 */
class UserLicenseeFetcher : RemoteFetcher<UserLicensee> {
    override fun fetch(action: FetchAction): Observable<ResponseValue<UserLicensee>> {
        return Observable.fromCallable {
            val map = HashMap<String, Any?>()
            map["hkCustNo"] = apiUserInfo().getHkCustNo()
            val params = UserRequest.createTradeReqParams(
//                UserRequest.CRM_CGI_HKACCOUNT_CUST_APP_ISLICENSEE,
                "https://m1.apifoxmock.com/m1/2829913-1212853-default/hkaccount/cust/app/isLicensee",
                UserLicensee::class.java,
                true, null, true, 0, null, map
            )
            val result = HttpCaller.getInstance().request(params, null)
            val data: UserLicensee? = if (result.isSuccess && result.mData is UserLicensee) {
                result.mData as UserLicensee
            } else {
                null
            }
            ResponseValue(data, result.isSuccess, result.mErr)
        }.subscribeOn(Schedulers.io())
    }
}