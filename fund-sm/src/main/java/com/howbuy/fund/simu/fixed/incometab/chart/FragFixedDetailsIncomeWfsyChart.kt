package com.howbuy.fund.simu.fixed.incometab.chart

import android.content.res.Configuration
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.widget.ImageView
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.content.ContextCompat
import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.github.mikephil.charting.interfaces.datasets.ILineDataSet
import com.github.mikephil.charting.utils.Transformer
import com.howbuy.fund.base.utils.FundTextUtils
import com.howbuy.fund.base.utils.FundUtils
import com.howbuy.fund.chart.mpchart.DefaultPercentValueFormat
import com.howbuy.fund.simu.R
import com.howbuy.fund.simu.fixed.incometab.FragFixedDetailsIncome
import com.howbuy.fund.simu.fixed.incometab.data.ShareViewModelProvider
import com.howbuy.fund.simu.fixed.incometab.entity.FixedIncomeSyChartData
import com.howbuy.fund.simu.fixed.incometab.vm.VmFixedDetailsIncomeWfsy
import com.howbuy.fund.simu.utils.SmChartHelper
import com.howbuy.lib.compont.GlobalApp
import com.howbuy.lib.utils.DateUtils
import com.howbuy.lib.utils.DensityUtils
import com.howbuy.lib.utils.MathUtils
import kotlinx.android.synthetic.main.frag_fixed_details_income_wfsy_chart.chart_layout
import kotlinx.android.synthetic.main.frag_fixed_details_income_wfsy_chart.empty
import kotlinx.android.synthetic.main.frag_fixed_details_income_wfsy_chart.hbline_chart
import kotlinx.android.synthetic.main.frag_fixed_details_income_wfsy_chart.iv_watermark
import kotlinx.android.synthetic.main.frag_fixed_details_income_wfsy_chart.lay_chart_legend_click
import kotlinx.android.synthetic.main.frag_fixed_details_income_wfsy_chart.lay_time_setting
import kotlinx.android.synthetic.main.frag_fixed_details_income_wfsy_chart.lay_value_mark
import kotlinx.android.synthetic.main.frag_fixed_details_income_wfsy_chart.loading
import kotlinx.android.synthetic.main.frag_fixed_details_income_wfsy_chart.tv_click_date
import kotlinx.android.synthetic.main.frag_fixed_details_income_wfsy_chart.tv_fund_value_click
import kotlinx.android.synthetic.main.frag_fixed_details_income_wfsy_chart.tv_quan_shang_fullscreen
import kotlinx.android.synthetic.main.frag_fixed_details_income_wfsy_chart.tv_time_setting
import kotlinx.android.synthetic.main.frag_fixed_details_income_wfsy_chart.tv_value_mark
import kotlinx.android.synthetic.main.frag_fixed_details_income_wfsy_chart.v_end_value_dot

/**
 * @Description 固定收益-份额型-收益表现tab-万份收益 走势图
 * <AUTHOR>
 * @Date 2023/6/12
 * @Version v824
 */
class FragFixedDetailsIncomeWfsyChart : FragFixedDetailsIncomeBaseChart(), View.OnClickListener {

    private val mEntryList: MutableList<Entry> = mutableListOf() //本基金净值绘图数据
    private val mList: MutableList<FixedIncomeSyChartData.Item> = mutableListOf() //原始数据

    private lateinit var mVm: VmFixedDetailsIncomeWfsy

    override fun getFragLayoutId(): Int {
        return R.layout.frag_fixed_details_income_wfsy_chart
    }

    override fun stepAllViews(root: View?, savedInstanceState: Bundle?) {
        super.stepAllViews(root, savedInstanceState)
        tv_time_setting.setOnClickListener(this)
        lay_chart_legend_click.setOnClickListener(this)
        tv_quan_shang_fullscreen.setOnClickListener(this)
    }

    override fun initVm() {
        mPageType = FragFixedDetailsIncome.TYPE_WFSY
        mVm = ShareViewModelProvider.get(this, VmFixedDetailsIncomeWfsy::class.java, mCode)
        mVm.showData.observe(this) {
            if (TextUtils.equals(it, mType)) {
                renderData()
            }
        }
    }

    override fun vm() = mVm

    override fun parseArgment(arg: Bundle?) {
        super.parseArgment(arg)
        if (mFromLandMode) {
            lay_chart_legend_click.visibility = View.GONE
            tv_quan_shang_fullscreen.visibility = View.GONE
        }
    }

    override fun waterMarkView(): ImageView = iv_watermark

    private var hasRenderChart = false
    private fun renderData() {
        hideLoadingView()
        val data = mVm.mChartMap[mType]
        if (data == null || data.chartList.isNullOrEmpty()) {
            hasRenderChart = false
            showEmptyView()
        } else {
            hasRenderChart = true
            hideEmptyView()
            mList.clear()
            mList.addAll(data.chartList)
            setLineChartData(data.chartList)
        }
    }

    private fun setLineChartData(list: List<FixedIncomeSyChartData.Item>) {
        mEntryList.clear()
        dateIndexs.clear()
        val minMax = mutableListOf<Float>()
        for ((i: Int, item: FixedIncomeSyChartData.Item) in list.withIndex()) {
            dateIndexs.add(FundTextUtils.showTextEmpty(item.date))
            val f = MathUtils.formatF(MathUtils.forValF(item.wfsy, 0f), MathUtils.FDECIMAL4).toFloat()
            mEntryList.add(Entry(i.toFloat(), f, item))
            minMax.add(f)
        }
        setRightAisleFormat(minMax.minOrNull() ?: 0f, minMax.maxOrNull() ?: 0f)
        val showDate = "${list.firstOrNull()?.date ?: "--"}至${list.lastOrNull()?.date ?: "--"}"
        if (mFromLandMode) {
            tv_quan_shang_fullscreen.visibility = View.GONE
            if (needShowTimeSetting()) {
                //此处仅赋值横屏下自定义时间范围，是否显示由外层去控制
                (parentFragment as FragFixedDetailsIncomeBase).setSelectTime(showDate)
            }
        } else {
            tv_quan_shang_fullscreen.visibility = View.VISIBLE
            if (needShowTimeSetting()) {
                lay_time_setting.visibility = View.VISIBLE
                tv_time_setting.text = showDate
            } else {
                lay_time_setting.visibility = View.GONE
            }
        }
        drawChart()
    }

    override fun onPageScroll() {
        cleanHighLight()
    }

    override fun cleanHighLight() {
        hbline_chart.cleanHighlight()
        lay_chart_legend_click.visibility = View.GONE
        lay_value_mark.visibility = View.VISIBLE
        v_end_value_dot.visibility = View.VISIBLE
    }

    override fun renderTitle(index: Int, isClick: Boolean) {
        val leftValue: String?
        val rightValue: String?
        if (isClick) {
            leftValue = mList[index].date
            rightValue = mList[index].wfsy
            if (!mFromLandMode) {
                lay_chart_legend_click.visibility = View.VISIBLE
            }
            lay_value_mark.visibility = View.GONE
            v_end_value_dot.visibility = View.GONE
        } else {
            leftValue = FundTextUtils.showTextEmpty(dateIndexs.lastOrNull())
            rightValue = FundTextUtils.showTextEmpty(mList.lastOrNull()?.wfsy)
            hbline_chart.cleanHighlight()
            lay_chart_legend_click.visibility = View.GONE
            lay_value_mark.visibility = View.VISIBLE
            v_end_value_dot.visibility = View.VISIBLE
        }
        if (mFromLandMode) {
            //横屏手指触摸
            mVm.onLandFloatView(mType, leftValue, rightValue, !isClick)
        } else {
            tv_click_date.text = leftValue
            tv_fund_value_click.text = FundUtils.formatFundValue(rightValue)
        }
    }

    /**
     * 调整Y轴上显示的坐标值
     */
    private fun setRightAisleFormat(min: Float, max: Float) {
        /**
         * 走势图纵坐标轴调整:
         * 最高收益*（1+(-)10%），最低收益*（1-10%）的结果，个位数位上就近取5倍数的整数
         * （如，最高值*（1+10%）=6%，那上限则直接取为10%；
         * 最低值*（1+(-)10%）=-7%,那下线取值为-10%）以此为纵坐标轴的上线和下线值，
         * 中间纵坐标轴刻度线五等分(相当于有6个刻度)；
         */
        val yAisleMax = SmChartHelper.formatMinMaxValue(max, true, 5)
        mRightYAxis.axisMaximum = yAisleMax

        val yAisleMin = SmChartHelper.formatMinMaxValue(min, false, 5)
        mRightYAxis.axisMinimum = yAisleMin

        mRightYAxis.setLabelCount(6, true)
        mRightYAxis.valueFormatter = DefaultPercentValueFormat(4, false)
    }

    private fun drawChart() {
        if (!isAdded) return
        val sets = java.util.ArrayList<ILineDataSet>()
        if (mEntryList.isNotEmpty()) {
            val fundSet = LineDataSet(mEntryList, "main")
            fundSet.mode = LineDataSet.Mode.LINEAR
            fundSet.axisDependency = YAxis.AxisDependency.RIGHT
            fundSet.lineWidth = 1f
            fundSet.color = ContextCompat.getColor(GlobalApp.getApp(), R.color.fd_f92d2d)
            fundSet.isHighlightEnabled = true
            fundSet.highlightLineWidth = 1f
            fundSet.highLightColor = ContextCompat.getColor(GlobalApp.getApp(), R.color.fd_f92d2d)
            fundSet.setDrawVerticalHighlightIndicator(true)
            fundSet.setDrawHorizontalHighlightIndicator(false)
            fundSet.setDrawValues(false)
            fundSet.setDrawCircles(false)
            fundSet.circleRadius = 0f
            fundSet.setDrawFilled(true)
            fundSet.lineWidth = 1.3f
            fundSet.fillDrawable = ContextCompat.getDrawable(GlobalApp.getApp(), R.drawable.bg_sm_chart_fill_red)
            fundSet.setFillFormatter { _, dataProvider -> dataProvider.yChartMin }
            sets.add(fundSet)
        }
        renderTitle(0, false)
        hbline_chart.data = LineData(sets)
        hbline_chart.invalidate()
        setFullScreenPosition(hbline_chart, tv_quan_shang_fullscreen)
        hbline_chart.postDelayed({ drawEventLabel() }, 50)
    }

    private fun drawEventLabel() {
        try {
            lay_value_mark.visibility = View.VISIBLE
            v_end_value_dot.visibility = View.VISIBLE
            val dataSetByIndex = hbline_chart.lineData?.getDataSetByLabel("main", true) as? LineDataSet
            dataSetByIndex?.let {
                val trans: Transformer = hbline_chart.getTransformer(dataSetByIndex.axisDependency)
                val maxPoint = trans.getPixelForValues(mEntryList.lastOrNull()?.x ?: 0f, mEntryList.lastOrNull()?.y ?: 0f)
                val highX = maxPoint.x.toFloat()
                val highY = maxPoint.y.toFloat()
                val text = "${DateUtils.dateFormat(dateIndexs.lastOrNull(), DateUtils.DATEF_YMD_, DateUtils.DATEF_YMD_4)}, ${
                    mEntryList.lastOrNull()?.y.toString()
                }"
                tv_value_mark.text = text
                val constraintSet = ConstraintSet()
                constraintSet.clone(chart_layout)
                constraintSet.setMargin(R.id.v_end_value_dot, ConstraintSet.START, (highX - DensityUtils.dip2px(2.5f)).toInt())
                constraintSet.setMargin(R.id.v_end_value_dot, ConstraintSet.TOP, (highY - DensityUtils.dip2px(2.5f)).toInt())
                constraintSet.applyTo(chart_layout)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        hbline_chart.postDelayed({ drawEventLabel() }, 100)
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.tv_time_setting -> {
                if (parentFragment is FragFixedDetailsIncomeBase) {
                    (parentFragment as FragFixedDetailsIncomeBase).showTimeDlg()
                }
            }

            R.id.lay_chart_legend_click -> renderTitle(0, false)

            R.id.tv_quan_shang_fullscreen -> toLand()
        }
    }

    override fun showLoadingView() {
        loading.visibility = View.VISIBLE
    }

    override fun hideLoadingView() {
        loading.visibility = View.GONE
    }

    override fun hideEmptyView() {
        empty.visibility = View.GONE
    }

    override fun showEmptyView() {
        try {
            empty.visibility = View.VISIBLE
            mVm.onLandFloatView(mType, null, null, true)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}