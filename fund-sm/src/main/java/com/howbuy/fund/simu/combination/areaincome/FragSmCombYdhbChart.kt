package com.howbuy.fund.simu.combination.areaincome

import android.graphics.Color
import android.graphics.Paint
import android.os.Bundle
import android.text.TextUtils
import androidx.core.content.ContextCompat
import android.view.MotionEvent
import android.view.View
import android.widget.FrameLayout
import com.github.mikephil.charting.charts.BarChart
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.data.BarData
import com.github.mikephil.charting.data.BarDataSet
import com.github.mikephil.charting.data.BarEntry
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.formatter.ValueFormatter
import com.github.mikephil.charting.highlight.Highlight
import com.github.mikephil.charting.interfaces.datasets.IBarDataSet
import com.github.mikephil.charting.listener.ChartTouchListener
import com.github.mikephil.charting.listener.OnChartGestureListener
import com.github.mikephil.charting.listener.OnChartValueSelectedListener
import com.github.mikephil.charting.utils.Utils
import com.google.gson.GsonUtils
import com.google.gson.reflect.TypeToken
import com.howbuy.fund.base.config.ValConfig
import com.howbuy.fund.base.frag.AbsHbFrag
import com.howbuy.fund.base.livedata.LiveDataBus
import com.howbuy.fund.simu.utils.LiveDataEventKey
import com.howbuy.fund.base.utils.FundUtils
import com.howbuy.fund.base.utils.LargeBundleHelper
import com.howbuy.fund.chart.mpchart.DefaultPercentValueFormat
import com.howbuy.fund.simu.R
import com.howbuy.fund.simu.entity.CombineChartData
import com.howbuy.fund.simu.entity.CombineDetail
import com.howbuy.fund.simu.entity.CombineSimple
import com.howbuy.fund.simu.entity.SmCombAreaIncomeBody
import com.howbuy.fund.simu.utils.SmChartHelper.formatMinMaxValue
import com.howbuy.lib.utils.DateUtils
import com.howbuy.lib.utils.MathUtils
import kotlinx.android.synthetic.main.frag_sm_comb_ydhb_chart_layout.*
import java.util.*

/**
 * class description.
 * 私募组合模拟-区间收益模块-月度回报图表
 * <AUTHOR>
 * @date 2021/8/13
 */
class FragSmCombYdhbChart : AbsHbFrag() {

    var body: SmCombAreaIncomeBody? = null
    var mCombSimpleData: CombineSimple? = null

    //成分基金列表数据
    var mCombFundList: MutableList<CombineChartData.Item>? = null
    private val minMaxLineFundList = ArrayList<Float>()
    private val minMaxLineBaseList = ArrayList<Float>()
    private val minMaxLineBase2List = ArrayList<Float>()
    private var mBjjzCount = 0

    override fun getFragLayoutId(): Int = R.layout.frag_sm_comb_ydhb_chart_layout

    override fun stepAllViews(root: View?, savedInstanceState: Bundle?) {
        initViews()
        LiveDataBus.get().with(LiveDataEventKey.BUS_KEY_SM_YDHB_DATA_UPDATE, Bundle::class.java).observeBus(this, {
            updateArgs(it)
        })
        LiveDataBus.get().with(LiveDataEventKey.BUS_KEY_SIMU_COMBINE_CLEAR_HIGHLIGHT, Boolean::class.java).observeBus(this, {
            clearHighLight()
        })
    }

    fun clearHighLight() {
        if (bar_chart_comb_area_income.highlighted != null) {
            bar_chart_comb_area_income.highlightValues(null)
            lay_ydhb_lenged_title_click.visibility = View.GONE
        }
    }

    override fun parseArgment(arg: Bundle?) {
        body = LargeBundleHelper.getData(LargeBundleHelper.KEY_SM_COMB_INCOME_DETAILS) as? SmCombAreaIncomeBody
//        body = arg?.getParcelable<SmCombAreaIncomeBody>(ValConfig.IT_ENTITY)
        //成分基金列表
        mCombFundList = arg?.getParcelableArrayList<CombineChartData.Item>(ValConfig.IT_VALUE_3)
        mCombSimpleData = arg?.getParcelable<CombineSimple>(ValConfig.IT_BEAN)
        if (!TextUtils.isEmpty(mCombSimpleData?.bjjzArray)) {
            val bjjzList = GsonUtils.toObj<MutableList<CombineDetail.BjjzItem>>(mCombSimpleData?.bjjzArray,
                    object : TypeToken<MutableList<CombineDetail.BjjzItem>>() {}.type)
            lay_comb_area_title_click_3.visibility = View.GONE
            tv_ydhb_chart_lenged_indicator_2.visibility = View.GONE
            tv_ydhb_chart_lenged_name_2.visibility = View.GONE
            if (bjjzList?.size == 1) {
                mBjjzCount = 1
                tv_ydhb_chart_lenged_name_1.text = bjjzList[0].bjjzName
                tv_ydhb_chart_click_lenged_name_1.text = bjjzList[0].bjjzName
            } else if (bjjzList?.size == 2) {
                lay_comb_area_title_click_3.visibility = View.VISIBLE
                tv_ydhb_chart_lenged_indicator_2.visibility = View.VISIBLE
                tv_ydhb_chart_lenged_name_2.visibility = View.VISIBLE
                mBjjzCount = 2
                tv_ydhb_chart_lenged_name_1.text = bjjzList[0].bjjzName
                tv_ydhb_chart_lenged_name_2.text = bjjzList[1].bjjzName
                tv_ydhb_chart_click_lenged_name_1.text = bjjzList[0].bjjzName
                tv_ydhb_chart_click_lenged_name_2.text = bjjzList[1].bjjzName

            }
        }
        setUIData(body)
    }

    private fun updateArgs(bundle: Bundle?) {
        //恢复默认的不可左右滑动
        initViews()
        parseArgment(bundle)
        setUIData(body)
    }

    private fun initViews() {
        bar_chart_comb_area_income.setBackgroundColor(Color.WHITE)
        bar_chart_comb_area_income.extraTopOffset = 0f
        bar_chart_comb_area_income.extraBottomOffset = 20f
        bar_chart_comb_area_income.extraLeftOffset = 0f
        bar_chart_comb_area_income.extraRightOffset = 0f
        bar_chart_comb_area_income.setDrawBarShadow(false)
        bar_chart_comb_area_income.description.isEnabled = false
        // scaling can now only be done on x- and y-axis separately
        bar_chart_comb_area_income.setPinchZoom(false)
        bar_chart_comb_area_income.setScaleEnabled(false)
        bar_chart_comb_area_income.setDrawGridBackground(false)
        //显示X轴
        bar_chart_comb_area_income.xAxis.position = XAxis.XAxisPosition.BOTTOM
        bar_chart_comb_area_income.xAxis.setDrawGridLines(false)
        bar_chart_comb_area_income.xAxis.setDrawAxisLine(true)
        bar_chart_comb_area_income.xAxis.axisLineColor = ContextCompat.getColor(activity!!, R.color.cl_eaeaea)
        bar_chart_comb_area_income.xAxis.textColor = ContextCompat.getColor(activity!!, R.color.cl_4b4e61)
        bar_chart_comb_area_income.xAxis.textSize = 9.5f
        bar_chart_comb_area_income.xAxis.isEnabled = true
        bar_chart_comb_area_income.xAxis.granularity = 1f
        bar_chart_comb_area_income.xAxis.setCenterAxisLabels(true)
        //显示y轴
        val left = bar_chart_comb_area_income.axisLeft
        left.setDrawLabels(true)
        left.setDrawAxisLine(false)
        left.setDrawGridLines(true)
        bar_chart_comb_area_income.axisLeft.gridColor = ContextCompat.getColor(activity!!, R.color.cl_eaeaea)
        left.setLabelCount(3, true)
        left.setDrawZeroLine(true)
        bar_chart_comb_area_income.axisLeft.zeroLineColor = ContextCompat.getColor(activity!!, R.color.cl_eaeaea)
        bar_chart_comb_area_income.axisLeft.textColor = ContextCompat.getColor(activity!!, R.color.cl_999999)
        left.zeroLineWidth = 0.7f
        bar_chart_comb_area_income.axisLeft.isEnabled = true
        bar_chart_comb_area_income.axisLeft.valueFormatter = DefaultPercentValueFormat(0, true)
        bar_chart_comb_area_income.axisRight.isEnabled = false
        //禁用图例
        bar_chart_comb_area_income.legend.isEnabled = false
        bar_chart_comb_area_income.setHighlightGroupStyle()
        bar_chart_comb_area_income.setCancelHighLight(false)
        bar_chart_comb_area_income.setOnChartValueSelectedListener(object : OnChartValueSelectedListener {
            override fun onValueSelected(e: Entry?, highlight: Highlight?) {
                try {
                    lay_ydhb_lenged_title_click.visibility = View.VISIBLE
                    val pos = bar_chart_comb_area_income.data.dataSets[highlight?.dataSetIndex
                            ?: 0].getEntryIndex(e as BarEntry?)
                    val y1 = MathUtils.forValF(body?.dataArray?.get(pos)?.ydhb, -99999f)
                    if (y1 >= 0 || y1== -99999f) {
                        tv_ydhb_marker_lenged.setBackgroundResource(R.drawable.xml_dot_red)
                    } else {
                        tv_ydhb_marker_lenged.setBackgroundResource(R.drawable.xml_dot_green)
                    }
                    FundUtils.formatRate(tv_ydhb_chart_click_lenged_group_value, body?.dataArray?.get(pos)?.ydhb)
                    FundUtils.formatRate(tv_ydhb_chart_click_lenged_value_1, body?.dataArray?.get(pos)?.bjydhb)
                    FundUtils.formatRate(tv_ydhb_chart_click_lenged_value_2, body?.dataArray?.get(pos)?.bjydhb2)
                } catch (e: java.lang.Exception) {
                    e.printStackTrace()
                }
            }

            override fun onNothingSelected() {

            }
        })
        bar_chart_comb_area_income.onChartGestureListener = object : TouchListener() {
            override fun onChartGestureEnd(me: MotionEvent?, lastPerformedGesture: ChartTouchListener.ChartGesture?) {
                if ((me?.action == MotionEvent.ACTION_CANCEL || me?.action == MotionEvent.ACTION_UP) && lastPerformedGesture == ChartTouchListener.ChartGesture.DRAG) {
                    //当滑动barchart时,清除高亮线
                    bar_chart_comb_area_income.highlightValues(null)
                    lay_ydhb_lenged_title_click.visibility = View.GONE
                }
            }
        }
        lay_ydhb_lenged_title_click.setOnClickListener { //当滑动barchart时,清除高亮线
            bar_chart_comb_area_income.highlightValues(null)
            lay_ydhb_lenged_title_click.visibility = View.GONE
        }
    }

    fun setUIData(body: SmCombAreaIncomeBody?) {
        val realList = mutableListOf<SmCombAreaIncomeBody.MonthItem>()
        body?.dataArray?.forEach {
            if (it.ydhbDate?.contains("成立当年") == false) {
                realList.add(it)
            }
        }
        if (realList.isNotEmpty()) {
            bar_chart_comb_area_income.xAxis.valueFormatter = object : ValueFormatter() {
                override fun getFormattedValue(value: Float): String {
                    try {
                        realList.let {
                            return DateUtils.dateFormat(it[Math.min(Math.max(value.toInt(), 0), it.size - 1)].ydhbDate, DateUtils.DATEF_YMD_10, DateUtils.DATEF_YMD_11)
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                    return ""
                }
            }
            drawBarchart(realList)
        } else {
            val p = Paint(1)
            p.color = Color.parseColor("#E1E1E1")
            p.textAlign = Paint.Align.CENTER
            p.textSize = Utils.convertDpToPixel(20f)
            bar_chart_comb_area_income.setPaint(p, 7)
            bar_chart_comb_area_income.setNoDataText("暂无数据")
            bar_chart_comb_area_income.invalidate()
        }
    }

    private fun drawBarchart(dataList: MutableList<SmCombAreaIncomeBody.MonthItem>) {
        minMaxLineFundList.clear()
        minMaxLineBaseList.clear()
        minMaxLineBase2List.clear()
        val valuesFund = ArrayList<BarEntry>()
        val valuesBase = ArrayList<BarEntry>()
        val valuesBase2 = ArrayList<BarEntry>()
        val colorsFund = ArrayList<Int>()
        val colorsBase = ArrayList<Int>()
        val colorsBase2 = ArrayList<Int>()
        //主柱图数据
        for (i in dataList.indices) {
            val item = dataList[i]
            val y = MathUtils.forValF(item.ydhb, 0f)
            val entry = BarEntry(i.toFloat(), y, dataList[i])
            valuesFund.add(entry)
            minMaxLineFundList.add(y)
            if (y >= 0) {
                colorsFund.add(-0x8788)
            } else {
                colorsFund.add(-0x813782)
            }
            //比较基准1
            val y1 = MathUtils.forValF(item.bjydhb, 0f)
            val entry1 = BarEntry(i.toFloat(), y1, dataList[i])
            valuesBase.add(entry1)
            minMaxLineBaseList.add(y1)
            colorsBase.add(ContextCompat.getColor(activity!!, R.color.cl_bbbdcb))
            //比较基准2
            if (mBjjzCount == 2) {
                val y2 = MathUtils.forValF(item.bjydhb2, 0f)
                val entry2 = BarEntry(i.toFloat(), y2, dataList[i])
                valuesBase2.add(entry2)
                minMaxLineBase2List.add(y2)
                colorsBase2.add(ContextCompat.getColor(activity!!, R.color.cl_4f79ab))
            }
        }

        //设置Y轴坐标数据
        setChartRightAlaisFormat(bar_chart_comb_area_income)
        //本组合柱形数据
        val list = ArrayList<IBarDataSet>()
        val set = BarDataSet(valuesFund, "")
        set.colors = colorsFund
        set.setDrawValues(false)
        set.isHighlightEnabled = true
        set.highLightColor = ContextCompat.getColor(activity!!, R.color.cl_bbbdcb)
        set.highLightAlpha = 51
        set.axisDependency = YAxis.AxisDependency.LEFT
        list.add(set)
        //比较基准1
        val set1 = BarDataSet(valuesBase, "")
        set1.colors = colorsBase
        set1.setDrawValues(false)
        set1.isHighlightEnabled = true
        set1.highLightColor = ContextCompat.getColor(activity!!, R.color.cl_bbbdcb)
        set1.highLightAlpha = 51
        set1.axisDependency = YAxis.AxisDependency.LEFT
        list.add(set1)
        //控制视图不柱图完全显示
        bar_chart_comb_area_income.xAxis.axisMinimum = 0f
        bar_chart_comb_area_income.xAxis.axisMaximum = dataList.size.toFloat()
        //比较基准2
        val set2 = BarDataSet(valuesBase2, "")
        set2.colors = colorsBase2
        set2.setDrawValues(false)
        set2.isHighlightEnabled = true
        set2.highLightColor = ContextCompat.getColor(activity!!, R.color.cl_bbbdcb)
        set2.highLightAlpha = 51
        set2.axisDependency = YAxis.AxisDependency.LEFT
        list.add(set2)

        val data = if (mBjjzCount == 2) {
            BarData(set, set1, set2)
        } else {
            BarData(set, set1)
        }
        //由堆积柱状图变为并排多列柱状图
        val groupSpace = 0.5f //柱状图组之间的间距
        val barSpace = 0f // x4 DataSet
        val barWidth = (1 - groupSpace) / (mBjjzCount + 1) // x4 DataSet

        //设置柱状图宽度
        data.barWidth = barWidth
        bar_chart_comb_area_income.data = data
        //(起始点、柱状图组间距、柱状图之间间距)
        data.groupBars(0f, groupSpace, barSpace)

        data.isHighlightEnabled = true
        bar_chart_comb_area_income.invalidate()
        bar_chart_comb_area_income.setVisibleXRangeMaximum(7f)

        val lp = iv_bg.layoutParams as FrameLayout.LayoutParams
        lp.leftMargin = bar_chart_comb_area_income.viewPortHandler.contentLeft().toInt()
        iv_bg.requestLayout()
    }

    /**
     * 重新调整Y轴上显示的坐标值
     */
    private fun setChartRightAlaisFormat(barChart: BarChart) {
        var min1 = 0f
        var max1 = 0f
        if (minMaxLineFundList.isNotEmpty()) {
            min1 = Collections.min(minMaxLineFundList)
            max1 = Collections.max(minMaxLineFundList)
        }
        var min2 = 0f
        var max2 = 0f
        if (minMaxLineBaseList.isNotEmpty()) {
            min2 = Collections.min(minMaxLineBaseList)
            max2 = Collections.max(minMaxLineBaseList)
        }
        val tempMin = Math.min(min1, min2)
        val tempMax = Math.max(max1, max2)
        var min3 = 0f
        var max3 = 0f
        if (minMaxLineBase2List.isNotEmpty()) {
            min3 = Collections.min(minMaxLineBase2List)
            max3 = Collections.max(minMaxLineBase2List)
        }
        val realMin = Math.min(tempMin, min3)
        val realMax = Math.max(tempMax, max3)
        /*
         * 走势图纵坐标轴调整:
         * 最高收益*（1+(-)10%），最低收益*（1-10%）的结果，个位数位上就近取5倍数的整数
         * （如，最高值*（1+10%）=6%，那上限则直接取为10%；
         * 最低值*（1+(-)10%）=-7%,那下线取值为-10%）以此为纵坐标轴的上线和下线值，
         * 中间纵坐标轴刻度线五等分(相当于有6个刻度)；
         *
         * 1. 如果全部是正数(负数):就是显示3个坐标点
         * 2. 如果部分是正数,部分是负数:
         * a. 如果 |最小值|>= |最大值|/2, 就是 5个点
         * b. 如果 |最小值|< |最大值|/2, 就是 4个点
         *
         */
        val yAisleMax = formatMinMaxValue(realMax, true, 10)
        val yAisleMin = formatMinMaxValue(realMin, false, 10)

        if (realMin >= 0) {
            if (Math.abs(realMax) < Math.abs(yAisleMax) / 2.0f) {
                //两条比较线中,各自的最小值为>=0,则绘制三个Y坐标点,起点为0,最大坐标点为YMax
                barChart.axisLeft.setLabelCount(2, true)
                barChart.axisLeft.axisMinimum = 0f
                barChart.axisLeft.axisMaximum = yAisleMax / 2
            } else {
                //两条比较线中,各自的最小值为>=0,则绘制三个Y坐标点,起点为0,最大坐标点为YMax
                barChart.axisLeft.setLabelCount(3, true)
                barChart.axisLeft.axisMinimum = 0f
                barChart.axisLeft.axisMaximum = yAisleMax
            }

        } else if (realMax <= 0) {
            if (Math.abs(realMin) < Math.abs(yAisleMin) / 2.0f) {
                //两条比较线中,各自的最小值为>=0,则绘制三个Y坐标点,起点为0,最小坐标点为YMin
                barChart.axisLeft.setLabelCount(2, true)
                barChart.axisLeft.axisMaximum = 0f
                barChart.axisLeft.axisMinimum = yAisleMin / 2
            } else {
                //两条比较线中,各自的最小值为>=0,则绘制三个Y坐标点,起点为0,最小坐标点为YMin
                barChart.axisLeft.setLabelCount(3, true)
                barChart.axisLeft.axisMaximum = 0f
                barChart.axisLeft.axisMinimum = yAisleMin
            }

        } else if (realMin * realMax < 0) {
            //如果|最小值|>=(|最大值|/2),则显示5个y坐标点,(最小,最大)为Math.abs(|最小值|,|最大值|)
            val ymaxVal = Math.max(Math.abs(yAisleMin), Math.abs(yAisleMax)).toInt()
            if (Math.abs(realMin) > ymaxVal / 2.0f && Math.abs(realMax) > ymaxVal / 2.0f) {
                barChart.axisLeft.setLabelCount(5, true)
                barChart.axisLeft.axisMaximum = ymaxVal.toFloat()
                barChart.axisLeft.axisMinimum = (-ymaxVal).toFloat()
            } else {
                //如果|最小值|<(|最大值|/2),且|最小值|>=|最大值|
                if (Math.abs(realMin) <= ymaxVal / 2.0f && Math.abs(realMax) <= ymaxVal / 2.0f) {
                    barChart.axisLeft.setLabelCount(3, true)
                    barChart.axisLeft.axisMaximum = ymaxVal / 2.0f
                    barChart.axisLeft.axisMinimum = -ymaxVal / 2.0f
                } else {
                    barChart.axisLeft.setLabelCount(4, true)
                    if (Math.abs(realMin) >= ymaxVal / 2.0f && Math.abs(realMax) < ymaxVal / 2.0f) {
                        barChart.axisLeft.axisMaximum = ymaxVal / 2.0f
                        barChart.axisLeft.axisMinimum = (-ymaxVal).toFloat()
                    } else {
                        barChart.axisLeft.axisMaximum = ymaxVal.toFloat()
                        barChart.axisLeft.axisMinimum = -ymaxVal / 2.0f
                    }
                }

            }
        }
    }

}

open class TouchListener : OnChartGestureListener {
    override fun onChartGestureEnd(me: MotionEvent?, lastPerformedGesture: ChartTouchListener.ChartGesture?) {

    }

    override fun onChartFling(me1: MotionEvent?, me2: MotionEvent?, velocityX: Float, velocityY: Float) {
    }

    override fun onChartSingleTapped(me: MotionEvent?) {
    }

    override fun onChartGestureStart(me: MotionEvent?, lastPerformedGesture: ChartTouchListener.ChartGesture?) {
    }

    override fun onChartScale(me: MotionEvent?, scaleX: Float, scaleY: Float) {
    }

    override fun onChartLongPressed(me: MotionEvent?) {
    }

    override fun onChartDoubleTapped(me: MotionEvent?) {
    }

    override fun onChartTranslate(me: MotionEvent?, dX: Float, dY: Float) {
    }
}