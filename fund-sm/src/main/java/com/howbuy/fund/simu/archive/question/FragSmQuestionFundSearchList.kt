package com.howbuy.fund.simu.archive.question

import android.content.Context
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar
import androidx.core.content.ContextCompat
import com.alibaba.android.arouter.facade.annotation.Route
import com.howbuy.component.widgets.ClearableEdittext
import com.howbuy.fund.base.aty.AtyEmpty
import com.howbuy.fund.base.config.ValConfig
import com.howbuy.fund.base.frag.AbsHbFrag
import com.howbuy.fund.base.router.fragpath.SmRouterPath
import com.howbuy.fund.base.utils.FundTextUtils
import com.howbuy.fund.common.search.AdpBaseSearch
import com.howbuy.fund.common.search.IQuerySearch
import com.howbuy.fund.net.entity.http.ReqNetOpt
import com.howbuy.fund.net.entity.http.ReqResult
import com.howbuy.fund.net.interfaces.IReqNetFinished
import com.howbuy.fund.simu.CommonSmBuilder
import com.howbuy.fund.simu.R
import com.howbuy.fund.simu.entity.SearchResultItem
import com.howbuy.fund.simu.entity.SmSearchResultData
import com.howbuy.hbrefresh.layout.api.RefreshLayout
import com.howbuy.hbrefresh.layout.listener.OnRefreshLoadmoreListener
import com.howbuy.lib.compont.GlobalApp
import com.howbuy.lib.utils.StrUtils
import kotlinx.android.synthetic.main.frag_sm_head_search_item.*

/**
 * Create by zsm on 2018/11/14.
 **/
@Route(path = SmRouterPath.PATH_FRAG_SM_QUESTION_FUND_SEARCH_LIST)
class FragSmQuestionFundSearchList : AbsHbFrag(), ClearableEdittext.IEditChanged, IReqNetFinished {
    companion object {
        private const val REQ_SEARCH_INFO = 1
        private const val PAGE_SIZE = 20
    }

    private var mSearchList = mutableListOf<SearchResultItem>()
    private lateinit var mAdpSearch: AdpQuestionFundSearch
    private var mSearchKey = ""
    private var mPageNum = 1
    private var mType: String = AdpBaseSearch.CLASS_TYPE_SM_FUND
    private lateinit var mSEditText: ClearableEdittext


    override fun getFragLayoutId(): Int {
        return R.layout.frag_sm_head_search_item
    }

    override fun stepAllViews(root: View?, savedInstanceState: Bundle?) {
        initRefresh()
    }

    override fun parseArgment(arg: Bundle?) {
        mSearchKey = arg?.getString(ValConfig.IT_VALUE_1) ?: ""
        mType = arg?.getString(ValConfig.IT_TYPE) ?: AdpBaseSearch.CLASS_TYPE_SM_FUND

        mAdpSearch = AdpQuestionFundSearch(activity, mSearchList)
        lv_search.adapter = mAdpSearch
        initNavigateSpinner()
        if (::mSEditText.isInitialized) {
            mSEditText.setText(mSearchKey)
            mSEditText.setSelection(mSearchKey.length)
        }
    }

    /**
     * 本地导航切换
     */
    private fun initNavigateSpinner() {
        val toobar = (activity as AtyEmpty).actionBarToolBar
        if ((activity as AppCompatActivity).supportActionBar != null) {
            (activity as AppCompatActivity).supportActionBar!!.setDisplayHomeAsUpEnabled(false)
            (activity as AppCompatActivity).supportActionBar!!.setDisplayShowHomeEnabled(false)
        }
        toobar?.setBackgroundColor(ContextCompat.getColor(GlobalApp.getApp(), R.color.fd_window_bg))
        (activity as AtyEmpty).mToolbarLine.visibility = View.GONE
        val segView = View.inflate(activity, R.layout.layout_question_search_edittext, null)
        mSEditText = segView.findViewById(R.id.et_search)
        val hint = when (mType) {
            AdpBaseSearch.CLASS_TYPE_SM_MGR -> "请输入经理名称"
            AdpBaseSearch.CLASS_TYPE_SM_COM -> "请输入公司名称"
            else -> "请输入基金名称"
        }
        mSEditText.hint = hint
        mSEditText.setEditChangedListener(this)
        mSEditText.requestFocus()
        val layoutParams = Toolbar.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
        toobar?.addView(segView, layoutParams)
        segView.findViewById<View>(R.id.tv_search_cancel).setOnClickListener { activity!!.finish() }
        mSEditText.post {
            (activity?.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager)
                .showSoftInput(mSEditText, InputMethodManager.SHOW_IMPLICIT)
        }
    }

    /**
     * 初始化刷新控件
     */
    private fun initRefresh() {
        refresh_layout.setEnableLoadmoreWhenContentNotFull(false)
        refresh_layout.isEnableRefresh = false
        refresh_layout.isEnableLoadmore = true
        refresh_layout.setOnRefreshLoadmoreListener(object : OnRefreshLoadmoreListener {
            override fun onLoadmore(refreshlayout: RefreshLayout) {
                mPageNum++
                requestData()
            }

            override fun onRefresh(refreshlayout: RefreshLayout) {}
        })
    }

    private fun renderRecycleView(list: List<SearchResultItem>?, tag: String) {
        if ((list == null || list.isEmpty()) && mPageNum == 1) {
            mSearchList.clear()
        }
        if (list != null && list.isNotEmpty()) {
            if (mPageNum == 1) {
                mSearchList.clear()
                list[0].tag1 = tag
            }
            mSearchList.addAll(list)
        }
        if (mSearchList.size % PAGE_SIZE != 0 || list == null || list.isEmpty()) {
            refresh_layout.isLoadmoreFinished = true
            refresh_layout.finishLoadmore(0)
            refresh_layout.isEnableLoadmore = false
        } else {
            refresh_layout.isLoadmoreFinished = false
            refresh_layout.finishLoadmore(100)
            refresh_layout.isEnableLoadmore = true
        }
//        if (mSearchList.isEmpty()) {
//            showEmptyView("暂无内容", resources.getDrawable(R.drawable.materials_empty))
//        } else {
//            hideEmptyView()
//        }
        mAdpSearch.setKeywords(mSearchKey)
        mAdpSearch.setItems(mSearchList, true)
    }

    private fun requestData() {
        var fq = IQuerySearch.TYPE_SEARCH_SM_USEED + "," + IQuerySearch.TYPE_SEARCH_SM_STOCK + "," + IQuerySearch.TYPE_SEARCH_SM_FIXED
        when (mType) {
            AdpBaseSearch.CLASS_TYPE_SM_MGR -> fq = IQuerySearch.TYPE_SEARCH_SM_MANAGER
            AdpBaseSearch.CLASS_TYPE_SM_COM -> fq = IQuerySearch.TYPE_SEARCH_SM_COMPANY
        }
        CommonSmBuilder.requestSmQuestionSearch("", mSearchKey, fq, mPageNum.toString(), PAGE_SIZE.toString(), REQ_SEARCH_INFO, this)
    }

    override fun onReqNetFinished(result: ReqResult<ReqNetOpt>) {
        when (result.mReqOpt.handleType) {
            REQ_SEARCH_INFO ->
                if (result.isSuccess && result.mData != null) {
                    val data = result.mData as SmSearchResultData
                    val mTotalCount = data.totalCount
                    val list = arrayListOf<SearchResultItem>()
                    val count = FundTextUtils.showTextEmpty(mTotalCount)
                    val tag = when (mType) {
                        AdpBaseSearch.CLASS_TYPE_SM_MGR -> {
                            list.addAll(data.smjlContent)
                            "基金经理(${count})"
                        }
                        AdpBaseSearch.CLASS_TYPE_SM_COM -> {
                            list.addAll(data.smgsContent)
                            "基金公司(${count})"
                        }
                        else -> {
                            list.addAll(data.smjjContent)
                            "私募基金(${count})"
                        }
                    }
                    renderRecycleView(list, tag)
                } else {
                    mPageNum--
                }
        }
    }

    override fun onFocusChange(v: EditText?, hasFocus: Boolean) {
    }

    override fun onSelectionChanged(v: EditText?, selStart: Int, selEnd: Int) {
    }

    override fun onTextChanged(v: EditText?, str: CharSequence?, start: Int, before: Int, count: Int) {
        if (!::mAdpSearch.isInitialized) return
        mPageNum = 1
        mSearchKey = v?.text.toString()
        if (StrUtils.isEmpty(mSearchKey)) {
            mSearchList.clear()
            mAdpSearch.setItems(mSearchList, true)
        } else {
            requestData()
        }
    }

}