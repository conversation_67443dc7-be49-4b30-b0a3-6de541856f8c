package com.howbuy.fund.simu.fixed.zx

import android.os.Bundle
import android.view.View
import androidx.core.content.ContextCompat
import com.alibaba.android.arouter.facade.annotation.Route
import com.howbuy.android.analytics.annotation.pv.PvInfo
import com.howbuy.fund.base.frag.AbsBindingFrag
import com.howbuy.fund.simu.BR
import com.howbuy.fund.simu.R
import com.howbuy.fund.simu.databinding.FragSmPkManagerHistoryBinding
import com.howbuy.fund.base.router.fragpath.SmRouterPath
import com.howbuy.lib.utils.DensityUtils
import kotlinx.android.synthetic.main.frag_sm_pk_manager_history.refresh_layout

/**
 * @Description 固收-相关资讯-二级页面
 * <AUTHOR>
 * @Date 2023/6/13
 * @Version v824
 */
@PvInfo(pageId = "357740", level = "3", name = "固定收益更多资讯页", className = "FragSmFixedIncomeDetailsZx")
@Route(path = SmRouterPath.PATH_FRAG_SM_FIXED_DETAILS_ZX)
class FragSmFixedIncomeDetailsZx : AbsBindingFrag<FragSmPkManagerHistoryBinding, FragSmFixedIncomeDetailsZxVM>() {

    override fun getFragLayoutId() = R.layout.frag_sm_fixed_income_details_zx
    override fun getViewModelId(): Int = BR.viewModel

    override fun stepAllViews(root: View?, savedInstanceState: Bundle?) = Unit

    override fun initViewObservable() {
        mViewModel.uc.emptyLiveData.observe(this) {
            showEmptyView("暂无数据", ContextCompat.getDrawable(context!!, R.drawable.materials_empty), 1, DensityUtils.dip2px(60f))
        }
    }

    override fun initViewAndViewModel() {
        super.initViewAndViewModel()
        mViewModel.bindLifecycle(lifecycle)
        mViewModel.rc.refreshLiveData.observe(this) {
            refresh_layout.finishRefresh()
            refresh_layout.isLoadmoreFinished = false
            refresh_layout.finishLoadmore(100)
        }
        mViewModel.rc.refreshNoMoreLiveData.observe(this) {
            if (it == false) {
                refresh_layout.isLoadmoreFinished = false
                refresh_layout.finishLoadmore(100)
                refresh_layout.isEnableLoadmore = true
            } else {
                refresh_layout.isLoadmoreFinished = true
                refresh_layout.finishLoadmore(0)
                refresh_layout.isEnableLoadmore = false
            }
        }
    }
}