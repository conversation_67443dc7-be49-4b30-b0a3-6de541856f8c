package com.howbuy.fund.simu.archive.question
import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.howbuy.fund.base.config.ValConfig
import com.howbuy.fund.base.frag.AbsHbFrag
import com.howbuy.fund.base.livedata.LiveDataBus
import com.howbuy.fund.simu.utils.LiveDataEventKey
import com.howbuy.fund.net.entity.http.ReqNetOpt
import com.howbuy.fund.net.entity.http.ReqResult
import com.howbuy.fund.net.interfaces.IReqNetFinished
import com.howbuy.fund.simu.CommonSmBuilder
import com.howbuy.fund.simu.R
import com.howbuy.fund.simu.entity.QuestionFundListItem
import com.howbuy.fund.simu.entity.QuestionHot
import com.howbuy.fund.simu.entity.QuestionHotItem
import com.howbuy.fund.simu.entity.QuestionVote
import com.howbuy.hbrefresh.layout.api.RefreshLayout
import com.howbuy.hbrefresh.layout.listener.OnRefreshLoadmoreListener
import com.howbuy.lib.utils.MathUtils
import com.howbuy.lib.utils.StrUtils
import com.howbuy.userdata.business.getHboneNo
import android.view.LayoutInflater
import android.view.ViewGroup
import com.howbuy.fund.base.frag.AbsFragViewBinding
import com.howbuy.fund.simu.databinding.FragQuestionHotAllListBinding

/**
 * Create by zsm on 2018/12/21.
 * 私募问答首页- [推荐/关注/全部 tab 对应的pager list ]
 **/
class FragSmQuestionHotList : AbsFragViewBinding<FragQuestionHotAllListBinding>(), IReqNetFinished {

    override fun createViewBinding(inflater: LayoutInflater, container: ViewGroup?): FragQuestionHotAllListBinding {
        return FragQuestionHotAllListBinding.inflate(inflater, container, false)
    }

    companion object {
        private const val REQ_HOT_DATA = 1
        private const val REQ_VOTE_DATA = 2
        private const val PAGE_SIZE = 20

        private const val TAB_RECOMMEND = 0
        private const val TAB_ATTENTION = 1
        private const val TAB_ALL = 2
    }

    private var mPageNum = 1
    private var mTotalCount = 0
    private var mEmptyText: String = ""
    private lateinit var mAdapter: AdpQuestionHot
    private var mTabType = TAB_RECOMMEND
    private var mVoteQuestionId: String? = null

    override fun getFragLayoutId(): Int {
        return R.layout.frag_question_hot_all_list
    }

    override fun stepAllViews(root: View?, savedInstanceState: Bundle?) {
        binding.refreshLayout.setEnableLoadmoreWhenContentNotFull(true)
        binding.refreshLayout.isEnableRefresh = false
        binding.refreshLayout.setOnRefreshLoadmoreListener(object : OnRefreshLoadmoreListener {
            override fun onLoadmore(refreshlayout: RefreshLayout) {
                mPageNum++
                requestData(ValConfig.LOAD_LIST_PAGE)
            }

            override fun onRefresh(refreshlayout: RefreshLayout) {
            }
        })
    }

    override fun parseArgment(arg: Bundle?) {
        arg?.let {
            mTabType = it.getInt(ValConfig.IT_FROM, 0)
        }

        mAdapter = AdpQuestionHot()
        binding.rcView.adapter = mAdapter
//        if (binding.rcView.itemDecorationCount == 0) {
//            binding.rcView.addItemDecoration(SpacesItemDecoration(DensityUtils.dp2px(10f)))
//        }
        binding.rcView.layoutManager = LinearLayoutManager(activity)

        requestData(ValConfig.LOAD_LIST_REFUSH)
        requestVoteData()
        observerVoteState()
    }

    private fun requestData(loadType: Int) {
        if (ValConfig.LOAD_LIST_REFUSH == loadType) {
            mPageNum = 1
        }
        var type: String? = null
        when (mTabType) {
            0 -> type = "2"
            1 -> type = "1"
            2 -> type = "0"
        }
        CommonSmBuilder.requestSmQuestionHot(mPageNum.toString(), PAGE_SIZE.toString(), getHboneNo(), type, "", "", REQ_HOT_DATA, this)
    }

    private fun requestVoteData() {
        if (mTabType == TAB_RECOMMEND || mTabType == TAB_ALL) {
            CommonSmBuilder.reqQuestionVoteDetail(getHboneNo(), REQ_VOTE_DATA, this)
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun renderRefreshView(list: List<QuestionHotItem>?, emptyFundList: MutableList<QuestionFundListItem>?) {
        if (mPageNum == 1) {
            mAdapter.setList(list)
        } else {
            mAdapter.addData(list ?: mutableListOf())
        }
        if (list == null || list.isEmpty() || mAdapter.data.size >= mTotalCount) {
            binding.refreshLayout.isLoadmoreFinished = true
            binding.refreshLayout.finishLoadmore(0)
            binding.refreshLayout.isEnableLoadmore = false
        } else {
            binding.refreshLayout.isLoadmoreFinished = false
            binding.refreshLayout.finishLoadmore(100)
            binding.refreshLayout.isEnableLoadmore = true
        }

        if (mTabType == TAB_ATTENTION && mAdapter.data.isEmpty()) {
            binding.layEmpty.visibility = View.VISIBLE
            if (!StrUtils.isEmpty(mEmptyText)) {
                binding.tvEmpty.text = mEmptyText
            }
            if (emptyFundList != null && emptyFundList.size > 0) {
                binding.rvEmpty.visibility = View.VISIBLE
                binding.rvEmpty.layoutManager = LinearLayoutManager(activity)
                binding.rvEmpty.adapter = AdpQuestionFundListOld(emptyFundList, false)
            } else {
                binding.rvEmpty.visibility = View.GONE
            }
        } else {
            binding.layEmpty.visibility = View.GONE
        }
    }

    override fun onReqNetFinished(result: ReqResult<ReqNetOpt>) {
        if (activity == null || activity!!.isFinishing) return
        when (result.mReqOpt.handleType) {
            REQ_HOT_DATA -> {
                binding.refreshLayout.finishRefresh()
                binding.refreshLayout.isLoadmoreFinished = false
                binding.refreshLayout.finishLoadmore(100)
                if (result.isSuccess && result.mData != null) {
                    val data = result.mData as QuestionHot
                    mTotalCount = MathUtils.forValI(data.totalCount, 0)
                    mEmptyText = data.descriptionContent
                    renderRefreshView(data.dataList, data.recommendFocusList)
                } else {
                    if (mPageNum > 1) mPageNum--
                }
            }
            REQ_VOTE_DATA -> {
                if (result.isSuccess && result.mData != null) {
                    val questionVote: QuestionVote = result.mData as QuestionVote
                    mVoteQuestionId = questionVote.questionId
                    if (questionVote.voteList != null && questionVote.voteList.size > 0) {
                        mAdapter.setVoteData(questionVote)
                    }
                }
            }
        }
    }

    private fun observerVoteState() {
        LiveDataBus.get().with(LiveDataEventKey.BUS_KEY_SM_QUESTION_VOTE, String::class.java).observeBus(this, {
            it?.let {
                if (StrUtils.equals(it, mVoteQuestionId)) {
                    requestVoteData()
                }
            }
        })
    }
}