package com.howbuy.fund.simu.headline

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import com.howbuy.fund.base.storage.CommonStorageUtils
import com.howbuy.fund.base.widget.xrecyclerdivider.builder.XLinearBuilder
import com.howbuy.fund.net.util.HandleErrorMgr
import com.howbuy.fund.simu.CommonSmBuilder
import com.howbuy.fund.simu.R
import com.howbuy.fund.simu.entity.SubscribeList
import com.howbuy.fund.simu.headline.adp.AdpDialogSubscribe
import com.howbuy.lib.utils.LogUtils
import com.howbuy.userdata.business.getHboneNo
import kotlinx.android.synthetic.main.dialog_subscribe.rv
import kotlinx.android.synthetic.main.dialog_subscribe.tv_no_tips
import kotlinx.android.synthetic.main.dialog_subscribe.tv_subscribe
import kotlinx.android.synthetic.main.dialog_swift_horse.iv_close

/**
 * 订阅弹框
 */
class DialogSubscribe(
    mContext: Context,
    private val mSubscribeList: List<SubscribeList.Subscribe>
) : Dialog(mContext, R.style.dialog),
    View.OnClickListener {

    companion object {
        const val SF_SUBSCRIBE_NO_TIP = "SF_SUBSCRIBE_NO_TIP_" // 关闭提示
        const val SF_SUBSCRIBE_SHOW_TIME = "SF_SUBSCRIBE_SHOW_TIME_" // 最近显示时间，每个用户每个月(30天)仅提示一次即可
        const val SF_SUBSCRIBE_FIRST_TIME = "SF_SUBSCRIBE_FIRST_TIME_" // 用户一周(7天)内第三次进入私募头条频道，且未订阅头条任一栏目及头条周刊时，弹出订阅弹框
    }

    private val adapter by lazy {
        AdpDialogSubscribe(object : AdpDialogSubscribe.OnCheckListener {
            override fun onCheckChange() {
                onCheckedChanged()
            }
        })
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_subscribe)
        setCanceledOnTouchOutside(true)
        setCancelable(true)
        initView()
        CommonStorageUtils.putLong(SF_SUBSCRIBE_SHOW_TIME + getHboneNo(), System.currentTimeMillis())
    }

    private fun initView() {
        iv_close.setOnClickListener(this)
        tv_subscribe.setOnClickListener(this)
        tv_no_tips.setOnClickListener(this)
        mSubscribeList.forEach {
            it.select = "1"
        }
        adapter.setList(mSubscribeList)
        rv.adapter = adapter
        rv.addItemDecoration(XLinearBuilder(context).setSpacing(12f).build())
    }

    private fun onCheckedChanged() {
        val selectCodes = adapter.getSelectCodes()
        tv_subscribe.isEnabled = !TextUtils.isEmpty(selectCodes)
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.tv_subscribe -> {
                subscribe()
            }
            R.id.tv_no_tips -> {
                CommonStorageUtils.putBoolean(SF_SUBSCRIBE_NO_TIP + getHboneNo(), true)
            }
        }
        dismiss()
    }

    private fun subscribe() {
        val selectCodes = adapter.getSelectCodes()
        val hboneNo = getHboneNo()
        CommonSmBuilder.requestSubscribe(hboneNo, "1", "", "1", selectCodes, "", "", 0) { result ->
            if (result.isSuccess && result.mData != null) {
                LogUtils.pop("订阅成功")
            } else {
                val errMsg = HandleErrorMgr.handErrorMsg(result.mErr, true)
                LogUtils.pop(errMsg)
            }
        }
        CommonSmBuilder.requestSmHeadSubscribe(hboneNo, 1) { }
    }
}
