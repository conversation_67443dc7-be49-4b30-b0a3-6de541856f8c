package com.howbuy.fund.simu.archive.manager

import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.util.SparseBooleanArray
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.core.widget.NestedScrollView
import androidx.lifecycle.Observer
import com.alibaba.android.arouter.facade.annotation.Route
import com.google.android.material.appbar.AppBarLayout
import com.google.android.material.tabs.TabLayout
import com.howbuy.android.analytics.annotation.pv.PvInfo
import com.howbuy.fund.base.analytics.HbAnalytics
import com.howbuy.fund.base.aty.AtyEmpty
import com.howbuy.fund.base.config.ValConfig
import com.howbuy.fund.base.frag.AbsBindingFrag
import com.howbuy.fund.base.livedata.LiveDataBus
import com.howbuy.fund.simu.utils.LiveDataEventKey
import com.howbuy.fund.base.router.fragpath.SmRouterPath
import com.howbuy.fund.base.utils.ImgHelper
import com.howbuy.fund.base.utils.SceneIdFactory
import com.howbuy.fund.simu.BR
import com.howbuy.fund.simu.R
import com.howbuy.fund.simu.databinding.FragSmManagerDetailBinding
import com.howbuy.fund.simu.dialog.SimuCompliance
import com.howbuy.fund.simu.entity.SmMgrYdIncomeChartBody
import com.howbuy.fund.simu.utils.HMDPViewUtil
import com.howbuy.fund.simu.utils.SmChartHelper
import com.howbuy.fund.simu.utils.SmCommonTools
import com.howbuy.lib.compont.GlobalApp
import com.howbuy.lib.utils.DensityUtils
import com.howbuy.lib.utils.StrUtils
import com.howbuy.share.entity.WorkWeixinEntity
import com.howbuy.fund.base.utils.SmValConfig
import com.howbuy.lib.interfaces.IShareProvider
import com.howbuy.router.proxy.Invoker

/**
 * class description.
 * 私募基金经理档案页面
 * <AUTHOR>
 * @date 2020/11/6
 */
@Route(path = SmRouterPath.PATH_FRAG_SM_MANAGER_DETAILS)
@PvInfo(pageId = "32", level = "2", name = "经理档案页", className = "FragSmManagerDetails")
class FragSmManagerDetails : AbsBindingFrag<FragSmManagerDetailBinding, SmManagerDetailVM>(),
    View.OnClickListener {

    private lateinit var mToolbarIvHeader: ImageView
    private lateinit var mToolbarTvName: TextView

    //当前tab定位所属的模块view
    private val anchorList: MutableList<View> = mutableListOf()

    //用户点击tab时的当前tab下标
    private var mSelectedTab = 0
    private var mPositionYToScroll = -1

    //保存控制tab显示与否
    private val mShowTabArray = SparseBooleanArray()
    private var isUserClickTab = false
    //标记是否为自动定位选中的(如果是自选中定位的,不要上报click给大数据)
    private var mAutoSelectedTabMode = false

    override fun getFragLayoutId(): Int {
        return R.layout.frag_sm_manager_detail
    }

    override fun getViewModelId(): Int = BR.viewModel

    override fun stepAllViews(root: View?, savedInstanceState: Bundle?) {
        initToolbar()
        SmChartHelper.initScatterChart(mBinding.layFxsy.scatterChart)
        mBinding.layZhfxInfo.rbLeft.setOnClickListener(this)
        mBinding.layZhfxInfo.rbLeftSelect.setOnClickListener(this)
        mBinding.layZhfxInfo.rbRight.setOnClickListener(this)
        mBinding.layZhfxInfo.rbRightSelect.setOnClickListener(this)
        mBinding.layZhfxInfo.vpIncomeChart.isCanHScroll = false
        SimuCompliance(this).showDirectly()
        tabSection()
        showFullScreenLoadingView()
    }

    override fun initViewAndViewModel() {
        super.initViewAndViewModel()
        mViewModel.pageId = "32"
        mViewModel.sceneId = SceneIdFactory.createSceneId(this)
        //监听页面各模块数据变更,刷新UI操作
        mViewModel.mJjbxVisibleListener = object :SmManagerDetailVM.IJjbxVisible{
            override fun onVisible() {
                //基金表现模块及tab是否显示
                if (!mShowTabArray.get(1)){
                    mShowTabArray.put(1, true)
                    findAllAnchorViews()
                }
            }

        }
    }

    private fun initToolbar() {
        (activity as AtyEmpty).actionBarToolBar?.setContentInsetsAbsolute(0, 0)
        (activity as AtyEmpty).setTitleVisible(false)
        val viewGroup = (activity as AtyEmpty).toolbarLayoutTitle
        val titleView = View.inflate(activity, R.layout.frag_sm_manager_details_title_layout, null)
        mToolbarIvHeader = titleView.findViewById(R.id.toolbar_iv_mgr_header)
        mToolbarTvName = titleView.findViewById(R.id.toolbar_tv_mgr_name)
        mToolbarTvName.text = "基金经理"
        viewGroup?.addView(titleView)
    }

    override fun initViewObservable() {
        mViewModel.mBasicInfoLiveData.observe(this, {
            //同一个页面中如果有两个viewpager,需要使用不同的FragmentManager对象
            val manager = (activity as AppCompatActivity?)?.supportFragmentManager
            manager?.let { it1 ->
                mViewModel.setBasicInfoModuleUIData(it, childFragmentManager, it1, mViewModel.pageId)
            }
            //设置Toolbar上的经理头像和姓名
            ImgHelper.display(it?.jltx, mToolbarIvHeader, mViewModel.mSmMgrBasicInfo?.options)
            mShowTabArray.put(
                2, (!TextUtils.isEmpty(it?.summary) || it?.cyjlList?.isNotEmpty() == true)
                        && it != null
            )
            findAllAnchorViews()
            mViewModel.mLoadingReqCount--
            dismissFullLoading()
        })
        mViewModel.mUIOptObservable.observe(this, Observer {
            val item = it as Pair<*, *>
            val first = item.first
            if (first == 1) {
                //好买点评
                activity ?: return@Observer
                val value = item.second as? String
                HMDPViewUtil().showHmdp(activity, value, R.drawable.sm_txt_hmdp)
            } else if (first == 2) {
                //雷达图数据UI渲染
                val value = item.second as? Array<*>
                mBinding.layZhfxInfo.radarView.setData(
                    value?.get(0) as? FloatArray,
                    value?.get(1) as? FloatArray
                )
            } else if (first == 3) {
                //经理月度回报模块,显示pop
                activity ?: return@Observer
                val value = item.second as? MutableList<SmMgrYdIncomeChartBody.MonthItem>
                //通过获取子模块中的LiveData字段刷新选中的数据
                val location = IntArray(2)
                mBinding.laySmMgrYdChart.laySmMgrYdincomeTitle.getLocationOnScreen(location) //getLocationInWindow 还没细究两者之前的区别
                showMonthPop(
                    activity!!,
                    mBinding.laySmMgrYdChart.tvSmMgrYdincomeSelectDate,
                    value,
                    mBinding.laySmMgrYdChart.tvSmMgrYdincomeSelectDate.width,
                    location[1],
                    mViewModel.mSmMgrYdIncome
                )
            } else if (first == 4) {
                //手指在代表基金走势图上
                val value = item.second as? Boolean
                setScrollEnable(value == true)
            } else if (first == 5) {
                //点击存续基金只数, 定位到存续模块
                scrollToMgrFundFxModule()
            } else if (first == 6) {
                //经理档案模块tab添加
                val value = item.second as? Boolean
                mShowTabArray.put(3, value == true)
                findAllAnchorViews()
            }
        })

        //监听综合分析tab是否显示
        LiveDataBus.get().with(LiveDataEventKey.BUS_KEY_SM_MGR_INCOME_CHART_VISIBLE, Pair::class.java).observeBus(this, {
            val item = it as Pair<*, *>
            val first: String? = item.first as String?
            if (TextUtils.equals(first, mViewModel.mManagerCode)) {
                val nhsyl = item.second as String?
                if (TextUtils.isEmpty(nhsyl)) {
                    mViewModel.mSmMgrZhfxModule?.mIncomeChartVisiable?.set(false)
                } else {
                    mViewModel.mSmMgrZhfxModule?.mIncomeChartVisiable?.set(true)
                }
            }

            if (TextUtils.isEmpty(mViewModel.mSmMgrZhfxModule?.mScore?.get()) && mViewModel.mSmMgrZhfxModule?.mIncomeChartVisiable?.get() == false) {
                mShowTabArray.put(0, false)
            } else {
                mShowTabArray.put(0, true)
            }
            findAllAnchorViews()
        })
    }

    private fun dismissFullLoading() {
        if (mViewModel.mLoadingReqCount <= 0) {
            hideLoadingView()
            mViewModel.onPageLoadFinish()
        }
    }

    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        super.onCreateOptionsMenu(menu, inflater)
        inflater.inflate(R.menu.menu_sm_manager_details, menu)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (!TextUtils.isEmpty(mViewModel.shareLink)) {
            val mShareTitle: String =
                mViewModel.shareCompanyNane + ":" + mViewModel.mManagerName.get()
            val mShareUrl: String? = mViewModel.shareLink
            var bmp: Any? = null
            if (!StrUtils.isEmpty(mViewModel.shareIcon)) {
                bmp = mViewModel.shareIcon
            }
            val shareEntity = WorkWeixinEntity(mShareTitle, mViewModel.shareContent, mShareUrl, bmp)
            Invoker.getInstance().navigation(IShareProvider::class.java).showShareDialog(
                activity,
                shareEntity,
                null,
                null)
            HbAnalytics.onClick(GlobalApp.getApp(), SmValConfig.CLICK_SHARE)
        }
        return super.onOptionsItemSelected(item)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == 1 && data != null) {
            val typeLineFill = data.getIntExtra(ValConfig.IT_TYPE, 0)
            mViewModel.mSmMgrDbjjChart?.reDrawPortChart(typeLineFill)
        }
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.rb_left, R.id.rb_left_select -> {
                setIncomeChartUI(0)
                HbAnalytics.onClick(activity, "86530")
            }
            R.id.rb_right, R.id.rb_right_select -> {
                setIncomeChartUI(1)
                HbAnalytics.onClick(activity, "86540")
            }
        }
    }

    private fun setIncomeChartUI(type: Int) {
        if (type == 0) {
            mBinding.layZhfxInfo.vpIncomeChart.currentItem = 0
            mBinding.layZhfxInfo.rbLeftSelect.visibility = View.VISIBLE
            mBinding.layZhfxInfo.rbLeft.visibility = View.GONE
            mBinding.layZhfxInfo.rbRight.visibility = View.VISIBLE
            mBinding.layZhfxInfo.rbRightSelect.visibility = View.GONE
        } else {
            mBinding.layZhfxInfo.vpIncomeChart.currentItem = 1
            mBinding.layZhfxInfo.rbLeftSelect.visibility = View.GONE
            mBinding.layZhfxInfo.rbLeft.visibility = View.VISIBLE
            mBinding.layZhfxInfo.rbRight.visibility = View.GONE
            mBinding.layZhfxInfo.rbRightSelect.visibility = View.VISIBLE
        }
    }

    private fun setScrollEnable(enable: Boolean) {
        mBinding.vcsSmMgr.isCanHScroll = enable
    }

    /**
     * 滚动到旗下基金分析模块
     */
    private fun scrollToMgrFundFxModule() {
        mBinding.layAppbar.setExpanded(false)
        mBinding.vcsSmMgr.post {
            //scrollview 滚动到旗下基金表现下的存续基金模块顶部
            mBinding.vcsSmMgr.smoothScrollTo(
                0,
                mBinding.layQixianjjfxTitle.y.toInt() + mBinding.layCxjj.root.top
            )
        }
    }

    override fun getAlysContentId(): String {
        return mViewModel.mManagerCode ?: ""
    }

    override fun getFundCode(): String {
        return alysContentId
    }

    private fun tabSection() {
        //滑动时,toolbar title 变化
        mBinding.layAppbar.addOnOffsetChangedListener(AppBarLayout.OnOffsetChangedListener { _, verticalOffset ->
            if (Math.abs(verticalOffset) > DensityUtils.dp2px(90f)) {
                mToolbarIvHeader.visibility = View.VISIBLE
                mToolbarTvName.text = mViewModel.mSmMgrBasicInfo?.mJlName?.get()
            } else {
                mToolbarIvHeader.visibility = View.GONE
                mToolbarTvName.text = "基金经理"
            }
        })

        //滑动scrollview, 定位tab
        mBinding.vcsSmMgr.setOnScrollChangeListener(NestedScrollView.OnScrollChangeListener { _, _, _, _, _ ->
            try {
                //为了控制,点击tab定位tab问题,定位完成后,需要重置mSelectedTab
                if (anchorList.size > 0) {
                    val i = SmCommonTools.getSelectItemPosition(
                        mBinding.tabLayout,
                        mBinding.vcsSmMgr, anchorList, 10f
                    )
                    if (isUserClickTab) {
                        mBinding.tabLayout.setScrollPosition(0, mSelectedTab.toFloat(), true)
                        isUserClickTab = false
                    } else {
                        mBinding.tabLayout.setScrollPosition(0, i.toFloat(), true)
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
            //滚动视图,刷新取消页面上图表上的用户选中高亮操作
            LiveDataBus.get().with(LiveDataEventKey.BUS_KEY_SM_MGR_AND_COMP_VERTICAL_SCROLL)
                .postValue(true)
            mViewModel.mSmMgrDbjjChart?.cleanHightLine()
        })

        //点击tab,定位模块
        mBinding.tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                tab ?: return
                scrollByClickTab(tab, false)
                //由于tab不是固定的,所以通过tab的文案来判断当前是哪个tab
                if (!mAutoSelectedTabMode){
                    var clickId = ""
                    if (TextUtils.equals("综合分析", tab.text)) {
                        //每次进入经理档案页面,默认定位该tab,默认的这次定位不上报click
                        clickId = "337000"
                    } else if (TextUtils.equals("基金表现", tab.text)) {
                        clickId = "337010"
                    } else if (TextUtils.equals("经理档案", tab.text)) {
                        clickId = "337020"
                    } else if (TextUtils.equals("演说轨迹", tab.text)) {
                        clickId = "337030"
                    }
                    HbAnalytics.onClick(activity, clickId)
                }
                mAutoSelectedTabMode = false
            }


            override fun onTabUnselected(tab: TabLayout.Tab?) {
            }

            override fun onTabReselected(tab: TabLayout.Tab?) {
                tab ?: return
                scrollByClickTab(tab, true)
            }

            private fun scrollByClickTab(tab: TabLayout.Tab, reClick:Boolean) {
                isUserClickTab = true
                val positionSelect = tab.position
                if (positionSelect >= 0 && positionSelect < anchorList.size) {
                    mPositionYToScroll = anchorList[positionSelect].top
                    mSelectedTab = positionSelect
                    if (positionSelect != 0 || reClick) {
                        mBinding.layAppbar.setExpanded(false)
                    }
                    //调用scrollTo时,滚动监听只会接收到一次, 如果用smooth则会回调多次
                    mBinding.vcsSmMgr.scrollTo(0, mPositionYToScroll)
                }
            }
        })
        mBinding.layAppbar.addOnOffsetChangedListener(AppBarLayout.OnOffsetChangedListener { appBarLayout, verticalOffset ->
            if (Math.abs(verticalOffset) > appBarLayout.totalScrollRange - 10) {
                //收起状态
                mBinding.tabLayout.setBackgroundColor(
                    ContextCompat.getColor(
                        GlobalApp.getApp(),
                        R.color.white
                    )
                )
            } else {
                //展开状态
                mBinding.tabLayout.setBackgroundColor(
                    ContextCompat.getColor(
                        GlobalApp.getApp(),
                        R.color.fd_window_bg
                    )
                )
            }
        })
    }

    /**
     * 添加tab
     */
    private fun findAllAnchorViews() {
        anchorList.clear()
        mBinding.tabLayout.removeAllTabs()
        if (mShowTabArray.get(0)) {
            mBinding.tabLayout.addTab(mBinding.tabLayout.newTab().setText("综合分析"), false)
            anchorList.add(mBinding.layZhfxInfo.root)
        }
        if (mShowTabArray.get(1)) {
            mBinding.tabLayout.addTab(mBinding.tabLayout.newTab().setText("基金表现"), false)
            anchorList.add(mBinding.layQixianjjfxTitle)
        }
        if (mShowTabArray.get(2)) {
            mBinding.tabLayout.addTab(mBinding.tabLayout.newTab().setText("经理档案"), false)
            anchorList.add(mBinding.layManagerTitle)
        }
        if (mShowTabArray.get(3)) {
            mBinding.tabLayout.addTab(mBinding.tabLayout.newTab().setText("演说轨迹"), false)
            anchorList.add(mBinding.laySmMgrVideoList.laySmMgrAndCompLyrl)
        }
        mAutoSelectedTabMode = true
        mBinding.tabLayout.getTabAt(0)?.select()
    }

}