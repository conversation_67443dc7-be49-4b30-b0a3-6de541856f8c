package com.howbuy.fund.simu.combination.pk

import android.graphics.Paint
import android.os.Bundle
import android.view.View
import androidx.core.content.ContextCompat
import androidx.lifecycle.Observer
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.github.mikephil.charting.formatter.ValueFormatter
import com.github.mikephil.charting.interfaces.datasets.ILineDataSet
import com.github.mikephil.charting.utils.Utils
import com.howbuy.fund.base.config.ValConfig
import com.howbuy.fund.base.livedata.LiveDataBus
import com.howbuy.fund.base.utils.CollectionUtil
import com.howbuy.fund.simu.utils.LiveDataEventKey
import com.howbuy.fund.base.utils.FundUtils
import com.howbuy.fund.chart.mpchart.DefaultPercentValueFormat
import com.howbuy.fund.chart.mpchart.HbChartAttrHelper
import com.howbuy.fund.chart.mpchart.line.IHbFundLineChartListener
import com.howbuy.fund.simu.R
import com.howbuy.fund.simu.combination.FragCombineChartBase
import com.howbuy.fund.simu.entity.CombineChartData
import com.howbuy.fund.simu.utils.SmChartHelper
import com.howbuy.fund.simu.utils.SmChartHelper.formatMinMaxValue
import com.howbuy.lib.compont.GlobalApp
import com.howbuy.lib.utils.DateUtils
import com.howbuy.lib.utils.LogUtils
import com.howbuy.lib.utils.MathUtils
import com.howbuy.lib.utils.StrUtils
import kotlinx.coroutines.*
import android.view.LayoutInflater
import android.view.ViewGroup
import com.howbuy.fund.simu.databinding.FragCombinePkIncomeChartBinding

class FragCombinePKIncomeChart : FragCombineChartBase<FragCombinePkIncomeChartBinding>() {

    private lateinit var mXAxis: XAxis
    private lateinit var mYAxis: YAxis

    /**
     * lateinit var 使用的时候,需要判断是否初始化了,否则容易引起异常
     *
     * catch exception:kotlin.UninitializedPropertyAccessException: lateinit property mFundEntryList has not been initialized
     */
    private var dateIndexs: MutableList<String> = mutableListOf()//X 轴日期集合
    private var mFundEntryList: MutableList<Entry> = mutableListOf()//本基金绘图数据
    private var mFundEntryList2: MutableList<Entry> = mutableListOf()//2绘图数据
    private var mOtherEntryList: MutableList<Entry> = mutableListOf()//指数绘图数据
    private var mDataList: List<CombineChartData.Item> = mutableListOf()
    private var mMergeJob: Job? = null
    private var hasFirst = true
    private var hasSecond = true

    override fun getFragLayoutId(): Int {
        return R.layout.frag_combine_pk_income_chart
    }

    override fun createViewBinding(inflater: LayoutInflater, container: ViewGroup?): FragCombinePkIncomeChartBinding {
        return FragCombinePkIncomeChartBinding.inflate(inflater,container,false)
    }

    override fun stepAllViews(root: View?, savedInstanceState: Bundle?) {
        initLineChart()

        LiveDataBus.get().with(LiveDataEventKey.BUS_KEY_SM_PK_CHART_INCOME_UI, Int::class.java).observeBus(this, {
            if (it != null) {
                (parentFragment as FragCombinePKChartContainer).resetIncomeTitleUI()
                drawChart(false, it)
            }
        })
    }

    override fun parseArgment(arg: Bundle?) {
        LiveDataBus.get().with(LiveDataEventKey.BUS_KEY_SIMU_COMBINE_CLEAR_HIGHLIGHT, Boolean::class.java)
            .observeBus(this, {
                binding.hblineChart.cleanHighlight()
            })
    }

    override fun fetchData() {
        showLoadingView()
    }

    fun setData(t: CombineChartData?, t2: CombineChartData?) {
        hideLoadingView()
        if (t != null || t2 != null) {
            if (t?.dataList != null && t.dataList.size > 0 || t2?.dataList != null && t2.dataList.size > 0) {
                hasFirst = true
                hasSecond = true
                mMergeJob = CoroutineScope(Job() + Dispatchers.Main).launch {
                    val data = withContext(Dispatchers.IO) {
                        var mergeList = mutableListOf<CombineChartData.Item>()
                        if (t?.dataList == null || t.dataList.size == 0) {
                            hasFirst = false
                            for (item in t2?.dataList!!) {
                                mergeList.add(CombineChartData.Item(item.hbdate, null, item.zhData, item.bjjzData, null))
                            }
                        } else if (t2?.dataList == null || t2.dataList.size == 0) {
                            hasSecond = false
                            mergeList = t.dataList
                        } else {
                            var tagIndex = 0
                            for (index in t.dataList.indices) {
                                val item1 = t.dataList[index]
                                val date1 = DateUtils.getTimeFormatLong(item1.hbdate, DateUtils.DATEF_YMD_)
                                val date1Next = if (index == t.dataList.size - 1) {
                                    Long.MAX_VALUE
                                } else {
                                    DateUtils.getTimeFormatLong(t.dataList[index + 1].hbdate, DateUtils.DATEF_YMD_)
                                }
                                for (index2 in tagIndex until t2.dataList.size) {
                                    val item2 = t2.dataList[index2]
                                    val date2 = DateUtils.getTimeFormatLong(item2.hbdate, DateUtils.DATEF_YMD_)
                                    if (date1 == date2) {
                                        mergeList.add(CombineChartData.Item(item2.hbdate, item1.zhData, item2.zhData, item2.bjjzData, null))
                                        tagIndex = index2 + 1
                                        break
                                    } else if (date2 < date1) {
                                        mergeList.add(CombineChartData.Item(item2.hbdate, null, item2.zhData, item2.bjjzData, null))
                                        tagIndex = index2 + 1
                                    } else {
                                        if (!mergeList.contains(item1)) {
                                            mergeList.add(item1)
                                            tagIndex = index2
                                        }
                                        if (date2 in (date1 + 1) until date1Next) {
                                            mergeList.add(CombineChartData.Item(item2.hbdate, null, item2.zhData, item2.bjjzData, null))
                                            tagIndex = index2 + 1
                                        } else {
                                            break
                                        }
                                    }
                                }
                            }
                        }
                        mergeList
                    }
                    setLineChartData(data)
                }
            } else {
                showEmptyChart()
            }
        } else {
            showEmptyChart()
        }
    }

    private fun initLineChart() {
        mXAxis = binding.hblineChart.xAxis
        mYAxis = binding.hblineChart.axisLeft

        HbChartAttrHelper.setHbLineChartBasicAttr(binding.hblineChart)
        binding.hblineChart.isHighLightLineDash = false
        binding.hblineChart.isNeedHighLightDot = true
        binding.hblineChart.highLightDotColor = SmChartHelper.SM_LINE_COLOR[0]
        binding.hblineChart.highLightDotRadius = 7f
        binding.hblineChart.highLightHoleDotColor = -0x1
        binding.hblineChart.highLightHoleDotRadius = 8f
        binding.hblineChart.axisRight.setDrawZeroLine(false)
        binding.hblineChart.axisRight.setDrawAxisLine(false)
        binding.hblineChart.axisRight.setDrawGridLines(false)
        binding.hblineChart.axisRight.isEnabled = false

        mYAxis.gridLineWidth = 0.5f
        mYAxis.setDrawGridLines(true)
        mYAxis.gridColor = -0x151516
        mYAxis.yOffset = 0f

        mXAxis.setLabelCount(2, true)
        mXAxis.textColor = -0x444235
        mXAxis.setDrawGridLines(false)
        mXAxis.valueFormatter = object : ValueFormatter() {
            override fun getFormattedValue(value: Float): String {
                return if (value == 0f) {
                    dateIndexs[0]
                } else if (value == (dateIndexs.size - 1).toFloat()) {
                    dateIndexs[dateIndexs.size - 1]
                } else {
                    ""
                }
            }
        }

        binding.hblineChart.isHighLightLineDash = false
        binding.hblineChart.setTouchImmediate(true)
        binding.hblineChart.setCustomFingerTouchEnable(true, false)
        binding.hblineChart.fingerTouchListener = object : IHbFundLineChartListener<String> {
            override fun onChartViewFocus(focus: Boolean) {
//                setScrollEnable(!focus)
            }

            override fun onFitSingleTouchIndex(index: Int, xPos: Int) {
                LogUtils.d("FragBrokerBaseChart", index.toString())
//                setScrollEnable(false)
                onChartClick(xPos, true)
            }

            override fun onFitDoubleTouchIndexs(preIndex: Int, sufIndex: Int) {
            }
        }

    }

    protected fun showEmptyChart() {
        val p = Paint(1)
        p.color = -0x111112
        p.textAlign = Paint.Align.CENTER
        p.textSize = Utils.convertDpToPixel(60f)
        binding.hblineChart.setPaint(p, 7)
        binding.hblineChart.setNoDataText("暂无数据")
        binding.hblineChart.invalidate()
    }

    /**
     * 图表点击时，时间旗帜及头部变化
     */
    private fun onChartClick(xPos: Int, isClick: Boolean) {
        renderTitle(xPos, isClick)
    }

    private fun renderTitle(xPos: Int, isClick: Boolean) {
        if (isClick) {
            val date = dateIndexs.get(xPos)
            var firstLineValue = ""
            if (hasFirst) {
                try {
                    firstLineValue = (MathUtils.forValF(mDataList[xPos].zhData, -99f) * 100).toString()
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
            var secondLineValue = ""
            if (!CollectionUtil.isEmpty(mOtherEntryList)) {
                try {
                    secondLineValue = (MathUtils.forValF(mDataList[xPos].bjjzData, -99f) * 100).toString()
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
            var thirdLineValue = ""
            if (hasSecond) {
                try {
                    thirdLineValue = (MathUtils.forValF(mDataList[xPos].zhData2, -99f) * 100).toString()
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
            mContainerFragment?.setTitleText(firstLineValue, thirdLineValue, secondLineValue, date, mOtherEntryList.isEmpty(), true)
        } else {
            binding.hblineChart.cleanHighlight()
            mContainerFragment?.setTitleText(null, null, null, null, false, true)
        }
    }

    override fun onDestroy() {
        if (binding.hblineChart != null) {
            binding.hblineChart.clear()
        }
        mMergeJob?.cancel()
        super.onDestroy()
    }


    private fun setLineChartData(list: List<CombineChartData.Item>) {
        mDataList = list
        renderTitle(0, false)

        mFundEntryList.clear()
        mFundEntryList2.clear()
        mOtherEntryList.clear()
        dateIndexs.clear()
        var min = 0f
        var max = 0f
        for ((i: Int, item: CombineChartData.Item) in list.withIndex()) {
            dateIndexs.add(item.hbdate ?: "")
            val f: Float = (item.zhData ?: "0").toFloat() * 100
            if (!StrUtils.isEmpty(item.zhData)) {
                mFundEntryList.add(Entry(i.toFloat(), f, item))
            }
            val f2: Float = (item.zhData2 ?: "0").toFloat() * 100
            if (!StrUtils.isEmpty(item.zhData2)) {
                mFundEntryList2.add(Entry(i.toFloat(), f2, item))
            }
            var f1 = 0.0f
            if (!StrUtils.isEmpty(item.bjjzData)) {
                f1 = item.bjjzData!!.toFloat() * 100
                mOtherEntryList.add(Entry(i.toFloat(), f1, item))
            }
            if (min > f) min = f
            if (min > f2) min = f2
            if (min > f1) min = f1
            if (max < f) max = f
            if (max < f2) max = f2
            if (max < f1) max = f1
        }

        setYAlaisFormat(min, max)
        drawChart(false, FragSmCombinePKDetail.mClickIncomeChartLineType)
    }

    /**
     * 调整Y轴上显示的坐标值
     */
    private fun setYAlaisFormat(min: Float, max: Float) {
        /**
         * 走势图纵坐标轴调整:
         * 最高收益*（1+(-)10%），最低收益*（1-10%）的结果，个位数位上就近取5倍数的整数
         * （如，最高值*（1+10%）=6%，那上限则直接取为10%；
         * 最低值*（1+(-)10%）=-7%,那下线取值为-10%）以此为纵坐标轴的上线和下线值，
         * 中间纵坐标轴刻度线五等分(相当于有6个刻度)；
         */
        val yAisleMax = formatMinMaxValue(max, true, 10)
        mYAxis.axisMaximum = yAisleMax

        val yAisleMin = formatMinMaxValue(min, false, 10)
        mYAxis.axisMinimum = yAisleMin

        mYAxis.setLabelCount(6, true)
        mYAxis.valueFormatter = DefaultPercentValueFormat(0, true)
    }

    fun drawChart(drawFill: Boolean, type: Int) {
        if (!isAdded) return

        val sets = java.util.ArrayList<ILineDataSet>()

        val fundSet = LineDataSet(mFundEntryList, "")
        fundSet.mode = LineDataSet.Mode.LINEAR
        fundSet.axisDependency = YAxis.AxisDependency.LEFT
        fundSet.lineWidth = 1f
        fundSet.color = ContextCompat.getColor(GlobalApp.getApp(), R.color.fd_f92d2d)
        fundSet.isHighlightEnabled = true
        fundSet.highlightLineWidth = 1f
        fundSet.highLightColor = ContextCompat.getColor(GlobalApp.getApp(), R.color.fd_f92d2d)
        fundSet.setDrawVerticalHighlightIndicator(true)
        fundSet.setDrawHorizontalHighlightIndicator(false)
        fundSet.setDrawValues(false)
        fundSet.setDrawCircles(false)

        if (type == 1 && FragSmCombinePKDetail.mClickIncomeChartType1) {
            fundSet.lineWidth = 1.3f
            fundSet.setDrawFilled(true)
            fundSet.fillDrawable = ContextCompat.getDrawable(GlobalApp.getApp(), R.drawable.bg_sm_chart_fill_red)
            fundSet.setFillFormatter { _, dataProvider -> dataProvider.yChartMin }
        } else {
            fundSet.setDrawFilled(false)
        }

        fundSet.circleRadius = 0f
        if (type == 1) {
            sets.add(fundSet)
        } else {
            sets.add(0, fundSet)
        }

        val otherSet = LineDataSet(mOtherEntryList, "")
        otherSet.mode = LineDataSet.Mode.LINEAR
        otherSet.axisDependency = YAxis.AxisDependency.LEFT
        otherSet.lineWidth = 1f
        otherSet.color = ContextCompat.getColor(GlobalApp.getApp(), R.color.fd_9e9e9e)

        otherSet.isHighlightEnabled = true
        otherSet.highlightLineWidth = 1f
        otherSet.highLightColor = ContextCompat.getColor(GlobalApp.getApp(), R.color.fd_f92d2d)
        otherSet.setDrawVerticalHighlightIndicator(true)
        otherSet.setDrawHorizontalHighlightIndicator(false)
        otherSet.setDrawValues(false)
        otherSet.setDrawCircles(false)
        if (type == 3 && FragSmCombinePKDetail.mClickIncomeChartType3) {
            otherSet.lineWidth = 1.3f
            otherSet.setDrawFilled(true)
            otherSet.fillDrawable = ContextCompat.getDrawable(GlobalApp.getApp(), R.drawable.bg_sm_chart_fill_gray)
            otherSet.setFillFormatter { _, dataProvider -> dataProvider.yChartMin }
        } else {
            otherSet.setDrawFilled(false)
        }

        otherSet.circleRadius = 0f
        if (type == 3) {
            sets.add(otherSet)
        } else {
            sets.add(0, otherSet)
        }

        val fundSet2 = LineDataSet(mFundEntryList2, "")
        fundSet2.mode = LineDataSet.Mode.LINEAR
        fundSet2.axisDependency = YAxis.AxisDependency.LEFT
        fundSet2.lineWidth = 1f
        fundSet2.color = ContextCompat.getColor(GlobalApp.getApp(), R.color.fd_4c77aa)
        fundSet2.isHighlightEnabled = true
        fundSet2.highlightLineWidth = 1f
        fundSet2.highLightColor = ContextCompat.getColor(GlobalApp.getApp(), R.color.fd_f92d2d)
        fundSet2.setDrawVerticalHighlightIndicator(true)
        fundSet2.setDrawHorizontalHighlightIndicator(false)
        fundSet2.setDrawValues(false)
        fundSet2.setDrawCircles(false)
        if (type == 2 && FragSmCombinePKDetail.mClickIncomeChartType2) {
            fundSet2.lineWidth = 1.3f
            fundSet2.setDrawFilled(true)
            fundSet2.fillDrawable = ContextCompat.getDrawable(GlobalApp.getApp(), R.drawable.bg_sm_chart_fill_blue)
            fundSet2.setFillFormatter { _, dataProvider -> dataProvider.yChartMin }
        } else {
            fundSet2.setDrawFilled(false)
        }
        fundSet2.circleRadius = 0f

        if (type == 2) {
            sets.add(fundSet2)
        } else {
            sets.add(0, fundSet2)
        }

        binding.hblineChart.data = LineData(sets)
        binding.hblineChart.invalidate()
    }

    fun getStartTime(): String {
        return dateIndexs[0]
    }

}