package com.howbuy.fund.simu.archive.manager.pk.gd

import android.content.Context
import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import com.alibaba.android.arouter.facade.annotation.Route
import com.howbuy.fund.base.config.ValConfig
import com.howbuy.fund.base.frag.AbsBindingFrag
import com.howbuy.fund.base.router.fragpath.SmRouterPath
import com.howbuy.fund.base.utils.FundUtils
import com.howbuy.fund.simu.BR
import com.howbuy.fund.simu.R
import com.howbuy.fund.simu.databinding.FragManagerPkQbgdBinding
import com.howbuy.lib.adp.AbsFragPageAdp
import com.howbuy.android.analytics.annotation.pv.PvInfo
import com.howbuy.fund.base.utils.CollectionUtil
import com.howbuy.fund.base.utils.FragmentUtils

/**
 * 全部观点
 **/
@Route(path = SmRouterPath.PATH_FRAG_MANAGER_PK_GD)
@PvInfo(pageId = "339030", level = "3", name = "经理对比-全部经理观点页", className = "FragManagerPkQbgd")
class FragManagerPkQbgd : AbsBindingFrag<FragManagerPkQbgdBinding, ManagerPkGdVM>() {
    override fun getFragLayoutId(): Int = R.layout.frag_manager_pk_qbgd

    override fun getViewModelId(): Int = BR.viewModel

    override fun stepAllViews(root: View?, savedInstanceState: Bundle?) {
    }

    override fun initViewObservable() {
        mBinding.viewPager.adapter = AdpVp(activity, this.childFragmentManager)
    }

    inner class AdpVp(val context: Context?, fm: FragmentManager) : AbsFragPageAdp(fm) {
        override fun getItem(position: Int): Fragment {
            val b = Bundle()
            if (!CollectionUtil.isEmpty(mViewModel.codeList)) {
                b.putString(ValConfig.IT_ID, mViewModel.codeList?.get(position))
            }
            return FragmentUtils.instantiate(activity!!, mFragmentManager, FragManagerPkGdList::class.java.name, b)
        }

        override fun getTag(position: Int): String {
            return "FragManagerPkGdList${position}"
        }

        override fun getCount(): Int {
            return when {
                mViewModel.hasManager3.get() == true -> {
                    3
                }
                mViewModel.hasManager2.get() == true -> {
                    2
                }
                else -> {
                    1
                }
            }
        }
    }

}