package com.howbuy.fund.simu.archive.manager.pk.fxsy

import android.annotation.SuppressLint
import android.graphics.Color
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.view.ViewTreeObserver.OnGlobalLayoutListener
import android.widget.FrameLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.alibaba.android.arouter.facade.annotation.Route
import com.github.mikephil.charting.components.LimitLine
import com.github.mikephil.charting.components.YAxis
import com.howbuy.android.analytics.annotation.pv.PvInfo
import com.howbuy.fund.base.aty.AtyEmpty
import com.howbuy.fund.base.config.SpConfig
import com.howbuy.fund.base.frag.AbsBindingFrag
import com.howbuy.fund.base.livedata.LiveDataBus
import com.howbuy.fund.simu.utils.LiveDataEventKey
import com.howbuy.fund.base.router.fragpath.SmRouterPath
import com.howbuy.fund.base.storage.CommonStorageUtils
import com.howbuy.fund.base.utils.FundUtils
import com.howbuy.fund.simu.BR
import com.howbuy.fund.simu.R
import com.howbuy.fund.simu.SmOptHelper
import com.howbuy.fund.simu.databinding.FragManagerPkFxsyLandBinding
import com.howbuy.fund.simu.entity.SmPkManagerFxsy
import com.howbuy.fund.simu.utils.SmChartHelper
import com.howbuy.fund.simu.widget.GuideView
import com.howbuy.lib.compont.GlobalApp
import com.howbuy.lib.utils.ColorUtils
import com.howbuy.lib.utils.DensityUtils
import com.howbuy.lib.utils.MathUtils
import com.howbuy.userdata.business.getHboneNo
import kotlinx.android.synthetic.main.frag_manager_pk_fxsy_land.*

/**
 * 经理PK风险收益横屏图
 **/
@Route(path = SmRouterPath.PATH_FRAG_MANAGER_PK_FXSY_LAND)
@PvInfo(pageId = "332880", level = "3", name = "经理对比风险收益分布图全屏页", className = "FragManagerPkFxsyLand")
@SuppressLint("SetTextI18n")
class FragManagerPkFxsyLand : AbsBindingFrag<FragManagerPkFxsyLandBinding, ManagerPkFxsyVMLand>() {

    override fun getFragLayoutId(): Int = R.layout.frag_manager_pk_fxsy_land

    override fun getViewModelId(): Int = BR.viewModel

    override fun stepAllViews(root: View?, savedInstanceState: Bundle?) {
        (activity as? AtyEmpty)?.actionBarToolBar?.visibility = View.GONE
        SmChartHelper.initScatterChart(scatter_chart)
    }

    override fun initViewObservable() {
        if (mViewModel.pkCount == 3) {
            mViewModel.mUIOptLiveData.observe(this, {
                dealTips(lay_recycler3)
            })
        } else {
            dealTips(lay_recycler)
        }
        LiveDataBus.get().with(LiveDataEventKey.BUS_KEY_SM_MANAGER_PK_RISK_CLICK)
            .observeBus(this@FragManagerPkFxsyLand, {
                it?.let {
                    if (it is SmPkManagerFxsy.PkItem.FundItem) {
                        showFloatView(true, it)
                        mViewModel.notifyClickItemStatus(it)
                    } else {
                        mViewModel.notifyClickItemStatus(null)
                    }
                } ?: let {
                    showFloatView(false, null)
                    mViewModel.notifyClickItemStatus(null)
                }
            })
    }


    private fun showFloatView(showMarkView: Boolean, item: SmPkManagerFxsy.PkItem.FundItem?) {
        //利用 limt x,y来确认当前点的位置,然后实现markview
        if (showMarkView) {
            lay_float_view.postDelayed({
                //X轴线
                val limitLineX = LimitLine(MathUtils.forValF(item?.nhbdl, 0f), "")
                limitLineX.lineColor = Color.parseColor("#00ffffff")
                scatter_chart.xAxis.addLimitLine(limitLineX)
                //y轴线
                val limitLineY = LimitLine(MathUtils.forValF(item?.nhsyl, 0f), "")
                limitLineY.lineColor = Color.parseColor("#00ffffff")
                scatter_chart.axisLeft.addLimitLine(limitLineY)

                val pointY = FloatArray(2)
                pointY[1] = limitLineY.limit
                val transformer = scatter_chart.getTransformer(YAxis.AxisDependency.LEFT)
                transformer.pointValuesToPixel(pointY)
                val pointX = FloatArray(2)
                pointX[0] = limitLineX.limit
                transformer.pointValuesToPixel(pointX)

                val tvContent = lay_float_view.findViewById<TextView>(R.id.tv_fundname)
                val tvSyl = lay_float_view.findViewById<TextView>(R.id.tv_syl)
                val tvBdl = lay_float_view.findViewById<TextView>(R.id.tv_bdl)

                tvContent.text = item?.fundName + ">"
                FundUtils.formatRate(tvSyl, item?.nhsyl)
                tvBdl.text = FundUtils.formatRate(null, item?.nhbdl)

                val params = lay_float_view.layoutParams as FrameLayout.LayoutParams
                val width = scatter_chart.viewPortHandler.contentRight()
                val height = scatter_chart.viewPortHandler.contentBottom()
                //如果定位图标右边的空间比浮层宽,就显示在右边浮层,否则左边,140dp为浮层宽度
                if (width - pointX[0] > DensityUtils.dp2px(140f)) {
                    params.leftMargin = pointX[0].toInt()
                } else {
                    params.leftMargin =
                        (pointX[0] - DensityUtils.dp2px(140f) - DensityUtils.dp2px(10f)).toInt()
                }
                //84dp为浮层高度
                val vh = DensityUtils.dp2px(84f)
                val dx = (pointY[1] - vh / 2)
                when {
                    dx < 0 -> {
                        params.topMargin = 0
                    }
                    dx > height - vh -> {
                        params.topMargin = (height - vh).toInt()
                    }
                    else -> {
                        params.topMargin = (dx - DensityUtils.dp2px(5f)).toInt()
                    }
                }
                lay_float_view.layoutParams = params
                val ivParams = iv_float_location.layoutParams as FrameLayout.LayoutParams
                ivParams.leftMargin = pointX[0].toInt() - DensityUtils.dp2px(12f / 2) //12、14为定位图标宽高
                ivParams.topMargin = pointY[1].toInt() - DensityUtils.dp2px(14f)
                lay_float_view.visibility = View.VISIBLE
                val locationDrawable = ContextCompat.getDrawable(GlobalApp.getApp(), R.drawable.sm_icon_map4)
                iv_float_location.setImageDrawable(locationDrawable)
                iv_float_location.visibility = View.VISIBLE
                lay_float_view.setOnClickListener {
                    SmOptHelper.launcherToSmProdDetails(
                        activity,
                        SmOptHelper.PROD_TYPE.PROD_SM,
                        item?.fundCode!!,
                        item.fundName,
                        0
                    )
                }
                makeTextViewResizable(tvContent, 2, ">")
            }, 100)
        } else {
            lay_float_view.visibility = View.GONE
            iv_float_location.visibility = View.GONE
        }
    }

    private var guideView: GuideView? = null
    private fun dealTips(view: View) {
        if (!CommonStorageUtils.getBoolean(
                SpConfig.SF_SM_MANAGER_FXSY_LAND_PK_TIPS + getHboneNo(),
                false
            )
        ) {
            val view1 = View.inflate(activity, R.layout.lay_manager_tips, null)
            guideView = GuideView.Builder
                .newInstance(activity)
                .setTargetView(view) //设置目标view
                .setTextGuideView(view1) //设置文字图片
                .setTextDirection(GuideView.Direction.LEFT) //方向
                .setTextOffset(DensityUtils.dip2px(350f), DensityUtils.dip2px(10f)) //偏移量
                .setShape(GuideView.MyShape.RECTANGULAR) //矩形
                .setRadius(DensityUtils.dip2px(10f)) //圆角
                .setContain(false) //透明的方块时候包含目标view 默认false
                .setBgColor(ColorUtils.parseColor("#bb000000")) //背景颜色
                .setOnclickListener { guideView?.hide() }
                .build()
            guideView?.show()
            CommonStorageUtils.putBoolean(
                    SpConfig.SF_SM_MANAGER_FXSY_LAND_PK_TIPS + getHboneNo(), true)

        }
    }

    private fun makeTextViewResizable(tv: TextView, maxLine: Int, expandText: String) {
        tv.gravity = Gravity.START
        val vto = tv.viewTreeObserver
        vto.addOnGlobalLayoutListener(object : OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                val obs = tv.viewTreeObserver
                obs.removeOnGlobalLayoutListener(this)
                if (maxLine > 0 && tv.lineCount > maxLine /* && tv.getLayout().getEllipsisCount(tv.getLineCount() - 1) > 0*/) {
                    val lineEndIndex = tv.layout.getLineEnd(maxLine - 1)
                    val text = tv.text.subSequence(0, lineEndIndex - expandText.length - 1)
                        .toString().trim() + "..." + expandText
                    tv.text = text
                }
            }
        })
    }

}