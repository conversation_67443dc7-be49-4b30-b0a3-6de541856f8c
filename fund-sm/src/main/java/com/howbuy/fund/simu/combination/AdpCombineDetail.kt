package com.howbuy.fund.simu.combination

import android.view.View
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.howbuy.fund.simu.R
import com.howbuy.fund.simu.entity.CombineDetail
import com.howbuy.lib.utils.StrUtils
import kotlinx.android.synthetic.main.item_combine_detail.view.*

/**
 * Create by zsm on 2020/6/15.
 **/
class AdpCombineDetail(val onClick: View.OnClickListener?) :
    BaseQuickAdapter<CombineDetail.Item, BaseViewHolder>(R.layout.item_combine_detail) {

    override fun convert(holder: BaseViewHolder, item: CombineDetail.Item) {
        holder.itemView.run {
            tv_fund_name.text = item.jjjc
            if (!StrUtils.isEmpty(item.tzcl)) {
                tv_label.text = item.tzcl
                tv_label.visibility = View.VISIBLE
            } else {
                tv_label.visibility = View.GONE
            }
            tv_fund_rate.text = item.jjzb
        }
        holder.itemView.tag = item
        holder.itemView.setOnClickListener(onClick)
    }
}