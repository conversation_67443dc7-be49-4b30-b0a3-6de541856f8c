package com.howbuy.fund.simu.headline

import android.app.Activity
import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import com.alibaba.android.arouter.facade.annotation.Route
import com.howbuy.fund.base.aty.AtyEmpty
import com.howbuy.fund.base.config.ValConfig
import com.howbuy.fund.base.frag.AbsHbFrag
import com.howbuy.fund.base.router.fragpath.SmRouterPath
import com.howbuy.fund.net.entity.http.ReqNetOpt
import com.howbuy.fund.net.entity.http.ReqResult
import com.howbuy.fund.net.interfaces.IReqNetFinished
import com.howbuy.fund.simu.CommonSmBuilder
import com.howbuy.fund.simu.R
import com.howbuy.fund.simu.entity.SmHeadNewestItem
import com.howbuy.fund.simu.entity.SmWeekliesHome
import com.howbuy.lib.adp.AbsFragPageAdp
import com.howbuy.lib.compont.GlobalApp
import com.howbuy.lib.utils.StatusBarUtil
import com.howbuy.lib.utils.StrUtils
import com.howbuy.share.entity.WorkWeixinEntity
import com.howbuy.userdata.business.getHboneNo
import kotlinx.android.synthetic.main.frag_head_weeklies_home.*
import com.howbuy.android.analytics.annotation.pv.PvInfo
import com.howbuy.fund.base.utils.FragmentUtils
import com.howbuy.lib.interfaces.IShareProvider
import com.howbuy.router.proxy.Invoker

/**
 * Create by zsm on 2018/11/26.
 **/
@Route(path = SmRouterPath.PATH_FRAG_HEAD_WEEKLIES_HOME)
@PvInfo(pageId = "372510", level = "2", name = "私募头条-头条周刊", className = "FragHeadWeekliesHome")
class FragHeadWeekliesHome : AbsHbFrag(), IReqNetFinished {
    companion object {
        private const val NET_WEEKLIES_HOME = 1
    }

    private var mTypeList = mutableListOf<String>()
    private var mWeekliesId: String? = null
    private var mData: SmWeekliesHome? = null


    override fun getFragLayoutId(): Int {
        return R.layout.frag_head_weeklies_home
    }

    override fun stepAllViews(root: View?, savedInstanceState: Bundle?) {
        activity?.let {
            StatusBarUtil.setStatusBarLightMode(activity, ContextCompat.getColor(it, R.color.fd_title), false)
        }
    }

    override fun parseArgment(arg: Bundle?) {
        mWeekliesId = arg?.getString(ValConfig.IT_ID)
        if (StrUtils.isEmpty(mWeekliesId)) {
            activity?.finish()
            return
        }

        requestData()
    }

    fun requestData() {
        CommonSmBuilder.requestSmHeadSubscribeHome(getHboneNo(), mWeekliesId, NET_WEEKLIES_HOME, this)
    }

    override fun onReqNetFinished(result: ReqResult<ReqNetOpt>) {
        if (activity == null || activity!!.isFinishing) return
        val handType = result.mReqOpt.handleType
        if (handType == NET_WEEKLIES_HOME) {
            if (result.mData != null && result.isSuccess) {
                mData = result.mData as SmWeekliesHome?
                renderView()
            }
        }
    }

    private fun renderView() {
        tv_desc.text = mData?.title

        mTypeList.clear()
        mData?.hotList?.let {
            if (it.size > 0) mTypeList.add(FragHeadWeekliesList.TYPE_HOT_STR)
        }
        mData?.renwuList?.let {
            if (it.size > 0) mTypeList.add(FragHeadWeekliesList.TYPE_CHARACTER_STR)
        }
        mData?.hgList?.let {
            if (it.size > 0) mTypeList.add(FragHeadWeekliesList.TYPE_MACRO_STR)
        }
        mData?.ybList?.let {
            if (it.size > 0) mTypeList.add(FragHeadWeekliesList.TYPE_RESEARCH_STR)
        }
        mData?.stList?.let {
            if (it.size > 0) mTypeList.add(FragHeadWeekliesList.TYPE_AV_STR)
        }

        if (mTypeList.isEmpty()) {
            showEmptyView()
        } else {
            val adapter = AdpFragPage(childFragmentManager)
            vp_detail.adapter = adapter
            vp_detail.offscreenPageLimit = mTypeList.size - 1
            tab_select.setupWithViewPager(vp_detail)
        }
    }

    override fun onAttachChanged(aty: Activity, isAttach: Boolean) {
        super.onAttachChanged(aty, isAttach)
        if (isAttach) {
            if (activity != null) {
                val actionBar = (activity as AppCompatActivity).supportActionBar
                actionBar?.hide()
                if (activity is AtyEmpty) {
                    (activity as AtyEmpty).mToolbarLine.visibility = View.GONE
                }
            }
        }
    }

    override fun onXmlBtClick(v: View?): Boolean {
        when (v?.id) {
            R.id.lay_back ->
                activity?.finish()
            R.id.iv_share -> {
                val shareEntity = WorkWeixinEntity(mData?.shareTitle, mData?.shareDesc, mData?.shareUrl, null)
                Invoker.getInstance().navigation(IShareProvider::class.java).showShareDialog(activity, shareEntity, null, "私募头条周刊")
            }
        }
        return super.onXmlBtClick(v)
    }

    inner class AdpFragPage internal constructor(val fm: FragmentManager) : AbsFragPageAdp(fm) {

        override fun getItem(position: Int): Fragment {
            val bundle = Bundle()
            bundle.putString(ValConfig.IT_ID, mTypeList[position])
            val fragment = FragmentUtils.instantiate(GlobalApp.getApp(), fm, FragHeadWeekliesList::class.java.name, bundle) as FragHeadWeekliesList
            var listData: MutableList<SmHeadNewestItem>? = null
            when (mTypeList[position]) {
                FragHeadWeekliesList.TYPE_HOT_STR ->
                    listData = mData?.hotList
                FragHeadWeekliesList.TYPE_CHARACTER_STR ->
                    listData = mData?.renwuList
                FragHeadWeekliesList.TYPE_AV_STR ->
                    listData = mData?.stList
                FragHeadWeekliesList.TYPE_MACRO_STR ->
                    listData = mData?.hgList
                FragHeadWeekliesList.TYPE_RESEARCH_STR ->
                    listData = mData?.ybList
            }
            fragment.setData(listData)
            return fragment
        }

        override fun getPageTitle(position: Int): CharSequence {
            return mTypeList[position]
        }

        override fun getCount(): Int {
            return mTypeList.size
        }

        override fun getTag(position: Int): String {
            return mTypeList[position]
        }

    }

}