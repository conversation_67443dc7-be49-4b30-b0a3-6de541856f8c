package com.howbuy.fund.simu.fixed.incometab.chart

import android.graphics.Typeface
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.content.ContextCompat
import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.github.mikephil.charting.interfaces.datasets.ILineDataSet
import com.howbuy.fund.base.utils.FundTextUtils
import com.howbuy.fund.base.utils.FundUtils
import com.howbuy.fund.chart.mpchart.DefaultPercentValueFormat
import com.howbuy.fund.simu.R
import com.howbuy.fund.simu.archive.tendcy.settings.SmChartZSSettingsDlg.Companion.NULL_ID
import com.howbuy.fund.simu.databinding.FragFixedDetailsIncomeYjzsChartBinding
import com.howbuy.fund.simu.fixed.incometab.FragFixedDetailsIncome
import com.howbuy.fund.simu.fixed.incometab.data.ShareViewModelProvider
import com.howbuy.fund.simu.fixed.incometab.entity.FixedYjzsChartItem
import com.howbuy.fund.simu.fixed.incometab.vm.VmFixedDetailsIncomeYjzs
import com.howbuy.fund.simu.utils.SmChartHelper.formatMinMaxValue
import com.howbuy.lib.compont.GlobalApp
import com.howbuy.lib.utils.DensityUtils
import com.howbuy.lib.utils.LogUtils
import com.howbuy.lib.utils.MathUtils

/**
 * Create by zsm on 2019/9/3.
 *
 * Update by jianzhu.zhang on 2020/12/16
 * 券商集合 - 业绩走势
 */
class FragFixedDetailsIncomeYjzsChart : FragFixedDetailsIncomeBaseChart<FragFixedDetailsIncomeYjzsChartBinding>(), View.OnClickListener {

    override fun createViewBinding(inflater: LayoutInflater, container: ViewGroup?): FragFixedDetailsIncomeYjzsChartBinding {
        return FragFixedDetailsIncomeYjzsChartBinding.inflate(inflater, container, false)
    }

    override fun getChartView() = binding.chartLayout.hblineChart

    private var mFundEntryList: MutableList<Entry> = mutableListOf() //本基金绘图数据
    private var mZsjzEntryList: MutableList<Entry> = mutableListOf() //指数绘图数据
    private var mCljzEntryList: MutableList<Entry> = mutableListOf() //策略绘图数据

    private var mFundValue = ""
    private var mZsjzValue = ""
    private var mCljzValue = ""

    private lateinit var mVm: VmFixedDetailsIncomeYjzs

    override fun getFragLayoutId(): Int = R.layout.frag_fixed_details_income_yjzs_chart

    override fun stepAllViews(root: View?, savedInstanceState: Bundle?) {
        super.stepAllViews(root, savedInstanceState)
        binding.layLegendFund.setOnClickListener(this)
        binding.layLegendZsjz.setOnClickListener(this)
        binding.layLegendCljz.setOnClickListener(this)
        binding.layChartLegendClick.setOnClickListener(this)
        binding.tvChangeBjjz.setOnClickListener(this)
        binding.tvTimeSetting.setOnClickListener(this)
        binding.chartLayout.tvQuanShangFullscreen.setOnClickListener(this)
    }

    override fun initVm() {
        mPageType = FragFixedDetailsIncome.TYPE_PERFORMANCE
        mVm = ShareViewModelProvider.get(this, VmFixedDetailsIncomeYjzs::class.java, mCode)
        mVm.mClickChartLineType.observe(this) {
            it?.let {
                //本基金或比较基准高亮
                resetIncomeTitleUI()
                drawChart(it)
            }
        }
        mVm.showData.observe(this) {
            hideLoadingView()
            LogUtils.e("fetchChartData", "业绩走势${mType}-livedata：=${it}")
            if (TextUtils.equals(it, mType)) {
                renderData()
            }
        }
    }

    override fun vm() = mVm

    override fun parseArgment(arg: Bundle?) {
        super.parseArgment(arg)
        if (mFromLandMode) {
            binding.chartLayout.tvQuanShangFullscreen.visibility = View.GONE
            binding.layLegend.visibility = View.GONE
        } else {
            binding.layLegend.visibility = View.VISIBLE
        }
    }

    override fun waterMarkView(): ImageView = binding.chartLayout.ivWatermark

    private fun renderData() {
        val data = mVm.mChartMap[mType]
        renderLegend()
        if (data == null || data.chartList.isNullOrEmpty()) {
            showEmptyView()
        } else {
            hideEmptyView()
            setLineChartData(data.chartList, data.zsdm, data.cldm)
        }
    }

    private fun renderLegend() {
        LogUtils.e(
            "fetchChartData",
            "业绩走势${mType}-渲染：zsmc=${mVm.mCurZsmc},zsdm=${mVm.mCurZsdm},clmc=${mVm.mCurClmc},cldm=${mVm.mCurCldm}"
        )
        binding.tvLegendZsjzName.text = mVm.mCurZsmc
        binding.tvLegendZsjzNameClick.text = mVm.mCurZsmc
        binding.tvLegendCljzName.text = mVm.mCurClmc
        binding.tvLegendCljzNameClick.text = mVm.mCurClmc
        if (!mFromLandMode) {
            var legendNum = 2
            if (TextUtils.isEmpty(mVm.mCurZsdm) || TextUtils.equals(NULL_ID, mVm.mCurZsdm)) {
                legendNum -= 1
                binding.layLegendZsjz.visibility = View.INVISIBLE
                binding.layLegendZsjzClick.visibility = View.GONE
            } else {
                binding.layLegendZsjz.visibility = View.VISIBLE
                binding.layLegendZsjzClick.visibility = View.VISIBLE
            }
            if (TextUtils.isEmpty(mVm.mCurCldm) || TextUtils.equals(NULL_ID, mVm.mCurCldm)) {
                legendNum -= 1
                binding.layLegendCljz.visibility = View.GONE
                binding.layLegendCljzClick.visibility = View.GONE
            } else {
                binding.layLegendCljz.visibility = View.VISIBLE
                binding.layLegendCljzClick.visibility = View.VISIBLE
            }
            legendStyle(legendNum)
        }
    }

    private fun legendStyle(legendNum: Int) {
        val constraintSet = ConstraintSet()
        constraintSet.clone(binding.layLegend)
        binding.tvChangeBjjz.text = if (legendNum > 0) "更换比较基准" else "添加比较基准"
        when (legendNum) {
            0 -> {
                //没有基准的时候，切换基准在第一行右侧
                constraintSet.connect(R.id.lay_change_bjjz, ConstraintSet.START, R.id.lay_legend_fund, ConstraintSet.END)
                constraintSet.connect(R.id.lay_change_bjjz, ConstraintSet.TOP, R.id.lay_legend_fund, ConstraintSet.TOP)
                constraintSet.connect(R.id.lay_change_bjjz, ConstraintSet.BOTTOM, R.id.lay_legend_fund, ConstraintSet.BOTTOM)

                constraintSet.setMargin(R.id.lay_change_bjjz, ConstraintSet.TOP, 0)
            }

            1 -> {
                //一个基准的时候，切换基准在第二行左侧
                constraintSet.connect(R.id.lay_change_bjjz, ConstraintSet.START, R.id.lay_legend_fund, ConstraintSet.START)
                constraintSet.connect(R.id.lay_change_bjjz, ConstraintSet.TOP, R.id.lay_legend_fund, ConstraintSet.BOTTOM)
                constraintSet.clear(R.id.lay_change_bjjz, ConstraintSet.BOTTOM)

                constraintSet.setMargin(R.id.lay_change_bjjz, ConstraintSet.TOP, DensityUtils.dp2px(4f))
                if (TextUtils.isEmpty(mVm.mCurZsdm) || TextUtils.equals(NULL_ID, mVm.mCurZsdm)) {
                    //指数基准为空，则只有一个好买策略基准，需要挪到第一行右侧
                    constraintSet.connect(R.id.lay_legend_cljz, ConstraintSet.START, R.id.lay_legend_fund, ConstraintSet.END)
                    constraintSet.connect(R.id.lay_legend_cljz, ConstraintSet.TOP, R.id.lay_legend_fund, ConstraintSet.TOP)
                    constraintSet.connect(R.id.lay_legend_cljz, ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END)
                    constraintSet.setMargin(R.id.lay_legend_cljz, ConstraintSet.TOP, 0)
                }
            }

            2 -> {
                //两个基准的时候，好买策略基准，需要挪到第二行左侧，切换基准挪到第二行右侧
                constraintSet.connect(R.id.lay_legend_cljz, ConstraintSet.START, ConstraintSet.PARENT_ID, ConstraintSet.START)
                constraintSet.connect(R.id.lay_legend_cljz, ConstraintSet.TOP, R.id.lay_legend_fund, ConstraintSet.BOTTOM)
                constraintSet.connect(R.id.lay_legend_cljz, ConstraintSet.END, R.id.lay_change_bjjz, ConstraintSet.START)
                constraintSet.setMargin(R.id.lay_legend_cljz, ConstraintSet.TOP, DensityUtils.dp2px(4f))

                constraintSet.connect(R.id.lay_change_bjjz, ConstraintSet.START, R.id.lay_legend_cljz, ConstraintSet.END)
                constraintSet.connect(R.id.lay_change_bjjz, ConstraintSet.TOP, R.id.lay_legend_cljz, ConstraintSet.TOP)
                constraintSet.connect(R.id.lay_change_bjjz, ConstraintSet.BOTTOM, R.id.lay_legend_cljz, ConstraintSet.BOTTOM)
                constraintSet.setMargin(R.id.lay_change_bjjz, ConstraintSet.TOP, 0)
            }
        }
        constraintSet.applyTo(binding.layLegend)

        legendClickStyle(legendNum < 2, binding.layLegendFundClick, R.id.tv_fund_value_click, R.id.tv_fund_name_click)
        legendClickStyle(legendNum < 2, binding.layLegendZsjzClick, R.id.tv_legend_zsjz_value_click, R.id.tv_legend_zsjz_name_click)
        legendClickStyle(legendNum < 2, binding.layLegendCljzClick, R.id.tv_legend_cljz_value_click, R.id.tv_legend_cljz_name_click)
    }

    /**
     * 点击浮层样式
     * 单行 双行
     */
    private fun legendClickStyle(isSingleLine: Boolean, parent: ConstraintLayout, tvValueId: Int, tvNameId: Int) {
        val constraintSet = ConstraintSet()
        constraintSet.clone(parent)
        if (isSingleLine) {
            constraintSet.connect(tvNameId, ConstraintSet.END, tvValueId, ConstraintSet.START)
            constraintSet.connect(tvNameId, ConstraintSet.TOP, tvValueId, ConstraintSet.TOP)
            constraintSet.connect(tvNameId, ConstraintSet.BOTTOM, tvValueId, ConstraintSet.BOTTOM)
            constraintSet.connect(tvValueId, ConstraintSet.START, tvNameId, ConstraintSet.END)
            constraintSet.connect(tvValueId, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP)
            constraintSet.connect(tvValueId, ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END)
            constraintSet.connect(tvValueId, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM)
            constraintSet.setMargin(tvValueId, ConstraintSet.START, DensityUtils.dp2px(12f))
        } else {
            constraintSet.connect(tvNameId, ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END)
            constraintSet.connect(tvNameId, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP)
            constraintSet.connect(tvNameId, ConstraintSet.BOTTOM, tvValueId, ConstraintSet.TOP)
            constraintSet.connect(tvValueId, ConstraintSet.START, tvNameId, ConstraintSet.START)
            constraintSet.connect(tvValueId, ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END)
            constraintSet.connect(tvValueId, ConstraintSet.TOP, tvNameId, ConstraintSet.BOTTOM)
            constraintSet.connect(tvValueId, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM)
            constraintSet.setMargin(tvValueId, ConstraintSet.START, 0)
        }
        constraintSet.applyTo(parent)
    }

    private fun setLineChartData(list: List<FixedYjzsChartItem>, zsdm: String?, cldm: String?) {
        mFundValue = FundTextUtils.showTextEmpty(list.lastOrNull()?.jjhb)
        FundUtils.formatRate(binding.tvFundValue, mFundValue)
        mZsjzValue = if (TextUtils.equals(mVm.mCurZsdm, zsdm)) {
            FundTextUtils.showTextEmpty(list.lastOrNull()?.zshb)
        } else {
            "--"
        }
        FundUtils.formatRate(binding.tvLegendZsjzValue, mZsjzValue)
        mCljzValue = if (TextUtils.equals(mVm.mCurCldm, cldm)) {
            FundTextUtils.showTextEmpty(list.lastOrNull()?.clhb)
        } else {
            "--"
        }
        FundUtils.formatRate(binding.tvLegendCljzValue, mCljzValue)
        mFundEntryList.clear()
        mZsjzEntryList.clear()
        mCljzEntryList.clear()
        dateIndexs.clear()
        val minMax = mutableListOf<Float>()
        for ((i: Int, item: FixedYjzsChartItem) in list.withIndex()) {
            dateIndexs.add(item.date ?: "")
            val f: Float = MathUtils.forValF(item.jjhb, 0f)
            mFundEntryList.add(Entry(i.toFloat(), f, item))
            minMax.add(f)
            if (!TextUtils.isEmpty(item.zshb) && TextUtils.equals(mVm.mCurZsdm, zsdm)) {
                val f1 = MathUtils.forValF(item.zshb, 0f)
                mZsjzEntryList.add(Entry(i.toFloat(), f1, item))
                minMax.add(f1)
            }
            if (!TextUtils.isEmpty(item.clhb) && TextUtils.equals(mVm.mCurCldm, cldm)) {
                val f2 = MathUtils.forValF(item.clhb, 0f)
                mCljzEntryList.add(Entry(i.toFloat(), f2, item))
                minMax.add(f2)
            }
        }
        renderTitle(0, false)
        setRightAisleFormat(minMax.minOrNull() ?: 0f, minMax.maxOrNull() ?: 0f)
        drawChart(mVm.mClickChartLineType.value ?: 0)
        val showDate = "${list.firstOrNull()?.date ?: "--"}至${list.lastOrNull()?.date ?: "--"}"
        if (mFromLandMode) {
            binding.chartLayout.tvQuanShangFullscreen.visibility = View.GONE
            if (needShowTimeSetting()) {
                //此处仅赋值横屏下自定义时间范围，是否显示由外层去控制
                (parentFragment as FragFixedDetailsIncomeBase).setSelectTime(showDate)
            }
        } else {
            binding.chartLayout.tvQuanShangFullscreen.visibility = View.VISIBLE
            if (needShowTimeSetting()) {
                binding.layTimeSetting.visibility = View.VISIBLE
                binding.tvTimeSetting.text = showDate
            } else {
                binding.layTimeSetting.visibility = View.GONE
            }
        }
    }

    override fun onPageScroll() {
        binding.layChartLegendClick.visibility = View.GONE
        cleanHighLight()
    }

    override fun cleanHighLight() {
        getChartView().cleanHighlight()
    }

    override fun renderTitle(index: Int, isClick: Boolean) {
        try {
            renderLegend()
            var fundValue = ""
            var zsjzValue = ""
            var cljzValue = ""
            if (isClick) {
                try {
                    val item = if (index < mFundEntryList.size) mFundEntryList[index].data else null
                    if (item is FixedYjzsChartItem) {
                        fundValue = item.jjhb ?: "--"
                        zsjzValue = item.zshb ?: "--"
                        cljzValue = item.clhb ?: "--"
                    } else {
                        fundValue = "--"
                        zsjzValue = "--"
                        cljzValue = "--"
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            } else {
                fundValue = mFundValue
                zsjzValue = mZsjzValue
                cljzValue = mCljzValue
                getChartView().cleanHighlight()
            }

            FundUtils.formatRate(binding.tvFundValueClick, fundValue)
            FundUtils.formatRate(binding.tvLegendZsjzValueClick, zsjzValue)
            FundUtils.formatRate(binding.tvLegendCljzValueClick, cljzValue)

            if (mFromLandMode) {
                //横屏手指触摸
                if (isClick) {
                    mVm.onLandFloatView(mType, dateIndexs[index], fundValue, zsjzValue, cljzValue, false)
                } else {
                    mVm.onLandFloatView(mType, FundTextUtils.showTextEmpty(dateIndexs.lastOrNull()), fundValue, zsjzValue, cljzValue, true)
                }
            } else {
                if (!isClick) {
                    binding.layChartLegendClick.visibility = View.GONE
                } else {
                    binding.layChartLegendClick.visibility = View.VISIBLE
                    binding.tvClickDate.text = if (dateIndexs.isEmpty()) "--" else dateIndexs[index]
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 调整Y轴上显示的坐标值
     */
    private fun setRightAisleFormat(min: Float, max: Float) {
        /**
         * 走势图纵坐标轴调整:
         * 最高收益*（1+(-)10%），最低收益*（1-10%）的结果，个位数位上就近取5倍数的整数
         * （如，最高值*（1+10%）=6%，那上限则直接取为10%；
         * 最低值*（1+(-)10%）=-7%,那下线取值为-10%）以此为纵坐标轴的上线和下线值，
         * 中间纵坐标轴刻度线五等分(相当于有6个刻度)；
         */
        val yAisleMax = formatMinMaxValue(max, true, 2)
        mRightYAxis.axisMaximum = yAisleMax

        val yAisleMin = formatMinMaxValue(min, false, 2)
        mRightYAxis.axisMinimum = yAisleMin

        mRightYAxis.setLabelCount(5, true)
        mRightYAxis.valueFormatter = DefaultPercentValueFormat(1, true)
    }

    private fun drawChart(type: Int) {
        if (!isAdded) return

        val sets = java.util.ArrayList<ILineDataSet>()

        if (mFundEntryList.isNotEmpty()) {
            val fundSet = LineDataSet(mFundEntryList, "")
            fundSet.mode = LineDataSet.Mode.LINEAR
            fundSet.axisDependency = YAxis.AxisDependency.RIGHT
            fundSet.lineWidth = 1f
            fundSet.color = ContextCompat.getColor(GlobalApp.getApp(), R.color.fd_f92d2d)
            fundSet.isHighlightEnabled = true
            fundSet.highlightLineWidth = 1f
            fundSet.highLightColor = ContextCompat.getColor(GlobalApp.getApp(), R.color.fd_f92d2d)
            fundSet.setDrawVerticalHighlightIndicator(true)
            fundSet.setDrawHorizontalHighlightIndicator(false)
            fundSet.setDrawValues(false)
            fundSet.setDrawCircles(false)
            fundSet.circleRadius = 0f
            if (type == 1) {
                fundSet.setDrawFilled(true)
                fundSet.lineWidth = 1.3f
                fundSet.fillDrawable = ContextCompat.getDrawable(GlobalApp.getApp(), R.drawable.bg_sm_chart_fill_red)
                fundSet.setFillFormatter { _, dataProvider -> dataProvider.yChartMin }
            } else {
                fundSet.setDrawFilled(false)
            }
            if (type == 1) {
                sets.add(fundSet)
            } else {
                sets.add(0, fundSet)
            }
        }

        if (mZsjzEntryList.isNotEmpty()) {
            val otherSet = LineDataSet(mZsjzEntryList, "")
            otherSet.mode = LineDataSet.Mode.LINEAR
            otherSet.axisDependency = YAxis.AxisDependency.RIGHT
            otherSet.lineWidth = 1f
            otherSet.color = ContextCompat.getColor(GlobalApp.getApp(), R.color.fd_4c77aa)
            otherSet.isHighlightEnabled = false
            otherSet.setDrawValues(false)
            otherSet.setDrawCircles(false)
            otherSet.circleRadius = 0f
            if (type == 2) {
                otherSet.setDrawFilled(true)
                otherSet.lineWidth = 1.3f
                otherSet.fillDrawable = ContextCompat.getDrawable(GlobalApp.getApp(), R.drawable.bg_sm_chart_fill_blue)
                otherSet.setFillFormatter { _, dataProvider -> dataProvider.yChartMin }
            } else {
                otherSet.setDrawFilled(false)
            }
            if (type == 2) {
                sets.add(otherSet)
            } else {
                sets.add(0, otherSet)
            }
        }

        if (mCljzEntryList.isNotEmpty()) {
            val otherSet2 = LineDataSet(mCljzEntryList, "")
            otherSet2.mode = LineDataSet.Mode.LINEAR
            otherSet2.axisDependency = YAxis.AxisDependency.RIGHT
            otherSet2.lineWidth = 1f
            otherSet2.color = ContextCompat.getColor(GlobalApp.getApp(), R.color.fd_9e9e9e)
            otherSet2.isHighlightEnabled = false
            otherSet2.setDrawValues(false)
            otherSet2.setDrawCircles(false)
            otherSet2.circleRadius = 0f
            if (type == 3) {
                otherSet2.setDrawFilled(true)
                otherSet2.lineWidth = 1.5f
                otherSet2.fillDrawable = ContextCompat.getDrawable(GlobalApp.getApp(), R.drawable.bg_sm_chart_fill_gray)
                otherSet2.setFillFormatter { _, dataProvider -> dataProvider.yChartMin }
            } else {
                otherSet2.setDrawFilled(false)
            }
            if (type == 3) {
                sets.add(otherSet2)
            } else {
                sets.add(0, otherSet2)
            }
        }

        getChartView().data = LineData(sets)
        getChartView().invalidate()
        setFullScreenPosition(getChartView(), binding.chartLayout.tvQuanShangFullscreen)
    }

    private fun resetIncomeTitleUI() {
        binding.tvFundName.typeface = Typeface.DEFAULT
        binding.tvLegendZsjzName.typeface = Typeface.DEFAULT
        binding.tvLegendCljzName.typeface = Typeface.DEFAULT
        when (mVm.mClickChartLineType.value) {
            1 -> binding.tvFundName.typeface = Typeface.DEFAULT_BOLD
            2 -> binding.tvLegendZsjzName.typeface = Typeface.DEFAULT_BOLD
            3 -> binding.tvLegendCljzName.typeface = Typeface.DEFAULT_BOLD
        }
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.lay_legend_fund -> yjzsChartLineClick(1)
            R.id.lay_legend_zsjz -> yjzsChartLineClick(2)
            R.id.lay_legend_cljz -> yjzsChartLineClick(3)
            R.id.lay_chart_legend_click -> renderTitle(0, false)
            R.id.tv_change_bjjz -> {
                if (parentFragment is FragFixedDetailsIncomeYjzs) {
                    (parentFragment as FragFixedDetailsIncomeYjzs).setBjjzDialog(childFragmentManager)
                }
            }

            R.id.tv_time_setting -> {
                if (parentFragment is FragFixedDetailsIncomeBase) {
                    (parentFragment as FragFixedDetailsIncomeBase).showTimeDlg()
                }
            }

            R.id.tv_quan_shang_fullscreen -> toLand()
        }
    }

    private fun yjzsChartLineClick(position: Int) {
        if (mVm.mClickChartLineType.value == position) {
            mVm.mClickChartLineType.postValue(0)
        } else {
            mVm.mClickChartLineType.postValue(position)
        }
    }

    override fun showLoadingView() {
        binding.loading.visibility = View.VISIBLE
    }

    override fun hideLoadingView() {
        binding.loading.visibility = View.GONE
    }

    override fun hideEmptyView() {
        binding.empty.visibility = View.GONE
    }

    override fun showEmptyView() {
        try {
            binding.tvFundValue.text = "--"
            binding.tvLegendZsjzValue.text = "--"
            binding.tvLegendCljzValue.text = "--"
            binding.empty.visibility = View.VISIBLE
            mVm.onLandFloatView(mType, null, null, null, null, false)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}