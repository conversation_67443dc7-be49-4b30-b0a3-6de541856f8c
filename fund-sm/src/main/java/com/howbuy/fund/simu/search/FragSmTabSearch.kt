package com.howbuy.fund.simu.search

import android.os.Bundle
import android.text.TextUtils
import android.view.View
import androidx.viewpager.widget.ViewPager
import com.google.android.material.tabs.TabLayout
import com.howbuy.android.analytics.annotation.pv.PvInfo
import com.howbuy.fund.base.analytics.HbAnalytics
import com.howbuy.fund.base.livedata.LiveDataBus
import com.howbuy.fund.simu.utils.LiveDataEventKey
import com.howbuy.fund.common.search.FragCommonSearchList
import com.howbuy.fund.common.util.PerformanceTracer
import com.howbuy.fund.common.util.PfmEvent
import com.howbuy.fund.common.util.TimeNode
import com.howbuy.fund.simu.R
import com.howbuy.lib.utils.LogUtils
import kotlinx.android.synthetic.main.frag_sm_tab_search_layout.*

/**
 * class description.
 * 私募搜索tab页面
 * <AUTHOR>
 * @date 2021/3/19
 */
@PvInfo(pageId = "371090", level = "2", name = "私募搜索-全部tab页", className = "FragSmTabSearch")
class FragSmTabSearch : FragCommonSearchList(), View.OnClickListener {

    //当前搜索的关键字(用于判断处理, 全部tab下时, 更新tab操作只执行一次)
    private var mCurSearchKey: String? = null

    //当前tab的内容,用于处理 在 非全部tab下, 修改了搜索关键字后,再切回到全部时,需要刷新tab内容
    private var mCurSearchArrayDataSize = intArrayOf()
    private var mNeedExecute = false

    //控制埋点上报
    private var mCanReportClick = true

    //当前viewpager的定位
    private var mPosition = 0

    fun setPageUIStatus(curSearchKey: String?, visible: Boolean, size: IntArray) {
        if (visible) {
            mCurSearchKey = curSearchKey
            ll_tabbar.visibility = View.VISIBLE
            vp_sm_search.isCanHScroll = true
            resetPageSize(size)
        } else {
            ll_tabbar.visibility = View.GONE
            vp_sm_search.isCanHScroll = false
        }
    }

    private fun resetPageSize(size: IntArray) {
        if (vp_sm_search.currentItem != 0) {
            //仅当定位在"全部"tab时,才执行判断,其它tab下是否有数据
            mCurSearchArrayDataSize = size
            mNeedExecute = true
            return
        }
        if (size.size == 7) {
            val smFundSize = size[0]
            val smMgrSize = size[1]
            val smComSize = size[2]
            val smAudioSize = size[3]
            val smNewsSize = size[4]
            val smFuncSize = size[5]
            val smDiscussContentSize = size[6]

            val tabList = mutableListOf<String>()
            tabList.add("全部")
            if (smFundSize > 0) {
                tabList.add("基金产品")
            }
            if (smMgrSize > 0) {
                tabList.add("基金经理")
            }
            if (smComSize > 0) {
                tabList.add("基金公司")
            }
            if (smAudioSize > 0) {
                tabList.add("视听")
            }
            if (smNewsSize > 0) {
                tabList.add("资讯研报")
            }
            if (smFuncSize > 0) {
                tabList.add("相关功能")
            }
            if (smDiscussContentSize > 0) {
                tabList.add("讨论")
            }
            mAdapger?.updateListFragmnets(tabList)
            vp_sm_search.offscreenPageLimit = tabList.size
            mNeedExecute = false
            lay_right.visibility = if (tabList.size > 4) {
                View.VISIBLE
            } else {
                View.GONE
            }
        }
    }

    override fun getFragLayoutId(): Int {
        return R.layout.frag_sm_tab_search_layout
    }

    private var mAdapger: AdpSmTabSearchPager? = null

    private var tracer: PerformanceTracer? = PerformanceTracer { }

    override fun stepAllViews(root: View?, savedInstanceState: Bundle?) {
        LogUtils.d("[Search-SmTab]", "stepAllViews, activity:" + (activity == null))
        tab_select.tabMode = TabLayout.MODE_SCROLLABLE
        /*
        * 首次加载私募搜索,切换公募/私募搜索时,会执行stepAllViews方法(要重新加载)
        * 不能调用mAdapger.updateListFragmnets:会抛出异常,因为FragTabSearch写的是每次切换tab都是重新commit的
        * java.lang.NullPointerException: Attempt to invoke virtual method 'android.content.Context androidx.fragment.app.FragmentHostCallback.getContext()'
        * on a null object reference
         */
        val tabList = if (mAdapger == null) {
            mutableListOf<String>("全部", "基金产品", "基金经理", "基金公司", "视听", "资讯研报", "相关功能", "讨论")
        } else {
            mAdapger!!.mTabList
        }
        mAdapger = AdpSmTabSearchPager(activity, childFragmentManager, tabList)
        vp_sm_search.adapter = mAdapger
        tab_select.setupWithViewPager(vp_sm_search)
        vp_sm_search.offscreenPageLimit = mAdapger?.mTabList?.size ?: 1
        lay_right.setOnClickListener(this)
        vp_sm_search.addOnPageChangeListener(object : ViewPager.SimpleOnPageChangeListener() {
            override fun onPageSelected(position: Int) {
                mPosition = position
                iv_right.isEnabled = mPosition != (mAdapger?.mTabList?.size ?: 1) - 1
                analytics(mAdapger?.mTabList?.get(position), true)
                mCanReportClick = true
                if (position == 0 && mNeedExecute) {
                    resetPageSize(mCurSearchArrayDataSize)
                }
                LiveDataBus.get().with(LiveDataEventKey.BUS_KEY_SM_SEARCH_TAB_CHANGED).postValue(position)
            }
        })
    }

    private var isInit = false
    fun startPageTrace() {
        if (isInit) return
        isInit = true
        LogUtils.e("[Search-Entry]", "页面初始化")
        tracer?.pageId = "371090"
        tracer?.pageName = "百万起搜索-搜索结果页"
        tracer?.start()
        tracer?.recordTime(PfmEvent.GENERATE_TRACE_ID, 1)
    }

    override fun parseArgment(arg: Bundle?) {
        analytics("", false)
    }

    fun getPosByTitle(title: String?): Int {
        var pos = 0
        run breaking@{
            mAdapger?.mTabList?.forEachIndexed { index, s ->
                if (TextUtils.equals(title, s)) {
                    pos = index
                    return@breaking
                }
            }
        }
        return pos
    }


    override fun hasOptionMenu(): Boolean {
        /**
         * 这个很重要,[中也同时需要实现该方法,否则 FragTabSearch的搜索菜单输入框会无效][FragGmSearchList]
         */
        return false
    }

    fun setCurrentIndex(pos: Int) {
        mCanReportClick = false
        vp_sm_search.currentItem = pos
    }

    fun getCurrentIndex(): Int {
        return vp_sm_search.currentItem
    }

    override fun onClick(v: View?) {
        if (v?.id == R.id.lay_right) {
            tab_select.setScrollPosition(mPosition, 1f, true)
            mPosition++
            iv_right.isEnabled = mPosition != (mAdapger?.mTabList?.size ?: 1) - 1
            vp_sm_search.currentItem = mPosition
        }
    }

    override fun isAutoAnalytics(): Boolean {
        return false
    }

    private fun analytics(tabTitle: String?, userClick: Boolean) {
        val clickId = when (tabTitle) {
            "基金产品" -> {
                "79430"
            }

            "基金经理" -> {
                "72150"
            }

            "基金公司" -> {
                "79440"
            }

            "视听" -> {
                "79450"
            }

            "资讯研报" -> {
                "71010"
            }

            "相关功能" -> {
                "335560"
            }

            "讨论" -> {
                "336770"
            }

            else -> {
                "79420"
            }
        }
        if (userClick && mCanReportClick) {
            HbAnalytics.onClick(activity, clickId)
        }
    }

    fun onPageChildListNetStart(tabPosition: Int, apiId: String) {
        if (mPosition == tabPosition) {
            LogUtils.e("[Search-Entry]", "开始请求搜索接口")
            tracer?.apiFetchStart(apiId)
        }
    }

    fun onPageChildListNetEnd(tabPosition: Int, timeNode: TimeNode) {
        if (mPosition == tabPosition) {
            LogUtils.e("[Search-Entry]", "搜索接口请求结束")
            tracer?.apiFetchDone(
                timeNode.key ?: "",
                timeNode.realStart ?: 0L,
                timeNode.end ?: System.currentTimeMillis(),
                timeNode.isSuccess ?: true,
                timeNode.isFromCache ?: false
            )
        }
    }

    fun onPageLoadFinish(tabPosition: Int) {
        if (mPosition == tabPosition) {
            LogUtils.e("[Search-Entry]", "页面绘制结束")
            tracer?.recordTime(PfmEvent.PAGE_REMOTE_END, 1)
        }
    }
}