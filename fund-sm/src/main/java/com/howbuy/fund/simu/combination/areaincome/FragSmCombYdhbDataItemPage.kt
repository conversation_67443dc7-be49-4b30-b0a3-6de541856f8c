package com.howbuy.fund.simu.combination.areaincome

import android.os.Bundle
import android.text.TextUtils
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.gson.GsonUtils
import com.google.gson.reflect.TypeToken
import com.howbuy.fund.base.config.ValConfig
import com.howbuy.fund.base.frag.FragLazyLoad
import com.howbuy.fund.simu.R
import com.howbuy.fund.simu.entity.CombineDetail
import com.howbuy.fund.simu.entity.CombineSimple
import com.howbuy.fund.simu.entity.SmCombAreaIncomeBody
import com.howbuy.lib.utils.DateUtils
import com.howbuy.lib.utils.StrUtils
import android.view.LayoutInflater
import android.view.ViewGroup
import com.howbuy.fund.base.frag.FragLazyViewBinding
import com.howbuy.fund.simu.databinding.FragSmCombYdhbDataItemLayoutBinding

/**
 * class description.
 * 私募组合模拟-区间收益模块-月度数据 Viewpager的item 页面
 * <AUTHOR>
 * @date 2021/8/13
 */
class FragSmCombYdhbDataItemPage : FragLazyViewBinding<FragSmCombYdhbDataItemLayoutBinding>(){

    override fun createViewBinding(inflater: LayoutInflater, container: ViewGroup?): FragSmCombYdhbDataItemLayoutBinding {
        return FragSmCombYdhbDataItemLayoutBinding.inflate(inflater, container, false)
    }

    //组合接口字段数据
    var mCombSimpleData: CombineSimple? = null

    //有效成分基金列表数据
    var mValidCombTitleList = mutableListOf<CombineDetail.Item>()
    var mValidCombFundList = mutableListOf<CombineDetail.Item>()
    var mBody: SmCombAreaIncomeBody? = null
    var mRealListData = mutableListOf<SmCombAreaIncomeBody.MonthItem>()
    val itemList = mutableListOf<SmCombAreaIncomeBody.MonthItem>()
    var yearHbdata: SmCombAreaIncomeBody.YearItem? = null

    //当前年度[格式: 2020]
    var mCurYear: String? = null

    //原始成分基金列表
    var mFundList: MutableList<CombineDetail.Item>? = null

    override fun getFragLayoutId(): Int = R.layout.frag_sm_comb_ydhb_data_item_layout

    override fun stepAllViews(root: View?, savedInstanceState: Bundle?) {
    }

    override fun parseArgment(arg: Bundle?) {
        mCurYear = arg?.getString(ValConfig.IT_ID)
        mBody = arg?.getParcelable<SmCombAreaIncomeBody>(ValConfig.IT_ENTITY)
        //成分基金列表
        mFundList = arg?.getParcelableArrayList<CombineDetail.Item>(ValConfig.IT_VALUE_3)
        //组合对象数据相关
        mCombSimpleData = arg?.getParcelable<CombineSimple>(ValConfig.IT_BEAN)
    }

    override fun fetchData() {
        setUIData(mFundList)
    }

    private fun setUIData(fundList: MutableList<CombineDetail.Item>?) {
        showAlermDlg("加载中...", false, false)
        //比较基准列表
        var bjjzCode1: String? = ""
        var bjjzName1: String? = ""
        var bjjzCode2: String? = ""
        var bjjzName2: String? = ""
        if (!TextUtils.isEmpty(mCombSimpleData?.bjjzArray)) {
            val bjjzList = GsonUtils.toObj<MutableList<CombineDetail.BjjzItem>>(mCombSimpleData?.bjjzArray, object : TypeToken<MutableList<CombineDetail.BjjzItem>>() {}.type)
            //获取比较基准列表中的数据,最多两个
            bjjzList?.forEachIndexed { index1, bjjzItem ->
                if (index1 == 0) {
                    bjjzCode1 = bjjzItem.bjjzCode
                    bjjzName1 = bjjzItem.bjjzName
                } else if (index1 == 1) {
                    bjjzCode2 = bjjzItem.bjjzCode
                    bjjzName2 = bjjzItem.bjjzName
                }
            }
        }
        //把本组合比较基金,都当成成分基金添加列表表头
        //1. 组装:年度列表头,本组合列表头,比较基准1列表头, 比较基准2列表头 的数据
        mValidCombTitleList.add(CombineDetail.Item("", "", "", "", ""))
        mValidCombFundList.add(CombineDetail.Item("本组合", "ydhb", "", "", ""))
        mValidCombFundList.add(CombineDetail.Item(bjjzName1, "bjydhb", "", "", ""))
        if (!TextUtils.isEmpty(bjjzCode2)) {
            mValidCombFundList.add(CombineDetail.Item(bjjzName2, "bjydhb2", "", "", ""))
        }
        //按比较基准数据将 成分基金列表中的数据去重:
        // 成分基金需与比较基准去重显示，即若选择的比较基准中有成分基金，则选择作为比较基准的成分基金不再重复显示；且成分基金作为比较基准，其基金简称下不展示固定文案
        //2. 组装成分基金列表头数据
        fundList?.forEachIndexed { index, item ->
            //判断比较基准是否与分成基金有重复
            if (!TextUtils.isEmpty(item.jjdm) && !TextUtils.equals(item.jjdm, bjjzCode1) && !TextUtils.equals(item.jjdm, bjjzCode2)) {
                //借用 cpfl字段设置为"1",标记为成分基金
                mValidCombFundList.add(CombineDetail.Item(item.jjjc, "cfjjydhb${(index + 1)}", "", "", "1"))
            }
        }
        //3. 获取当前年份的月度回报数据list(最多1月-12月)
        itemList.clear()
        mBody?.dataArray?.forEachIndexed { _, monthItem ->
            val keyGroup = DateUtils.timeFormat(monthItem.ydhbDate, DateUtils.DATEF_YMD_10, DateUtils.DATEF_YEAR)
            if (StrUtils.equals(keyGroup, mCurYear)) {
                itemList.add(monthItem)
            }
        }
        //月分反序排列
        itemList.reverse()
        //4. 获取当前年度对应的年度回报数据对象
        run breaking@{
            mBody?.ndDataArray?.forEachIndexed { _, yearItem ->
                if (TextUtils.equals(mCurYear, yearItem.ndhbDate)) {
                    yearHbdata = yearItem
                    return@breaking
                }
            }
        }
        //加载数据(支持横向数据列表滚动,垂直方向上是因为外部为一个ScrollView,所以不用关心垂直方向的滚动联动)
        if (mBody?.dataArray?.isNotEmpty() == true) {
            binding.rcvCombYdhbTitle.layoutManager = LinearLayoutManager(activity)
            binding.rcvCombYdhbTitle.adapter = mAdpMonthItemTitle
            binding.rcvCombYdhb.layoutManager = LinearLayoutManager(activity, LinearLayoutManager.HORIZONTAL, false)
            binding.rcvCombYdhb.adapter = mAdpMonthItem

        }
        //关闭进度条
        showAlermDlg(null, 0)
    }

    //默认选中第一个年份
    private val mAdpMonthItem: AdpSmCombMonth by lazy {
        AdpSmCombMonth(activity, mValidCombFundList, itemList, yearHbdata, 0, false)
    }

    //默认选中第一个年份
    private val mAdpMonthItemTitle: AdpSmCombMonth by lazy {
        AdpSmCombMonth(activity, mValidCombTitleList, itemList, yearHbdata, 0, true)
    }

}