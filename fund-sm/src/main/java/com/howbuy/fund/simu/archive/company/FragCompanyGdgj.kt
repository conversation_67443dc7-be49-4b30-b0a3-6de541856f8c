package com.howbuy.fund.simu.archive.company

import android.os.Bundle
import android.text.TextUtils
import android.view.View
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.howbuy.fund.base.config.ValConfig
import com.howbuy.fund.base.frag.AbsHbFrag
import com.howbuy.fund.base.nav.NavHelper
import com.howbuy.fund.base.router.RouterHelper
import com.howbuy.fund.base.router.fragpath.SmRouterPath
import com.howbuy.fund.base.router.fragpath.WebRouterPath
import com.howbuy.fund.base.utils.FundTextUtils
import com.howbuy.fund.simu.R
import com.howbuy.fund.simu.entity.CompanyDetail
import com.howbuy.fund.simu.headline.SmHeadLineLauncher
import com.howbuy.lib.utils.DateUtils
import kotlinx.android.synthetic.main.frag_company_gdgj.*
import java.util.*

/**
 * class description.
 * 私募公司档案页面-基本信息模块下- 观点轨迹
 * <AUTHOR>
 * @date 2020/11/9
 */
class FragCompanyGdgj : AbsHbFrag(), View.OnClickListener {
    private var listData: ArrayList<CompanyDetail.Scgd>? = null
    var mCurPos: Int = 0

    override fun stepAllViews(root: View?, savedInstanceState: Bundle?) {
        tv_sm_mgr_msg_pre.setOnClickListener(this)
        tv_sm_mgr_msg_next.setOnClickListener(this)
        tv_sm_mgr_msg_title.setOnClickListener(this)
    }

    override fun getFragLayoutId(): Int = R.layout.frag_company_gdgj

    override fun parseArgment(arg: Bundle?) {
        listData = arg?.getParcelableArrayList(ValConfig.IT_ENTITY)
        setUIData()
        setButtonEnable()
    }

    private fun setUIData() {
        listData?.let { it ->
            val item = it[mCurPos]
            val curDate = Calendar.getInstance()
            val curYear = curDate.get(Calendar.YEAR)
            if (item.time?.contains(curYear.toString()) == true) {
                tv_sm_mgr_msg_date.text = DateUtils.dateFormat(item.time, DateUtils.DATEF_YMD_, "MM月dd日")
            } else {
                if (TextUtils.isEmpty(item.time)) {
                    tv_sm_mgr_msg_date.text = "--"
                } else {
                    tv_sm_mgr_msg_date.text = DateUtils.dateFormat(item.time, DateUtils.DATEF_YMD_, "yyyy年MM月dd日")
                }
            }
            tv_sm_mgr_msg_title.text = FundTextUtils.showTextEmpty(item.title)
            val list = mutableListOf<String>()
            //转换为list,添加进去(在scrollview中嵌套scrollview,滑动事件冲突)
            item.summary?.let {
                list.add(it)
            }
            rcv_sm_comp_rwgj.apply {
                this.layoutManager = LinearLayoutManager(activity, LinearLayoutManager.VERTICAL, false)
                this.adapter = object : BaseQuickAdapter<String, BaseViewHolder>(R.layout.item_sm_mgr_basic_info_sub_page_rwgj_layout, list) {
                    override fun convert(holder: BaseViewHolder, t: String) {
                        holder.setText(R.id.tv_sm_mgr_msg_desc, FundTextUtils.showTextEmpty(t))
                        holder.itemView.setOnClickListener {
                            toArticleDetail(item.contentUrl, item.contentType)
                        }
                    }
                }
            }
        }
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.tv_sm_mgr_msg_pre -> {
                if (mCurPos > 0) {
                    mCurPos--
                    setUIData()
                }
                setButtonEnable()
            }
            R.id.tv_sm_mgr_msg_next -> {
                if (mCurPos < listData?.size ?: 0) {
                    mCurPos++
                    setUIData()
                }
                setButtonEnable()
            }
            R.id.tv_sm_mgr_msg_title -> {
                val item = listData?.get(mCurPos)
                //内容类型（SP：视频 YP：音频 ZX：资讯 YB：研报）
                toArticleDetail(item?.contentUrl, item?.contentType)
            }
        }
    }

    private fun setButtonEnable() {
        if (mCurPos <= 0) {
            tv_sm_mgr_msg_pre.isEnabled = false
            tv_sm_mgr_msg_pre.setTextColor(ContextCompat.getColor(activity!!, R.color.cl_eaeaea))
            if (listData?.size == 1) {
                tv_sm_mgr_msg_next.isEnabled = false
                tv_sm_mgr_msg_next.setTextColor(ContextCompat.getColor(activity!!, R.color.cl_eaeaea))
            } else {
                tv_sm_mgr_msg_next.isEnabled = true
                tv_sm_mgr_msg_next.setTextColor(ContextCompat.getColor(activity!!, R.color.cl_666666))
            }
        } else if (mCurPos >= (listData?.size ?: 0) - 1) {
            tv_sm_mgr_msg_pre.isEnabled = true
            tv_sm_mgr_msg_pre.setTextColor(ContextCompat.getColor(activity!!, R.color.cl_666666))
            tv_sm_mgr_msg_next.isEnabled = false
            tv_sm_mgr_msg_next.setTextColor(ContextCompat.getColor(activity!!, R.color.cl_eaeaea))
        } else {
            tv_sm_mgr_msg_pre.isEnabled = true
            tv_sm_mgr_msg_pre.setTextColor(ContextCompat.getColor(activity!!, R.color.cl_666666))
            tv_sm_mgr_msg_next.isEnabled = true
            tv_sm_mgr_msg_next.setTextColor(ContextCompat.getColor(activity!!, R.color.cl_666666))
        }
    }

    private fun toArticleDetail(url: String?, type: String?) {
        when {
            TextUtils.equals("ZX", type) -> RouterHelper.launchFrag(this, WebRouterPath.PATH_FRAG_WEBVIEW,
                    NavHelper.obtainArg("资讯正文", ValConfig.IT_URL, url))
            TextUtils.equals("YB", type) -> RouterHelper.launchFrag(this, WebRouterPath.PATH_FRAG_WEBVIEW,
                    NavHelper.obtainArg("研报正文", ValConfig.IT_URL, url))
            TextUtils.equals("SP", type) -> {
                SmHeadLineLauncher.launcherToVideoPage(activity, url,  null, false)
            }
            else -> RouterHelper.launchFrag(this, WebRouterPath.PATH_FRAG_WEBVIEW,
                    NavHelper.obtainArg("资讯正文", ValConfig.IT_URL, url))
        }
    }
}