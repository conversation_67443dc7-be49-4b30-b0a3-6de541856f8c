package com.howbuy.fund.simu.personage

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import android.view.animation.AlphaAnimation
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.ViewModelProviders
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.viewpager.widget.ViewPager
import com.alibaba.android.arouter.facade.annotation.Route
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.google.android.material.appbar.AppBarLayout
import com.google.android.material.appbar.CollapsingToolbarLayout
import com.google.android.material.tabs.TabLayout
import com.google.gson.GsonUtils
import com.google.gson.reflect.TypeToken
import com.howbuy.analytics.PvReportUtils
import com.howbuy.android.analytics.annotation.pv.PvInfo
import com.howbuy.dialog.DlgHelper
import com.howbuy.fund.base.FundApp
import com.howbuy.fund.base.analytics.HbAnalytics
import com.howbuy.fund.base.aty.AtyEmpty
import com.howbuy.fund.base.config.SpConfig
import com.howbuy.fund.base.config.ValConfig
import com.howbuy.fund.base.frag.AbsHbFrag
import com.howbuy.fund.base.livedata.LiveDataBus
import com.howbuy.fund.base.nav.NavHelper
import com.howbuy.fund.base.router.RouterHelper
import com.howbuy.fund.base.router.fragpath.SmRouterPath
import com.howbuy.fund.base.storage.CommonStorageUtils
import com.howbuy.fund.base.utils.FragmentUtils
import com.howbuy.fund.base.utils.FundTextUtils
import com.howbuy.fund.base.utils.FundUtils
import com.howbuy.fund.base.utils.ImgHelper
import com.howbuy.fund.base.widget.SpacesItemDecoration
import com.howbuy.fund.net.util.HandleErrorMgr
import com.howbuy.fund.simu.CommonSmBuilder
import com.howbuy.fund.simu.R
import com.howbuy.fund.simu.SmOptHelper
import com.howbuy.fund.simu.archive.mask.NewbieGuide
import com.howbuy.fund.simu.dialog.SimuCompliance
import com.howbuy.fund.simu.entity.*
import com.howbuy.fund.simu.mine.usercalendar.FootprintUtils
import com.howbuy.fund.simu.personage.vm.CharacterDetailVM
import com.howbuy.fund.simu.personage.vm.CharacterDetailVMFactory
import com.howbuy.fund.simu.personage.vm.CharacterRepository
import com.howbuy.fund.simu.utils.LiveDataEventKey
import com.howbuy.lib.adp.AbsFragPageAdp
import com.howbuy.lib.compont.GlobalApp
import com.howbuy.lib.interfaces.IShareActionListener
import com.howbuy.lib.interfaces.IShareHelper
import com.howbuy.lib.interfaces.IShareProvider
import com.howbuy.lib.utils.*
import com.howbuy.router.provider.ICommonProvider
import com.howbuy.router.proxy.Invoker
import com.howbuy.share.entity.ShareItem
import com.howbuy.share.entity.WorkWeixinEntity
import com.howbuy.userdata.business.get
import com.howbuy.userdata.business.getHboneNo
import com.howbuy.userdata.business.isLogined
import kotlinx.android.synthetic.main.frag_character_detail.*
import kotlinx.android.synthetic.main.lay_character_relation_recommend.*
import kotlin.math.abs
import kotlin.math.ceil

/**
 * Create by zsm on 2018/12/18.
 **/
@Route(path = SmRouterPath.PATH_FRAG_CHARACTER_DETAIL)
@PvInfo(pageId = "872", level = "3", name = "其他人物专栏", className = "FragCharacterDetail")
class FragCharacterDetail : AbsHbFrag() {
    private var mPersonId: String? = null
    private lateinit var mData: PersonageDetail
    private var isNeedAttention = false//h5打开时，若当前用户未关注该人物，将该人物加关注
    private var gjTabPosition = -1
    private var gdTabPosition = -1
    private var htTabPosition = -1
    private var wdTabPosition = -1
    private var tabCount = 1
    private var mPosition: Int = 0 //当前位置
    private lateinit var viewModel: CharacterDetailVM


    override fun getFragLayoutId(): Int {
        return R.layout.frag_character_detail
    }

    override fun stepAllViews(root: View, savedInstanceState: Bundle?) {
        if (activity is AtyEmpty) {
            (activity as AtyEmpty).setToolbarVisibility(false)
        }
        StatusBarUtil.translucentStatusBar(activity, true)
        val layoutParams: CollapsingToolbarLayout.LayoutParams =
            my_toolbar.layoutParams as CollapsingToolbarLayout.LayoutParams
        layoutParams.setMargins(0, SysUtils.getStatusBarHeight(activity), 0, 0)
        my_toolbar.layoutParams = layoutParams//要增加内容视图的 paddingTop,否则内容被 ActionBar 遮盖
        my_toolbar_hide.layoutParams.height =
            (SysUtils.getStatusBarHeight(activity) + resources.getDimension(R.dimen.toolbar_height)).toInt()
        val alphaAnimation = AlphaAnimation(0.0f, 1.0f)
        alphaAnimation.duration = 600
        lay_appbar.addOnOffsetChangedListener(AppBarLayout.OnOffsetChangedListener { _, verticalOffset ->
            if (verticalOffset == 0) {
                if (my_toolbar_hide?.visibility != View.GONE) {
                    alphaAnimation.cancel()
                    my_toolbar_hide?.visibility = View.GONE
                }
            } else if (abs(verticalOffset) >= DensityUtils.dp2px(50f)) {
                if (my_toolbar_hide?.visibility != View.VISIBLE) {
                    my_toolbar_hide?.visibility = View.VISIBLE
                    my_toolbar_hide?.startAnimation(alphaAnimation)
                }
            } else {
                if (my_toolbar_hide?.visibility != View.GONE) {
                    alphaAnimation.cancel()
                    my_toolbar_hide?.visibility = View.GONE
                }
            }
        })
    }

    override fun parseArgment(arg: Bundle?) {
        mPersonId = arg?.getString(ValConfig.IT_ID, "")
        mPosition = arg?.getInt(ValConfig.IT_VALUE_1, 0) ?: 0
        isNeedAttention = StrUtils.equals(arg?.getString(ValConfig.IT_STATUS, "0"), "1")
        if (StrUtils.isEmpty(mPersonId)) {
            activity?.finish()
            return
        }

        viewModel =
            ViewModelProviders.of(this, CharacterDetailVMFactory(CharacterRepository.getInstance()))
                .get(CharacterDetailVM::class.java)

        showLoadingView()
        requestData()
        hbAnalytics()
        LoginObserverTemplate().addLoginObserver(this) { handleLoginState() }
    }

    private fun requestData() {
        viewModel.getPersonInfo(mPersonId!!, getHboneNo())
            .observe(this, { t ->
                hideLoadingView()
                if (t != null) {
                    mData = t
                    FootprintUtils.saveManagerFootprint("view", mData.personId, mData.personName)
                    initTabView()
                    renderView()
                    setFloatViewUI()
                    dealPushAttention()
                } else {
                    lay_appbar.visibility = View.GONE
                    view_pager.visibility = View.GONE
                    showEmptyView("暂无数据", ContextCompat.getDrawable(GlobalApp.getApp(), R.drawable.materials_empty))
                }
            })
    }

    private fun requestRecommendData() {
        viewModel.getRecommendData(mPersonId!!, getHboneNo())
            .observe(this, { t ->
                if (t != null) {
                    initRecommendView(t.personList)
                }
            })
    }

    private fun initTabView() {
        if (view_pager.adapter != null) return

        tabCount = 1
        val hasTrack = MathUtils.forValI(mData.gjNum, 0) > 0
        val hasTopic = MathUtils.forValI(mData.htCount, 0) > 0
        val hasQuestion = MathUtils.forValI(mData.questionCount, 0) > 0
        if (hasTrack) {
            tabCount++
            gjTabPosition = 0
            gdTabPosition = 1
            if (hasTopic) {
                htTabPosition = 2
                tabCount++
            }
        } else {
            gdTabPosition = 0
            if (hasTopic) {
                htTabPosition = 1
                tabCount++
            }
        }
        if (hasQuestion) {
            wdTabPosition = tabCount
            tabCount++
        }
        if (tabCount > 1) {
            tab_layout.tabMode = TabLayout.MODE_FIXED
        } else {
            tab_layout.tabMode = TabLayout.MODE_SCROLLABLE
        }
        val adapter = AdpFragPage(activity, childFragmentManager)
        view_pager.adapter = adapter
        tab_layout.setupWithViewPager(view_pager)
        if (mPosition < tabCount) {
            view_pager.currentItem = mPosition
        }
        view_pager.addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
            override fun onPageScrollStateChanged(p0: Int) {
            }

            override fun onPageScrolled(p0: Int, p1: Float, p2: Int) {
            }

            override fun onPageSelected(p0: Int) {
                when (p0) {
                    gjTabPosition -> {
                        HbAnalytics.onClick(activity, "77890")
                    }
                    htTabPosition -> {
                        HbAnalytics.onClick(activity, "77930")
                    }
                    wdTabPosition -> {
                        HbAnalytics.onClick(FundApp.getApp(), "333310")
                    }
                    else -> {
                        HbAnalytics.onClick(activity, "77910")
                    }
                }
            }
        })
    }

    /**
     * h5打开时，若当前用户未关注该人物，将该人物加关注
     */
    private fun dealPushAttention() {
        if (isNeedAttention && isLogined()) {
            if (mData.isFollow) {
                LogUtils.pop("您已关注该人物")
                return
            }
            val dlgHelper = DlgHelper()
            dlgHelper.showDialog(
                activity,
                DlgHelper.DlgArg(getString(R.string.loading), true, true),
                0
            )
            CommonSmBuilder.newSmPersonAttention(
                mData.personId, getHboneNo(), false, 0
            ) { result1 ->
                dlgHelper.closeDialog(activity)
                if (result1.isSuccess) {
                    mData.setIsFollow("1")
                    val attentionAmount = MathUtils.forValI(mData.followCount, 0) + 1
                    mData.followCount = attentionAmount.toString()
                    renderChangedView()
                } else {
                    val errMsg = HandleErrorMgr.handErrorMsg(result1.mErr, true)
                    LogUtils.pop(errMsg)
                }
            }
        }
    }

    @SuppressLint("SetTextI18n")
    private fun renderView() {
        tv_page_title_hide.text = mData.personName

        if (MathUtils.forValI(mData.gjNum, 0) > 0) {
            tab_layout.getTabAt(gjTabPosition)?.text = "轨迹(" + mData.gjNum + ")"
        }
        tab_layout.getTabAt(gdTabPosition)?.text = if (MathUtils.forValI(mData.viewCount, 0) > 0) {
            "观点(" + mData.viewCount + ")"
        } else {
            "观点"
        }
        if (MathUtils.forValI(mData.htCount, 0) > 0) {
            tab_layout.getTabAt(htTabPosition)?.text = "话题(" + mData.htCount + ")"
        }
        tab_layout.getTabAt(wdTabPosition)?.text =
            if (MathUtils.forValI(mData.questionCount, 0) > 0) {
                "问答(" + mData.questionCount + ")"
            } else {
                "问答"
            }

        ImgHelper.display(mData.iconUrl, iv_icon, ImgHelper.OPTIONS_AVATAR_1)
        iv_v.visibility = if (mData.isV) View.VISIBLE else View.GONE
        if (!StrUtils.isEmpty(mData.labelType)) {
            when (mData.labelType) {
                "1" -> {
                    iv_tag.visibility = View.VISIBLE
                    iv_tag.setImageResource(R.drawable.sm_lable_preson_hot3)
                }
                "2" -> {
                    iv_tag.visibility = View.VISIBLE
                    iv_tag.setImageResource(R.drawable.sm_lable_preson_hot4)
                }
                "3" -> {
                    mData.label?.let {
                        tv_tag.visibility = View.VISIBLE
                        tv_tag.text = it
                    }
                }
            }
        }
        tv_name.text = FundTextUtils.showTextEmpty(mData.personName)
        tv_position.text = FundTextUtils.showTextEmpty(mData.describeWord)
        if (StrUtils.isEmpty(mData.introduce)) {
            lay_desc.visibility = View.GONE
        } else {
            lay_desc.visibility = View.VISIBLE
            val text =
                String.format(getString(R.string.three_chinese_character_format), mData.introduce)
            if (!StrUtils.isEmpty(mData.speech)) {
                tv_speech.visibility = View.VISIBLE
                tv_speech.text = "“ ${mData.speech} ”"
                tv_desc.updateText(text, 2)
            } else {
                tv_speech.visibility = View.GONE
                tv_desc.updateText(text, 4)
            }
        }
        renderChangedView()
        lay_attention.setOnClickListener(object : NoDbClickListener() {
            override fun onNoDoubleClick(v: View) {
                val type =
                    if (mData.isFollow) SmCharacterAttentionDlg.TYPE_ATTEND else SmCharacterAttentionDlg.TYPE_ATTENTION
                val dialog =
                    SmCharacterAttentionDlg.newInstance(
                        type,
                        mData.followCount,
                        mData.personId,
                        mData.personName
                    )
                dialog.setStatusListener { onAttentionNotify(isOnlyAttention = true, isReqRecommend = true) }
                dialog.show(childFragmentManager, null)
            }
        })
        lay_support.setOnClickListener(object : NoDbClickListener() {
            override fun onNoDoubleClick(v: View) {
                val dialog = SmCharacterAttentionDlg.newInstance(
                    SmCharacterAttentionDlg.TYPE_SUPPORT,
                    mData.thumCount,
                    mData.personId,
                    mData.personName
                )
                dialog.setStatusListener {
                    val supportCount = MathUtils.forValI(mData.thumCount, 0) + 1
                    mData.thumCount = supportCount.toString()
                    val supportStr = if (supportCount > 9999) {
                        MathUtils.formatF(ceil(supportCount / 1000.0).toInt() / 10.0f, 1) + "万"
                    } else supportCount.toString()
                    tv_support_amount.text = supportStr
                }
                dialog.show(childFragmentManager, null)
            }
        })
        tv_attention.setOnClickListener(object : NoDbClickListener() {
            override fun onNoDoubleClick(v: View) {
                onAttentionClick(true)
            }
        })
        tv_attention_toolbar_hide.setOnClickListener(object : NoDbClickListener() {
            override fun onNoDoubleClick(v: View?) {
                onAttentionClick(false)
            }
        })

        //基金经理的投资表现及基金
        if (!StrUtils.isEmpty(mData.rydm)) {
            lay_manager.visibility = View.VISIBLE
            tv_manager_title.setOnClickListener(object : NoDbClickListener() {
                override fun onNoDoubleClick(v: View?) {
                    RouterHelper.launchFrag(
                        activity,
                        SmRouterPath.PATH_FRAG_SM_MANAGER_DETAILS,
                        NavHelper.obtainArg("基金经理", ValConfig.IT_ID, mData.rydm),
                        0
                    )
                }
            })
            if (!StrUtils.isEmpty(mData.jjdm)) {
                lay_fund.visibility = View.VISIBLE
                tv_flagship.visibility = if (mData.isqj()) View.VISIBLE else View.GONE
                tv_fund_name.text = mData.jjjc
                tv_fund_type.text = mData.jjfl
                tv_fund_increase_desc.text = mData.zfdesc
                if (SimuCompliance.judgetCurUserNeedHegui()) {
                    tv_fund_increase.text = "认证可见"
                    tv_fund_increase.setTextColor(ContextCompat.getColor(GlobalApp.getApp(), R.color.fd_title))
                } else {
                    FundUtils.formatRate(tv_fund_increase, mData.zfvalue)
                }
                lay_fund.setOnClickListener(object : NoDbClickListener() {
                    override fun onNoDoubleClick(v: View) {
                        var prodType: SmOptHelper.PROD_TYPE? = null
                        when {
                            StrUtils.equals(
                                SmConstantEnum.SMProductType.TYPE_FUND.code,
                                mData.cpfl
                            ) -> prodType =
                                SmOptHelper.PROD_TYPE.PROD_SM
                            StrUtils.equals(
                                SmConstantEnum.SMProductType.TYPE_STOCK.code,
                                mData.cpfl
                            ) -> prodType =
                                SmOptHelper.PROD_TYPE.PROD_STOCK
                            StrUtils.equals(
                                SmConstantEnum.SMProductType.TYPE_FIXED.code,
                                mData.cpfl
                            ) -> prodType =
                                SmOptHelper.PROD_TYPE.PROD_FIXED
                        }
                        prodType?.let {
                            SmOptHelper.launcherToSmProdDetails(activity, it, mData.jjdm, "", 0)
                        }
                    }
                })
            } else {
                lay_fund.visibility = View.GONE
            }
        } else {
            lay_manager.visibility = View.GONE
        }
    }

    private fun onAttentionClick(isReqRecommend: Boolean) {
        if (!isLogined()) {
            get(ICommonProvider::class.java).launchLogin(activity, false, null) { login ->
                if (login != null && login && !mData.isFollow) {
                    requestAttention(isReqRecommend)
                }
            }
        } else {
            requestAttention(isReqRecommend)
        }
    }

    private fun requestAttention(isReqRecommend: Boolean) {
        val dlgHelper = DlgHelper()
        dlgHelper.showDialog(activity, DlgHelper.DlgArg(getString(R.string.loading), true, true), 0)
        CommonSmBuilder.newSmPersonAttention(
            mData.personId,
            getHboneNo(),
            mData.isFollow,
            0
        ) { result ->
            dlgHelper.closeDialog(activity)
            if (result.isSuccess) {
                onAttentionNotify(false, isReqRecommend)
            } else {
                val errMsg = HandleErrorMgr.handErrorMsg(result.mErr, true)
                LogUtils.pop(errMsg)
            }
        }
    }

    private fun initRecommendView(personageItems: MutableList<PersonageItem>?) {
        if (personageItems != null && personageItems.isNotEmpty()) {
            renderRecommendView(true)

            recycler_view.layoutManager =
                LinearLayoutManager(activity, LinearLayoutManager.HORIZONTAL, false)
            if (recycler_view.itemDecorationCount <= 0) {
                recycler_view.addItemDecoration(SpacesItemDecoration(DensityUtils.dp2px(10f), 0))
            }
            recycler_view.adapter = object :
                BaseQuickAdapter<PersonageItem, BaseViewHolder>(
                    R.layout.lay_character_relation_recommend_item,
                    personageItems
                ) {
                override fun convert(holder: BaseViewHolder, item: PersonageItem) {
                    val layout = holder.getView<LinearLayout>(R.id.lay_root)
                    val imageView = holder.getView<ImageView>(R.id.iv_icon)
                    val tvName = holder.getView<TextView>(R.id.tv_name)
                    val tvAttention = holder.getView<TextView>(R.id.tv_attention)
                    ImgHelper.display(item.iconUrl, imageView, ImgHelper.OPTIONS_AVATAR_1)
                    tvName.text = FundTextUtils.showTextEmpty(item.name)
                    tvAttention.text =
                        getString(if (item.isFollow) R.string.attended else R.string.attention_add)
                    tvAttention.setTextColor(ContextCompat.getColor(GlobalApp.getApp(), if (item.isFollow) R.color.fd_text_subtitle else R.color.white))
                    tvAttention.setBackgroundResource(
                        if (item.isFollow) R.drawable.bg_character_attended
                        else R.drawable.bg_character_attention_add
                    )
                    tvAttention.setOnClickListener(object : NoDbClickListener() {
                        @SuppressLint("NotifyDataSetChanged")
                        override fun onNoDoubleClick(v: View) {
                            if (!isLogined()) {
                                get(ICommonProvider::class.java).launchLogin(this@FragCharacterDetail, false, null, null)
                            } else {
                                val dlgHelper = DlgHelper()
                                dlgHelper.showDialog(
                                    activity,
                                    DlgHelper.DlgArg(getString(R.string.loading), true, true),
                                    0
                                )
                                CommonSmBuilder.newSmPersonAttention(
                                    item.personId, getHboneNo(), item.isFollow, 0
                                ) { result ->
                                    dlgHelper.closeDialog(activity)
                                    if (result.isSuccess) {
                                        HbAnalytics.onClick(
                                            activity,
                                            if (item.isFollow) "90090"
                                            else "90080"
                                        )
                                        item.setIsFollow(if (item.isFollow) "0" else "1")
                                        val index = personageItems.indexOf(item)
                                        if (index >= 0 && index < personageItems.size) {
                                            notifyItemChanged(personageItems.indexOf(item))
                                        } else {
                                            notifyDataSetChanged()
                                        }
                                        PersonageHelper.putAttentionState(
                                            item.personId,
                                            item.isFollow
                                        )
                                        LiveDataBus.get()
                                            .with(LiveDataEventKey.BUS_KEY_SIM_CHARACTER_ATTENTION)
                                            .postValue(item)
                                    } else {
                                        val errMsg = HandleErrorMgr.handErrorMsg(result.mErr, true)
                                        LogUtils.pop(errMsg)
                                    }
                                }
                            }
                        }
                    })
                    layout.setOnClickListener(object : NoDbClickListener() {
                        override fun onNoDoubleClick(v: View) {
                            PersonageHelper.launcherCharacterDetail(
                                this@FragCharacterDetail,
                                item.personId
                            )
                        }
                    })
                }
            }
        } else {
            renderRecommendView(false)
        }
    }

    private fun renderRecommendView(isExpand: Boolean) {
        iv_recom_close.visibility = View.VISIBLE
        lay_character_relation_recommend.visibility = if (isExpand) {
            View.VISIBLE
        } else {
            View.GONE
        }
    }

    private fun renderChangedView() {
        val followCount = MathUtils.forValI(mData.followCount, 0)
        val followStr = if (followCount > 9999) {
            MathUtils.formatF(ceil(followCount / 1000.0).toInt() / 10.0f, 1) + "万"
        } else followCount.toString()
        tv_attention_amount.text = followStr
        val supportCount = MathUtils.forValI(mData.thumCount, 0)
        val supportStr = if (supportCount > 9999) {
            MathUtils.formatF(ceil(supportCount / 1000.0).toInt() / 10.0f, 1) + "万"
        } else supportCount.toString()
        tv_support_amount.text = supportStr
        tv_attention.text = getString(if (mData.isFollow) R.string.attended else R.string.attention_add)
        tv_attention_toolbar_hide.text = getString(if (mData.isFollow) R.string.attended else R.string.attention_add)
        tv_attention.setTextColor(ContextCompat.getColor(GlobalApp.getApp(), if (mData.isFollow) R.color.fd_label else R.color.white))
        tv_attention_toolbar_hide.setTextColor(ContextCompat.getColor(GlobalApp.getApp(), if (mData.isFollow) R.color.white else R.color.fd_label))
        tv_attention.setBackgroundResource(if (mData.isFollow) R.drawable.bg_rectangle_white_corner_15dp else R.drawable.btn_discuss_bg)
        tv_attention_toolbar_hide.setBackgroundResource(if (mData.isFollow) R.drawable.transparent_drawable else R.drawable.bg_rectangle_white_corner_15dp)
    }

    private fun onAttentionNotify(isOnlyAttention: Boolean, isReqRecommend: Boolean) {
        if (mData.isFollow && isOnlyAttention) return

        HbAnalytics.onClick(
            activity,
            if (mData.isFollow) "90090" else "90080"
        )
        mData.setIsFollow(if (mData.isFollow) "0" else "1")
        var attentionAmount = MathUtils.forValI(mData.followCount, 0)
        if (mData.isFollow) {
            attentionAmount++
            if (isReqRecommend) {
                requestRecommendData()
            }
        } else {
            attentionAmount--
        }
        mData.followCount = attentionAmount.toString()
        PersonageHelper.putAttentionState(mData.personId, mData.isFollow)
        PersonageHelper.putAttentionCount(mData.personId, attentionAmount.toString())
        renderChangedView()

        val personageItem = PersonageItem(
            mData.personId,
            mData.personName,
            mData.followCount,
            mData.isFollow,
            mData.iconUrl,
            mData.describeWord,
            mData.viewCount,
            mData.isV
        )
        LiveDataBus.get().with(LiveDataEventKey.BUS_KEY_SIM_CHARACTER_ATTENTION)
            .postValue(personageItem)
    }

    override fun shouldEnableLocalBroadcast(): Boolean {
        return true
    }

    private fun handleLoginState() {
        //登录用户刷新数据
        requestData()
    }

    override fun isAutoAnalytics(): Boolean {
        return false
    }

    private fun hbAnalytics() {
        PvReportUtils.reportPvIfPvInfoExists(javaClass, activity, mPersonId, null, null)
    }

    private fun setFloatViewUI() {
        if (!isLogined() || mData.isFollow) return
        val hasShowFloatView =
            CommonStorageUtils.getBoolean(
                SpConfig.SF_SM_CHARACTER_DETAIL_TIPS + getHboneNo(),
                false
            )
        if (hasShowFloatView) return

        val info = CommonStorageUtils.getString(
            SpConfig.SF_SM_CHARACTER_DETAIL_TIPS_INFO + getHboneNo(),
            ""
        )
        var tipsList: MutableList<CharacterDetailTips>? =
            GsonUtils.toObj<MutableList<CharacterDetailTips>>(info, object : TypeToken<List<CharacterDetailTips>>() {}.type)
        var isShowTips = false
        var isTipsListContain = false
        var count = 1
        val currentTime = System.currentTimeMillis()
        if (tipsList != null && tipsList.size > 0) {
            for (tips in tipsList) {
                if (StrUtils.equals(tips.id, mPersonId)) {
                    isTipsListContain = true
                    for (time in tips.times) {
                        if (DateUtils.getDaySub(time, currentTime) <= 7) {
                            count++
                            if (count > 2) {
                                isShowTips = true
                            }
                        }
                    }
                    if (isShowTips) break
                    tips.addTime(currentTime)
                }
            }
        }
        if (!isTipsListContain) {
            if (tipsList == null) tipsList = ArrayList()
            tipsList.add(CharacterDetailTips(mPersonId, currentTime))
        }
        if (isShowTips) {
            NewbieGuide.with(this)
                .setLabel(SpConfig.SF_SM_CHARACTER_DETAIL_TIPS + getHboneNo())
                .setLayoutRes(R.layout.frag_character_detail_tips).show()
            CommonStorageUtils.remove(SpConfig.SF_SM_CHARACTER_DETAIL_TIPS_INFO + getHboneNo())
        } else {
            CommonStorageUtils.putString(SpConfig.SF_SM_CHARACTER_DETAIL_TIPS_INFO + getHboneNo(),
                    GsonUtils.toJson(tipsList))
        }
    }

    inner class AdpFragPage internal constructor(
        val mContext: FragmentActivity?,
        fm: FragmentManager
    ) :
        AbsFragPageAdp(fm) {

        override fun getItem(position: Int): Fragment {
            val bundle = Bundle()
            bundle.putString(ValConfig.IT_ID, mPersonId)
            return when (position) {
                gjTabPosition -> {
                    FragmentUtils.instantiate(
                        mContext!!,
                        mFragmentManager,
                        FragCharacterDetailTrackList::class.java.name,
                        bundle
                    )
                }
                gdTabPosition -> {
                    FragmentUtils.instantiate(
                        mContext!!,
                        mFragmentManager,
                        FragCharacterDetailVpList::class.java.name,
                        bundle
                    )
                }
                htTabPosition -> {
                    FragmentUtils.instantiate(
                        mContext!!,
                        mFragmentManager,
                        FragCharacterDetailTopicList::class.java.name,
                        bundle
                    )
                }
                wdTabPosition -> {
                    FragmentUtils.instantiate(
                        mContext!!,
                        mFragmentManager,
                        FragCharacterDetailQuestionList::class.java.name,
                        bundle
                    )
                }
                else -> {
                    Fragment()
                }
            }
        }

        override fun getCount(): Int {
            return tabCount
        }

        override fun getTag(position: Int): String {
            return position.toString()
        }
    }

    override fun onXmlBtClick(v: View?): Boolean {
        when (v?.id) {
            R.id.iv_back, R.id.iv_back_hide -> activity?.finish()
            R.id.iv_share, R.id.iv_share_hide -> {
                if (this::mData.isInitialized) {
                    val shareEntity = WorkWeixinEntity(
                        mData.personName,
                        if (StrUtils.isEmpty(mData.speech)) mData.introduce else mData.speech,
                        mData.shareUrl,
                        mData.iconUrl
                    )
                    shareEntity.addScreenShotChannel(true)
                    Invoker.getInstance().navigation(IShareProvider::class.java).showShareDialog(
                        activity,
                        shareEntity,
                        object : IShareActionListener {
                            override fun onSuccess(platformType: Int, shareItem: ShareItem?) {
                                if (platformType == IShareHelper.SHARE_TYPE_SCREENSHOT) {
                                    PersonShotScreenHelper.shotPersonDetails(
                                        this@FragCharacterDetail,
                                        false,
                                        mData.iconUrl,
                                        mData.personName,
                                        mData.describeWord,
                                        mData.speech,
                                        mData.introduce,
                                        mData.shareUrl
                                    )
                                    HbAnalytics.onClick(GlobalApp.getApp(), "90070")
                                }
                            }

                            override fun onError(platformType: Int) {

                            }

                            override fun onCancel(platformType: Int) {

                            }
                        },
                        "私募人物详情"
                    )
                }
                HbAnalytics.onClick(GlobalApp.getApp(), "40010")
            }
            R.id.iv_recom_close -> renderRecommendView(false)
        }
        return super.onXmlBtClick(v)
    }

}