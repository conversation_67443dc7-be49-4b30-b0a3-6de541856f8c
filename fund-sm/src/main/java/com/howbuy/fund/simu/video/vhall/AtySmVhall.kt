package com.howbuy.fund.simu.video.vhall

import android.content.Context
import android.graphics.Rect
import android.os.Bundle
import android.text.TextUtils
import android.view.MotionEvent
import android.view.View
import android.view.Window
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.ImageView
import androidx.fragment.app.FragmentTransaction
import com.alibaba.android.arouter.facade.annotation.Route
import com.howbuy.fund.base.analytics.HbAnalytics
import com.howbuy.fund.base.arch.ViewModelHelper
import com.howbuy.fund.base.aty.AtyEmpty
import com.howbuy.fund.base.config.ApkConfig
import com.howbuy.fund.base.config.SpConfig
import com.howbuy.fund.base.config.ValConfig
import com.howbuy.fund.base.frag.AbsFragMger.FragOpt
import com.howbuy.fund.base.livedata.LiveDataBus
import com.howbuy.fund.base.nav.NavHelper
import com.howbuy.fund.base.router.fragpath.SmRouterPath
import com.howbuy.fund.base.storage.CommonStorageUtils
import com.howbuy.fund.base.utils.ImgHelper
import com.howbuy.fund.base.utils.SmValConfig
import com.howbuy.fund.common.floatwindow.FloatWindow
import com.howbuy.fund.common.floatwindow.ITag
import com.howbuy.fund.simu.CommonSmBuilder
import com.howbuy.fund.simu.R
import com.howbuy.fund.simu.dialog.SimuCompliance
import com.howbuy.fund.simu.entity.SmLivePosterBody
import com.howbuy.fund.simu.entity.SmShareSwitchBody
import com.howbuy.fund.simu.mine.usercalendar.FootprintUtils
import com.howbuy.fund.simu.utils.LiveDataEventKey
import com.howbuy.fund.simu.video.FragSmLivingVideo
import com.howbuy.fund.simu.video.vhall.video.FragSmVhallVideo
import com.howbuy.fund.simu.video.vhall.video.VhallVideoPlayMgr
import com.howbuy.fund.simu.video.vhall.video.vm.SmVhallPreviewData
import com.howbuy.fund.simu.video.vhall.video.vm.VmSmVhallPreviewData
import com.howbuy.lib.compont.GlobalApp
import com.howbuy.lib.interfaces.IShareActionListener
import com.howbuy.lib.interfaces.IShareHelper
import com.howbuy.lib.interfaces.IShareProvider
import com.howbuy.lib.utils.LogUtils
import com.howbuy.router.provider.ICommonProvider
import com.howbuy.router.proxy.Invoker
import com.howbuy.share.entity.FloatSwitchEntity
import com.howbuy.share.entity.H5ShareEntity
import com.howbuy.share.entity.ShareItem
import com.howbuy.share.entity.WorkWeixinEntity
import com.howbuy.userdata.business.apiConsInfo
import com.howbuy.userdata.business.get
import com.howbuy.userdata.business.getHboneNo
import com.howbuy.userdata.business.isLogined
import com.vhall.business.VhallSDK
import com.vhall.business.data.UserInfo
import com.vhall.business.data.source.UserInfoDataSource
import html5.FragShareWebView


/**
 * @Description vhall直播页面（预告、直播、降级H5）
 * <AUTHOR>
 * @Date 2023/2/6
 * @Version v806
 * @param @link ValConfig.IT_ID 直播id（好买）
 *              ValConfig.IT_ENTITY 直播对象
 */
@Route(path = SmRouterPath.PATH_ATY_SM_VHALL)
class AtySmVhall : AtyEmpty() {

    private lateinit var fullLoading: View
    private lateinit var fullLoadingImg: ImageView
    lateinit var mVm: VmSmVhallPreviewData
    private var liveId: String? = ""
    private var liveData: SmVhallPreviewData? = null

    //投顾用户的名片数据
    private val mSwitchList: MutableList<FloatSwitchEntity> = mutableListOf()

    //海报分享数据
    private var mPosterInfoBody: SmLivePosterBody? = null
    private var mDetailsoBody: SmVhallPreviewData? = null

    /**
     * 前置页面接口是否已 请求返回
     */
    private var reqDetailsFinished = false

    /**
     * 海报接口是否请求已返回
     */
    private var reqPosterFinished = false

    override fun getLayout() = R.layout.aty_sm_vhall

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        initVHSDK()
        liveId = intent.extras?.getString(ValConfig.IT_ID)
        liveData = intent.extras?.getParcelable(ValConfig.IT_ENTITY)
        if (TextUtils.isEmpty(liveId)) {
            LogUtils.d("直播间信息为空")
            finish()
            return
        }
        //如果播放的直播视频与小窗的不一样,就关闭小窗
        fullLoading = findViewById(R.id.lay_sm_full_loading)
        fullLoadingImg = findViewById(R.id.iv_full_screen_loading)
        mVm = ViewModelHelper.createViewModel(this, VmSmVhallPreviewData::class.java)
        mVm.showFullLoading.observe(this) {
            it?.let {
                if (it) {
                    showLoading()
                } else {
                    hideLoading()
                }
            }
        }
        if (liveData == null) {
            mVm.fetchData(liveId)
        } else {
            //小窗跳转过来,一定是已登录状态，因为退出登录会关闭小窗
            mVm.initWebinarInfo(liveData!!)
        }
        //默认要请求分享和投顾名片数据
        reqShareInfo()
        mVm.mPreviewData.observe(this) {
            reqDetailsFinished = true
            mDetailsoBody = it
            if (it == null) {
                toError()
            } else {
                if (TextUtils.equals(it.downgradeType, "1") || TextUtils.equals(it.downgradeType, "2")) {
                    //降级
                    toDowngrade(it)
                } else {
                    //直播状态（0：直播预告 1：直播中 2：精彩回放）
                    when (it.liveStatus) {
                        "0" -> toPreview(it)
                        "1", "2" -> toDetail(it)
                        else -> toError()
                    }
                }
            }
        }

        mVm.mChangedStatus.observe(this) {
            //这里只需要调用主接口,不用刷新分享和海报,如果不满足条件,逻辑在数据监听回调中都有了
            mVm.fetchData(liveId)
        }

        //合规完成后刷新接口
        LiveDataBus.get().with(LiveDataEventKey.BUS_KEY_SM_HEGUI_DONE).observeBus(this) {
            if (it == true) {
                reqDetailsFinished = false
                reqPosterFinished = false
                mVm.fetchData(liveId)
                //已登录,需要刷新刷新分享和海报数据
                reqShareInfo()
            }
        }
    }

//    private fun initVhallSDK() {
//        //RSA 校验 必须设置否则校验不通过 从控制台 获取
//        if (!VhallSDK.isInit()) {
//            VhallSDK.setLogEnable(true)
//            VhallSDK.setRsaPrivateKey("RSA 私钥")
//            VhallSDK.init(GlobalApp.getApp(), "678c0482b4bca0382ccba4a71244e5d3", "d1e8da33721f94f3a75cf17b971473dc")
//        }
//    }

    /**
     * 初始化vhall sdk
     */
    private fun initVHSDK() {
        //只做开发调试包(.debug)和其它变种的包(生产包)分开(主要是产线包名,如果不是产线包名,该功能无法正常使用)
        if (TextUtils.equals("debug", ApkConfig.getBuildType())) {
            //掌机测试包名对应的key
            if (!VhallSDK.isInit() || !TextUtils.equals(
                    SmValConfig.VHALL_PLAYER_APP_KEY_DEBUG,
                    VhallSDK.APP_KEY
                )
            ) {
                VhallSDK.setLogEnable(true)
                VhallSDK.init(
                    GlobalApp.getApp(),
                    SmValConfig.VHALL_PLAYER_APP_KEY_DEBUG,
                    SmValConfig.VHALL_PLAYER_APP_SECRET_KEY_DEBUG
                )
            }
        } else {
            //掌机产线包名对应的key
            if (!VhallSDK.isInit() || !TextUtils.equals(
                    SmValConfig.VHALL_PLAYER_APP_KEY_RELEASE,
                    VhallSDK.APP_KEY
                )
            ) {
                VhallSDK.setLogEnable(false)
                VhallSDK.init(
                    GlobalApp.getApp(),
                    SmValConfig.VHALL_PLAYER_APP_KEY_RELEASE,
                    SmValConfig.VHALL_PLAYER_APP_SECRET_KEY_RELEASE
                )
            }
        }
    }

    /**
     * 显示错误页面
     */
    private fun toError() {
        val opt = FragOpt(
            FragSmVhallError::class.java.name,
            NavHelper.obtainArg("直播"),
            FragOpt.FRAG_ALLOW_LOSS_STATE
        )
        opt.setAnimSystem(FragmentTransaction.TRANSIT_UNSET)
        switchToFrag(opt)
        hideLoading()
    }

    /**
     * 显示预告
     */
    private fun toPreview(data: SmVhallPreviewData) {
        uploadFP(data)
        val opt = FragOpt(
            FragSmVhallVideoHerald::class.java.name,
            NavHelper.obtainArg(data.title, ValConfig.IT_ENTITY, data),
            FragOpt.FRAG_ALLOW_LOSS_STATE
        )
        opt.setAnimSystem(FragmentTransaction.TRANSIT_UNSET)
        switchToFrag(opt)
        hideLoading()
    }

    /**
     * 降级时,需要关闭小窗播放,释放播放器,使用H5播放
     */
    private fun toDowngrade(data: SmVhallPreviewData) {
        FloatWindow.destroyPlayer(ITag.SM_VHALL_VIDEO_FLOATWINDOW_TAG)
        FloatWindow.destroy(ITag.SM_VHALL_VIDEO_FLOATWINDOW_TAG)
        if (TextUtils.equals(data.downgradeType, "1")) {
            val opt = FragOpt(
                FragSmLivingVideo::class.java.name, NavHelper.obtainArg(
                    data.title,
                    ValConfig.IT_URL, data.downgradeUrl,
                    ValConfig.IT_FROM, true,
                    ValConfig.IT_ID, data.liveId
                ), FragOpt.FRAG_ALLOW_LOSS_STATE
            )
            opt.setAnimSystem(FragmentTransaction.TRANSIT_UNSET)
            switchToFrag(opt)
            hideLoading()
        } else if (TextUtils.equals(data.downgradeType, "2")) {
            launchforceShareH5Page()
        }
    }

    private fun launchforceShareH5Page() {
        if (TextUtils.equals(mDetailsoBody?.downgradeType, "2")) {
            if (reqDetailsFinished && reqPosterFinished) {
                val bundle = NavHelper.obtainArg(
                    mDetailsoBody?.title,
                    ValConfig.IT_URL, mDetailsoBody?.downgradeUrl,
                    ValConfig.IT_VALUE_1, mPosterInfoBody?.title,
                    ValConfig.IT_VALUE_2, mPosterInfoBody?.shareDesc,
                    ValConfig.IT_VALUE_3, mPosterInfoBody?.shareUrl,
                    ValConfig.IT_VALUE_4, mPosterInfoBody?.shareImg
                )
                val opt = FragOpt(
                    FragShareWebView::class.java.name,
                    bundle,
                    FragOpt.FRAG_ALLOW_LOSS_STATE
                )
                opt.setAnimSystem(FragmentTransaction.TRANSIT_UNSET)
                switchToFrag(opt)
                hideLoading()
            }
        }
    }

    /**
     * 1.需合规
     * 2.无需合规，但是需登录
     */
    private fun toDetail(data: SmVhallPreviewData) {
        if (TextUtils.equals("1", data.hegui)) {
            //需合规
            if (SimuCompliance.judgetCurUserNeedHegui()) {
                SimuCompliance(this).showDirectly()
            } else {
                showLiveOrVideo(data)
            }
        } else {
            //无需合规，但是需登录
            if (isLogined()) {
                showLiveOrVideo(data)
            } else {
                get<ICommonProvider>(ICommonProvider::class.java).launchLogin(
                    this,
                    false,
                    null
                ) { t ->
                    val loginSuccess = t != null && t
                    if (loginSuccess && isLogined()) {
                        reqDetailsFinished = false
                        reqPosterFinished = false
                        mVm.fetchData(liveId)
                        reqShareInfo()
                    } else {
                        finish()
                    }
                }
            }
        }
    }

    /**
     * 显示直播或者回看
     */
    private fun showLiveOrVideo(data: SmVhallPreviewData) {
        uploadFP(data)
        if (VhallSDK.isLogin()) {
            //已登录
            toLiveOrVideo(data)
        } else {
            //未登录
            if (TextUtils.isEmpty(data.vhallThirdId)) {
                //没有Vhall三方id，异常
                toError()
            } else {
                VhallSDK.loginByThirdId(
                    data.vhallThirdId,
                    "",
                    "",
                    object : UserInfoDataSource.UserInfoCallback {
                        override fun onError(p0: Int, p1: String?) {
                            toError()
                        }

                        override fun onSuccess(p0: UserInfo?) {
                            CommonStorageUtils.putString(
                                SpConfig.SP_KEY_SM_VHALL_USER_ID,
                                p0?.user_id
                            )
                            toLiveOrVideo(data)
                        }
                    })
            }
        }
    }

    /**
     * 显示直播或回看
     */
    private fun toLiveOrVideo(data: SmVhallPreviewData) {
        //进入直播详情页面时, 当前播放的视频与之前小窗的不一样,就销毁, 或者 当前的id相同,但直播状态发生了变化
        if (!TextUtils.equals(liveId, VhallVideoPlayMgr.mLiveData?.liveId)) {
            FloatWindow.destroy(ITag.SM_VHALL_VIDEO_FLOATWINDOW_TAG)
            VhallVideoPlayMgr.getWatchLive()?.destroy()
            VhallVideoPlayMgr.getWatchPlayback()?.destroy()
            VhallVideoPlayMgr.isShowing = false
            VhallVideoPlayMgr.releaseAll()
            //进入直播详情页面, 把其它所有的小窗都关掉
            FloatWindow.removeOtherWindow(ITag.SM_VHALL_VIDEO_FLOATWINDOW_TAG)
        }
        //不降级走本地
        val opt = FragOpt(
            FragSmVhallVideo::class.java.name,
            NavHelper.obtainArg("直播", FragSmVhallVideo.INTENT_LIVE_DATA, data),
            FragOpt.FRAG_CACHE
        )
        opt.setAnimSystem(FragmentTransaction.TRANSIT_UNSET)
        switchToFrag(opt)
        hideLoading()
    }

    fun reload() {
        showLoading()
        reqDetailsFinished = false
        reqPosterFinished = false
        mVm.fetchData(liveId)
        reqShareInfo()
    }

    fun showLoading() {
        fullLoading.visibility = View.VISIBLE
        ImgHelper.loadAnimatedWebP(fullLoadingImg, R.drawable.sm_full_screen_loading)
    }

    fun hideLoading() {
        fullLoading.visibility = View.GONE
    }

    fun reqShareInfo() {
        reqTgCardInfo()
        reqPostShareCardInfo()
    }

    /**
     * 未登录不请求投顾名片
     * 请求投顾名片数据
     */
    private fun reqTgCardInfo() {
        if (TextUtils.isEmpty(apiConsInfo().getMobile())) {
            return
        }
        CommonSmBuilder.reqSmShareTgSwitch(apiConsInfo().getMobile(), 0) {
            if (it.isSuccess && it.mData != null) {
                val switchBody = it.mData as SmShareSwitchBody
                if (switchBody.switchs?.isNotEmpty() == true) {
                    val tgCardInfo = switchBody.switchs
                    mSwitchList.clear()
                    mSwitchList.addAll(tgCardInfo)
                }
            } else {
                mSwitchList.clear()
            }
        }
    }

    /**
     * 不管是否登录都要请求分享数据,但登录后,同样需要刷新登录状态的分享数据
     * 请求投海报数据,登录后,需要刷新数据
     */
    private fun reqPostShareCardInfo() {
        CommonSmBuilder.reqSmLivingPosterInfo(getHboneNo(), liveId, 1) {
            if (it.isSuccess && it.mData != null) {
                mPosterInfoBody = it.mData as SmLivePosterBody
            } else {
                mPosterInfoBody = null
            }
            reqPosterFinished = true
            launchforceShareH5Page()
        }
    }

    /**
     * 足迹上报
     */
    private fun uploadFP(data: SmVhallPreviewData) {
        if (!TextUtils.isEmpty(data.h5LinkUrl)) {
            FootprintUtils.saveLiveDetail(data.h5LinkUrl, data.title)
        }
    }

    override fun dispatchTouchEvent(event: MotionEvent): Boolean {
        if (event.action == MotionEvent.ACTION_DOWN) {
            val v = currentFocus
            if (v is EditText) {
                v.postDelayed({
                    val outRect = Rect()
                    v.getGlobalVisibleRect(outRect)
                    if (!outRect.contains(event.rawX.toInt(), event.rawY.toInt())) {
                        v.clearFocus()
                        val imm: InputMethodManager =
                            getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                        imm.hideSoftInputFromWindow(v.getWindowToken(), 0)
                    }
                }, 100)
            }
        }
        return super.dispatchTouchEvent(event)
    }

    fun share(action: IShareScreen?) {
        //当接口报错，或分享字段部分字段无值（分享海报无值），导致图片或其他分享渠道无法分享的情况；针对此场景，点击页面右上角分享按钮，页面toast“服务器或配置异常，请稍后再试”
        if (mPosterInfoBody == null || TextUtils.isEmpty(mPosterInfoBody?.mimgbig)) {
            LogUtils.pop("服务器或配置异常，请稍后再试")
            return
        }
        val shareEntity =
            WorkWeixinEntity(
                mPosterInfoBody?.title, mPosterInfoBody?.shareDesc,
                mPosterInfoBody?.shareUrl, mPosterInfoBody?.shareImg
            )
        shareEntity.addScreenShotChannel(true)
        //组装名片数据列表
        val entity = H5ShareEntity()
        entity.switchLists = mSwitchList
        Invoker.getInstance().navigation(IShareProvider::class.java).showShareWithSwitchDialog(
            this,
            shareEntity,
            entity,
            null,
            object : IShareActionListener {

                override fun onSuccess(platformType: Int, shareItem: ShareItem?) {
                    if (platformType == IShareHelper.SHARE_TYPE_SCREENSHOT) {
                        liveId?.let {
                            action?.action(mPosterInfoBody)
                        }
                    }
                }

                override fun onError(platformType: Int) {
                }

                override fun onCancel(platformType: Int) {
                }
            },
            null
        )
        HbAnalytics.onClick(GlobalApp.getApp(), SmValConfig.CLICK_SHARE)
    }

    public interface IShareScreen {
        fun action(posterBody: SmLivePosterBody?)
    }
}

fun Window.toggleNavigationBar() {
    val uiOptions = this.decorView.systemUiVisibility
    var newUiOptions = uiOptions
    newUiOptions = newUiOptions xor View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
    newUiOptions = newUiOptions xor View.SYSTEM_UI_FLAG_FULLSCREEN
    newUiOptions = newUiOptions xor View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
    this.decorView.systemUiVisibility = newUiOptions
}

fun Window.showNavigationBar() {
    val uiOptions = this.decorView.systemUiVisibility
    var newUiOptions = uiOptions
    val isImmersiveModeEnabled = uiOptions or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY == uiOptions
    if (isImmersiveModeEnabled) {
        newUiOptions = newUiOptions and View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
        newUiOptions = newUiOptions and View.SYSTEM_UI_FLAG_FULLSCREEN
        newUiOptions = newUiOptions and View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
        this.decorView.systemUiVisibility = newUiOptions
    }
}

fun Window.hideNavigationBar() {
    val uiOptions = this.decorView.systemUiVisibility
    var newUiOptions = uiOptions
    val isImmersiveModeEnabled = uiOptions or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY == uiOptions
    if (!isImmersiveModeEnabled) {
        newUiOptions = newUiOptions or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
        newUiOptions = newUiOptions or View.SYSTEM_UI_FLAG_FULLSCREEN
        newUiOptions = newUiOptions or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
        this.decorView.systemUiVisibility = newUiOptions
    }
}