package com.howbuy.fund.simu.combination

import android.graphics.Paint
import android.os.Bundle
import android.view.View
import androidx.core.content.ContextCompat
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.github.mikephil.charting.formatter.ValueFormatter
import com.github.mikephil.charting.interfaces.datasets.ILineDataSet
import com.github.mikephil.charting.utils.Utils
import com.howbuy.fund.base.config.ValConfig
import com.howbuy.fund.base.livedata.LiveDataBus
import com.howbuy.fund.simu.utils.LiveDataEventKey
import com.howbuy.fund.chart.mpchart.DefaultPercentValueFormat
import com.howbuy.fund.chart.mpchart.HbChartAttrHelper
import com.howbuy.fund.chart.mpchart.line.IHbFundLineChartListener
import com.howbuy.fund.simu.R
import com.howbuy.fund.simu.entity.CombineChartData
import com.howbuy.fund.simu.utils.SmChartHelper
import com.howbuy.lib.compont.GlobalApp
import com.howbuy.lib.utils.LogUtils
import com.howbuy.lib.utils.MathUtils
import com.howbuy.lib.utils.StrUtils
import kotlinx.android.synthetic.main.frag_combine_retrace_chart.*

open class FragCombineRetraceChart : FragCombineChartBase() {
    private lateinit var mXAxis: XAxis
    private lateinit var mYAxis: YAxis

    /**
     * lateinit var 使用的时候,需要判断是否初始化了,否则容易引起异常
     *
     * catch exception:kotlin.UninitializedPropertyAccessException: lateinit property mFundEntryList has not been initialized
     */
    private var dateIndexs: MutableList<String> = mutableListOf() //X 轴日期集合
    private var mFundEntryList: MutableList<Entry> = mutableListOf() //本基金绘图数据
    private var mOther1EntryList: MutableList<Entry> = mutableListOf() //指数绘图数据
    private var mOther2EntryList: MutableList<Entry> = mutableListOf() //指数绘图数据
    private var mDataList: List<CombineChartData.Item> = mutableListOf()

    override fun getFragLayoutId(): Int {
        return R.layout.frag_combine_retrace_chart
    }

    override fun stepAllViews(root: View?, savedInstanceState: Bundle?) {
        initLineChart()
        LiveDataBus.get().with(LiveDataEventKey.BUS_KEY_SM_GROUP_CHART_INCOME_UI, Int::class.java)
            .observeBus(this, {
                if (it != null) {
                    (parentFragment as FragCombineChartContainer).resetRiskTitleUI()
                    drawChart(false, it)
                }
            })
    }

    override fun parseArgment(arg: Bundle?) {
        LiveDataBus.get()
            .with(LiveDataEventKey.BUS_KEY_SIMU_COMBINE_CLEAR_HIGHLIGHT, Boolean::class.java)
            .observeBus(this, {
                hbline_chart.cleanHighlight()
            })
    }

    override fun fetchData() {
        showLoadingView()
    }

    fun setData(t: CombineChartData?) {
        hideLoadingView()
        if (t != null) {
            if (t.dataList != null && t.dataList.size > 0) {
                setLineChartData(t.dataList)
            } else {
                showEmptyChart()
            }
        } else {
            showEmptyChart()
        }
    }

    private fun initLineChart() {
        mXAxis = hbline_chart.xAxis
        mYAxis = hbline_chart.axisLeft

        HbChartAttrHelper.setHbLineChartBasicAttr(hbline_chart)
        hbline_chart.isHighLightLineDash = false
        hbline_chart.isNeedHighLightDot = true
        hbline_chart.highLightDotColor = SmChartHelper.SM_LINE_COLOR[0]
        hbline_chart.highLightDotRadius = 7f
        hbline_chart.highLightHoleDotColor = -0x1
        hbline_chart.highLightHoleDotRadius = 8f
        hbline_chart.axisRight.setDrawZeroLine(false)
        hbline_chart.axisRight.setDrawAxisLine(false)
        hbline_chart.axisRight.setDrawGridLines(false)
        hbline_chart.axisRight.isEnabled = false

        mYAxis.gridLineWidth = 0.5f
        mYAxis.setDrawGridLines(true)
        mYAxis.gridColor = -0x151516
        mYAxis.yOffset = 0f
        mYAxis.axisMaximum = 0f

        mXAxis.setLabelCount(2, true)
        mXAxis.textColor = -0x444235
        mXAxis.setDrawGridLines(false)
        mXAxis.valueFormatter = object : ValueFormatter() {
            override fun getFormattedValue(value: Float): String {
                return if (value == 0f) {
                    dateIndexs[0]
                } else if (value == (dateIndexs.size - 1).toFloat()) {
                    dateIndexs[dateIndexs.size - 1]
                } else {
                    ""
                }
            }
        }

        hbline_chart.setTouchImmediate(true)
        hbline_chart.setCustomFingerTouchEnable(true, false)
        hbline_chart.fingerTouchListener = object : IHbFundLineChartListener<String> {
            override fun onChartViewFocus(focus: Boolean) {
                //                setScrollEnable(!focus)
            }

            override fun onFitSingleTouchIndex(index: Int, xPos: Int) {
                LogUtils.d("FragBrokerBaseChart", index.toString())
                //                setScrollEnable(false)
                onChartClick(xPos, true)
            }

            override fun onFitDoubleTouchIndexs(preIndex: Int, sufIndex: Int) {
            }
        }
    }

    protected fun showEmptyChart() {
        val p = Paint(1)
        p.color = -0x111112
        p.textAlign = Paint.Align.CENTER
        p.textSize = Utils.convertDpToPixel(60f)
        hbline_chart.setPaint(p, 7)
        hbline_chart.setNoDataText("暂无数据")
        hbline_chart.invalidate()
    }

    /**
     * 图表点击时，时间旗帜及头部变化
     */
    private fun onChartClick(xPos: Int, isClick: Boolean) {
        try {
            var clickDate: String? = null
            if (isClick) {
                val highlights = hbline_chart.highlighted
                if (highlights != null) {
                    clickDate = dateIndexs[xPos]
                }
            }
            renderTitle(xPos, isClick, clickDate)
        } catch (e1: Exception) {
            e1.printStackTrace()
        }
    }

    private fun renderTitle(xPos: Int, isClick: Boolean, clickDate: String?) {
        if (isClick) {
            try {
                val firstLineValue =
                    (MathUtils.forValF(mDataList[xPos].zhData, -99f) * 100).toString()
                val secondLineValue =
                    (MathUtils.forValF(mDataList[xPos].bjjzData, -99f) * 100).toString()
                val secondLineValue2 =
                    (MathUtils.forValF(mDataList[xPos].bjjzData2, -99f) * 100).toString()
                mContainerFragment?.setTitleText(firstLineValue,
                    secondLineValue,
                    secondLineValue2,
                    clickDate,
                    false,
                    false)
            } catch (e1: Exception) {
                e1.printStackTrace()
            }
        } else {
            hbline_chart.cleanHighlight()
            mContainerFragment?.setTitleText(null, null, null, null, false, false)
        }
    }

    override fun onDestroy() {
        if (hbline_chart != null) {
            hbline_chart.clear()
        }
        super.onDestroy()
    }

    private fun setLineChartData(list: List<CombineChartData.Item>) {
        mDataList = list
        renderTitle(0, false, null)

        mFundEntryList.clear()
        mOther1EntryList.clear()
        mOther2EntryList.clear()
        dateIndexs.clear()
        var min = 0f
        val size = list.size
        for (i in 0 until size) {
            val gData = list[i]
            try {
                dateIndexs.add(gData.hbdate ?: "")
                val f = MathUtils.forValF(gData.zhData, 0f) * 100
                mFundEntryList.add(Entry(i.toFloat(), f, gData))
                var f1 = 0.0f
                if (!StrUtils.isEmpty(gData.bjjzData)) {
                    f1 = MathUtils.forValF(gData.bjjzData, 0f) * 100
                    mOther1EntryList.add(Entry(i.toFloat(), f1, gData))
                }
                var f2 = 0.0f
                if (!StrUtils.isEmpty(gData.bjjzData2)) {
                    f2 = MathUtils.forValF(gData.bjjzData2, 0f) * 100
                    mOther2EntryList.add(Entry(i.toFloat(), f2, gData))
                }
                //y轴最大/最小值
                if (min > f) min = f
                if (min > f1) min = f1
                if (min > f2) min = f2
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        min *= 1.1f
        if (min % 10 != 0f) {
            min = ((min / 10).toInt() - 1) * 10.toFloat()
        }
        hbline_chart.axisLeft.axisMinimum = min
        var labelCount = 6
        if (min % 3 == 0f) {
            labelCount = 4
        } else if (min % 4 == 0f) {
            labelCount = 5
        }
        hbline_chart.axisLeft.setLabelCount(labelCount, true)
        hbline_chart.axisLeft.valueFormatter = DefaultPercentValueFormat(0, true)

        drawChart(FragSmCombineDetail.mClickIncomeChartLineType == 0,
            FragSmCombineDetail.mClickIncomeChartLineType)
    }

    /**
     * drawFill是因为初始的时候 回撤图要默认填充效果
     */
    fun drawChart(drawFill: Boolean, type: Int) {
        if (!isAdded) return

        val sets = java.util.ArrayList<ILineDataSet>()

        if (mFundEntryList.size > 0) {
            val fundSet = LineDataSet(mFundEntryList, "")
            fundSet.mode = LineDataSet.Mode.LINEAR
            fundSet.axisDependency = YAxis.AxisDependency.LEFT
            fundSet.lineWidth = 1f
            fundSet.color = ContextCompat.getColor(GlobalApp.getApp(), R.color.fd_f92d2d)
            fundSet.isHighlightEnabled = true
            fundSet.highlightLineWidth = 1f
            fundSet.highLightColor = ContextCompat.getColor(GlobalApp.getApp(), R.color.fd_f92d2d)
            fundSet.setDrawVerticalHighlightIndicator(true)
            fundSet.setDrawHorizontalHighlightIndicator(false)
            fundSet.setDrawValues(false)
            fundSet.setDrawCircles(false)
            fundSet.circleRadius = 0f
            if (drawFill || (type == 1 && FragSmCombineDetail.mClickIncomeChartType1)) {
                fundSet.setDrawFilled(true)
                fundSet.lineWidth = 1.3f
                //默认样式就是填充
                fundSet.fillDrawable = ContextCompat.getDrawable(GlobalApp.getApp(), R.drawable.bg_sm_chart_fill_red_1)
                fundSet.setFillFormatter { _, dataProvider -> dataProvider.yChartMax }
            } else {
                fundSet.setDrawFilled(false)
            }
            if (type == 1) {
                sets.add(fundSet)
            } else {
                sets.add(0, fundSet)
            }
        }

        if (mOther1EntryList.size > 0) {
            val otherSet = LineDataSet(mOther1EntryList, "")
            otherSet.mode = LineDataSet.Mode.LINEAR
            otherSet.axisDependency = YAxis.AxisDependency.LEFT
            otherSet.lineWidth = 1f
            otherSet.color = ContextCompat.getColor(GlobalApp.getApp(), R.color.fd_9e9e9e)
            otherSet.isHighlightEnabled = true
            otherSet.highlightLineWidth = 1f
            otherSet.highLightColor = ContextCompat.getColor(GlobalApp.getApp(), R.color.fd_f92d2d)
            otherSet.setDrawVerticalHighlightIndicator(true)
            otherSet.setDrawHorizontalHighlightIndicator(false)
            otherSet.setDrawValues(false)
            otherSet.setDrawCircles(false)
            if (type == 2 && FragSmCombineDetail.mClickIncomeChartType2) {
                otherSet.lineWidth = 1.3f
                otherSet.setDrawFilled(true)
                otherSet.fillDrawable = ContextCompat.getDrawable(GlobalApp.getApp(), R.drawable.bg_sm_chart_fill_bbbdcb)
                otherSet.setFillFormatter { _, dataProvider -> dataProvider.yChartMax }
            } else {
                otherSet.setDrawFilled(false)
            }
            otherSet.circleRadius = 0f
            if (type == 1) {
                sets.add(otherSet)
            } else {
                sets.add(0, otherSet)
            }
        }

        if (mOther2EntryList.size > 0) {
            val otherSet = LineDataSet(mOther2EntryList, "")
            otherSet.mode = LineDataSet.Mode.LINEAR
            otherSet.axisDependency = YAxis.AxisDependency.LEFT
            otherSet.lineWidth = 1f
            otherSet.color = ContextCompat.getColor(GlobalApp.getApp(), R.color.fd_4c77aa)
            otherSet.isHighlightEnabled = true
            otherSet.highlightLineWidth = 1f
            otherSet.highLightColor = ContextCompat.getColor(GlobalApp.getApp(), R.color.fd_f92d2d)
            otherSet.setDrawVerticalHighlightIndicator(true)
            otherSet.setDrawHorizontalHighlightIndicator(false)
            otherSet.setDrawValues(false)
            otherSet.setDrawCircles(false)
            if (type == 3 && FragSmCombineDetail.mClickIncomeChartType3) {
                otherSet.lineWidth = 1.3f
                otherSet.setDrawFilled(true)
                otherSet.fillDrawable = ContextCompat.getDrawable(GlobalApp.getApp(), R.drawable.bg_sm_chart_fill_blue_1)
                otherSet.setFillFormatter { _, dataProvider -> dataProvider.yChartMax }
            } else {
                otherSet.setDrawFilled(false)
            }
            otherSet.circleRadius = 0f
            if (type == 1) {
                sets.add(otherSet)
            } else {
                sets.add(0, otherSet)
            }
        }

        hbline_chart.data = LineData(sets)
        hbline_chart.invalidate()
    }

    fun getStartTime(): String {
        return dateIndexs[0]
    }

}
