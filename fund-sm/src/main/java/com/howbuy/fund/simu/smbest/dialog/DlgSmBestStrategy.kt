package com.howbuy.fund.simu.smbest.dialog

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Dialog
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.fragment.app.FragmentManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager.widget.ViewPager
import com.howbuy.dialog.DlgHelper
import com.howbuy.fund.base.BaseDialogFragment
import com.howbuy.fund.base.config.ValConfig
import com.howbuy.fund.base.nav.NavHelper
import com.howbuy.fund.base.utils.FundTextUtils
import com.howbuy.fund.base.utils.span.SpannableItem
import com.howbuy.fund.base.utils.span.SpannableUtils
import com.howbuy.fund.base.widget.xrecyclerdivider.builder.XLinearBuilder
import com.howbuy.fund.simu.CommonSmBuilder
import com.howbuy.fund.simu.R
import com.howbuy.fund.simu.college.home.CenterLayoutManager
import com.howbuy.fund.simu.entity.SmBestStrategy
import com.howbuy.fund.simu.smbest.adapter.AdpDlgSmBestStrategyTab
import com.howbuy.fund.simu.smbest.adapter.AdpSmBestStrategyVp
import com.howbuy.lib.utils.ColorUtils
import com.howbuy.lib.utils.LogUtils
import com.howbuy.lib.utils.SysUtils
import kotlinx.android.synthetic.main.dlg_sm_best_strategy.view.*


/**
 * @Description 私募优选-策略解读-弹框
 * <AUTHOR>
 * @Date 2024/1/30
 * @Version v838
 * @param currentTab 当前选中的tabCode 定位用 一级tab
 */
class DlgSmBestStrategy : BaseDialogFragment() {

    companion object {
        fun showDialog(activity: Activity?, childFragmentManager: FragmentManager, currentTab: String? = null) {
            activity?.let {
                val dlgHelper = DlgHelper()
                dlgHelper.showDialog(activity, DlgHelper.DlgArg("", true, true), 0)
                CommonSmBuilder.reqSmBestStrategy(0) {
                    if (activity.isDestroyed || childFragmentManager.isDestroyed) return@reqSmBestStrategy
                    dlgHelper.closeDialog(activity)
                    if (it.isSuccess && it.mData != null && it.mData is SmBestStrategy && !(it.mData as SmBestStrategy).explainCategoryList.isNullOrEmpty()) {
                        getInstance(currentTab, (it.mData as SmBestStrategy)).show(childFragmentManager, null)
                    } else {
                        LogUtils.pop("抱歉，我们暂时无法处理您的请求，请稍后再试。")
                    }
                }
            }
        }

        private fun getInstance(currentTab: String? = null, data: SmBestStrategy): DlgSmBestStrategy {
            val bundle = NavHelper.obtainArg("", ValConfig.IT_ID, currentTab, ValConfig.IT_DATA, data)
            val fragment = DlgSmBestStrategy()
            fragment.arguments = bundle
            return fragment
        }
    }

    private var centerLayoutManager: CenterLayoutManager? = null
    private val mTabAdapter by lazy {
        AdpDlgSmBestStrategyTab()
    }

    override fun onCreateDialog(): Dialog {
        val dialog = Dialog(activity!!, R.style.cpay_MyDialog)
        val view = activity?.layoutInflater?.inflate(R.layout.dlg_sm_best_strategy, null)
        view?.let {
            dialog.setContentView(it)
        }
        dialog.window?.setLayout(SysUtils.getWidth(context) * 9 / 10, LinearLayout.LayoutParams.WRAP_CONTENT)
        dialog.setCanceledOnTouchOutside(true)
        dialog.setCancelable(true)
        return dialog
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        return activity?.layoutInflater?.inflate(R.layout.dlg_sm_best_strategy, null)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val data = arguments?.getParcelable<SmBestStrategy>(ValConfig.IT_DATA)
        data?.let {
            renderUI(view, it)
        }
    }

    private fun renderUI(view: View, data: SmBestStrategy) {
        view.rcv_tab.apply {
            centerLayoutManager = CenterLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            layoutManager = centerLayoutManager
        }
        view.tv_risk.visibility = if (TextUtils.isEmpty(data.riskTip)) View.GONE else View.VISIBLE
        view.tv_risk.text = data.riskTip
        //定位当前传入tabCode
        val currentTabCode = arguments?.getString(ValConfig.IT_ID)
        var position = 0
        data.explainCategoryList?.forEachIndexed { index, item ->
            if (TextUtils.equals(currentTabCode, item.firstCode)) {
                position = index
            }
        }
        renderTitle(view.tv_strategy_title, view.tv_update_time, data.explainCategoryList?.getOrNull(position))
        setArrowStatus(position, data.explainCategoryList?.size ?: 0, view.iv_arrow_left, view.iv_arrow_right)
        view.vp.offscreenPageLimit = data.explainCategoryList?.size ?: 0
        mTabAdapter.setOnItemClickListener { _, _, p ->
            view.vp.setCurrentItem(p, true)
        }
        mTabAdapter.setList(data.explainCategoryList)
        view.rcv_tab.adapter = mTabAdapter
        if (view.rcv_tab.itemDecorationCount == 0) {
            view.rcv_tab.addItemDecoration(
                XLinearBuilder(context).setSpacing(5f).setShowFirstTopLine(true).setShowLastLine(true)
                    .setFirstLineExtraSpace(15f).setLastLineExtraSpace(15f).build()
            )
        }
        view.vp.adapter =
            AdpSmBestStrategyVp(childFragmentManager, view.vp, data.explainCategoryList ?: mutableListOf())
        view.vp.addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
            override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {}

            override fun onPageSelected(position: Int) {
                view.vp.resetHeight(position)
                setArrowStatus(position, data.explainCategoryList?.size ?: 0, view.iv_arrow_left, view.iv_arrow_right)
                centerLayoutManager?.smoothScrollToPosition(
                    view.rcv_tab,
                    RecyclerView.State(),
                    mTabAdapter.mSelectIndex,
                    position
                )
                mTabAdapter.mSelectIndex = position
                mTabAdapter.notifyDataSetChanged()
                renderTitle(view.tv_strategy_title, view.tv_update_time, data.explainCategoryList?.getOrNull(position))
            }

            override fun onPageScrollStateChanged(state: Int) {}
        })
        view.iv_arrow_left.setOnClickListener {
            val current = view.vp.currentItem
            view.vp.setCurrentItem(current - 1, true)
        }
        view.iv_arrow_right.setOnClickListener {
            val current = view.vp.currentItem
            view.vp.setCurrentItem(current + 1, true)
        }
        view.vp.post {
            view.vp.setCurrentItem(position, false)
            view.vp.resetHeight(position)
        }
    }

    private fun setArrowStatus(position: Int, size: Int, leftArrow: ImageView, rightArrow: ImageView) {
        if (size <= 1) {
            leftArrow.alpha = 0.3f
            rightArrow.alpha = 0.3f
            leftArrow.isEnabled = false
            rightArrow.isEnabled = false
        } else {
            if (position <= 0) {
                leftArrow.alpha = 0.3f
                rightArrow.alpha = 1.0f
                leftArrow.isEnabled = false
                rightArrow.isEnabled = true
            } else if (position >= mTabAdapter.data.size - 1) {
                leftArrow.alpha = 1.0f
                rightArrow.alpha = 0.3f
                leftArrow.isEnabled = true
                rightArrow.isEnabled = false
            } else {
                leftArrow.alpha = 1.0f
                rightArrow.alpha = 1.0f
                leftArrow.isEnabled = true
                rightArrow.isEnabled = true
            }
        }
    }

    @SuppressLint("SetTextI18n")
    private fun renderTitle(tvTitle: TextView, tvTime: TextView, tab: SmBestStrategy.Item?) {
        if (TextUtils.isEmpty(tab?.explainName)) {
            tvTitle.text = SpannableUtils.formatStr(
                SpannableItem("--", ColorUtils.parseColor("#333333"))
            )
        } else {
            tvTitle.text = SpannableUtils.formatStr(
                SpannableItem("${tab?.explainName?.take(2)}", ColorUtils.parseColor("#333333")),
                SpannableItem(tab?.explainName?.drop(2)?.take(2), ColorUtils.parseColor("#E54646")),
            )
        }
        tvTime.text = "更新于：${FundTextUtils.showTextEmpty(tab?.updateTime)}"
    }
}