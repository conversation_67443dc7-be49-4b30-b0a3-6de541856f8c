<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:orientation="vertical">

    <!--基金-->
    <LinearLayout
        android:id="@+id/lay_fund"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:gravity="center_vertical"
        android:onClick="onXmlBtClick"
        android:orientation="horizontal"
        android:paddingStart="15dp"
        android:paddingEnd="15dp">

        <TextView
            android:id="@+id/tv_fund_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="#296bdc"
            android:textSize="16dp"
            tools:text="熟练度空间付款款水电费" />

        <TextView
            android:id="@+id/tv_fund_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:textColor="@color/fd_text_1"
            android:textSize="12dp"
            tools:text="共12个相关问题>" />
    </LinearLayout>

    <!--其他-->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/lay_other"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="15dp"
        android:paddingEnd="15dp"
        android:visibility="gone">

        <TextView
            android:id="@+id/tv_other_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="#296bdc"
            android:textSize="16dp"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@id/tv_other_count"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="熟练度空间付款水电" />

        <TextView
            android:id="@+id/tv_other_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:textColor="@color/fd_text_1"
            android:textSize="12dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintLeft_toRightOf="@id/tv_other_name"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="共12个相关问题>" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_question_detail_top"
        android:orientation="vertical"
        android:paddingTop="15dp"
        android:paddingBottom="20dp">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="15dp"
            android:layout_marginRight="15dp"
            android:lineSpacingExtra="5dp"
            android:textColor="@color/cl_sm_title"
            android:textSize="20sp" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="6dp"
            android:layout_marginRight="15dp"
            android:gravity="bottom"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_owner"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/fd_text_3"
                android:textSize="13sp"
                app:layout_constrainedWidth="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/iv_vip"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                tools:text="来自于dajskdajdak" />

            <ImageView
                android:id="@+id/iv_vip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="4dp"
                android:src="@drawable/sm_lable_vip"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/iv_user"
                app:layout_constraintStart_toEndOf="@+id/tv_owner"
                tools:visibility="visible" />

            <ImageView
                android:id="@+id/iv_user"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="4dp"
                android:src="@drawable/sm_lable_user"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/tv_super"
                app:layout_constraintStart_toEndOf="@+id/iv_vip"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tv_super"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="4dp"
                android:background="@drawable/bg_black"
                android:paddingLeft="4dp"
                android:paddingTop="1dp"
                android:paddingRight="3dp"
                android:paddingBottom="1dp"
                android:text="问答达人"
                android:textColor="@color/cl_ffdb9a"
                android:textSize="11sp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/tv_time"
                app:layout_constraintStart_toEndOf="@+id/iv_user"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tv_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="5dp"
                android:textColor="@color/fd_text_3"
                android:textSize="13sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/tv_super"
                tools:text="sdfsdf" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/tv_time_line_feed"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="5dp"
            android:layout_marginRight="15dp"
            android:textColor="@color/fd_text_3"
            android:textSize="13sp"
            android:visibility="gone"
            tools:text="sdfsdf" />

    </LinearLayout>
</LinearLayout>