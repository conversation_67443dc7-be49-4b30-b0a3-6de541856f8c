package com.howbuy.fund.common.widget

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.content.Context
import android.text.TextUtils
import android.util.AttributeSet
import android.view.View
import android.view.animation.OvershootInterpolator
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.content.ContextCompat
import com.howbuy.fund.common.R
import com.howbuy.lib.utils.DensityUtils
import com.howbuy.lib.utils.MathUtils
import kotlinx.android.synthetic.main.yxs_level_float_view.view.*

/**
 * @Description 研习社-首页-任务等级气泡
 * <AUTHOR>
 * @Date 2023/4/3
 * @Version v816
 */
class YxsLevelFloatView @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0) :
    ConstraintLayout(context, attrs, defStyleAttr) {

    private val scale = 0.85f//宽高104->88

    /** 是否为可升级状态 */
    private var isUpgrade = false

    /** 是否为放大状态（默认放大） */
    private var isLargerStatus = true

    private val planeMoveAnim by lazy {
        ObjectAnimator.ofFloat(iv_plane, "translationY", 0f, -15f, 0f)
    }
    private val lightMoveAnim by lazy {
        ObjectAnimator.ofFloat(iv_light, "translationY", 0f, 15f, 0f)
    }

    init {
        inflate(context, R.layout.yxs_level_float_view, this)
        planeMoveAnim.apply {
            repeatCount = -1
//            interpolator = OvershootInterpolator()
            duration = 800
        }
        lightMoveAnim.apply {
            repeatCount = -1
//            interpolator = OvershootInterpolator()
            duration = 800
        }
    }

    /**
     * @param maxLevelStatus 只要达到满级, 就强制显示:"学习任务" 的文案
     */
    fun setLevel(level: String?, maxLevelStatus: Boolean, percent: String?) {
        val p = MathUtils.forValI(percent, 0)
        if (p < 100 || maxLevelStatus) {
            isUpgrade = false
            tv_level.visibility = View.VISIBLE
            tv_level.text = "LV${level}"
            tv_level_desc.text = if (maxLevelStatus){"学习任务"} else {"升级任务"}
            tv_level_desc.background = ContextCompat.getDrawable(context, R.drawable.bg_yxs_level_float_txt_bottom)
            iv_plane.setImageResource(R.drawable.yxs_float_plane)
            iv_light.background = ContextCompat.getDrawable(context, R.drawable.yxs_float_light)

            val constraintSet = ConstraintSet()
            constraintSet.clone(findViewById<ConstraintLayout>(R.id.root))

            constraintSet.clear(R.id.iv_plane, ConstraintSet.END)
            constraintSet.connect(R.id.iv_plane, ConstraintSet.TOP, R.id.iv_red_bg, ConstraintSet.TOP, 0)
            constraintSet.connect(R.id.iv_plane, ConstraintSet.START, R.id.iv_red_bg, ConstraintSet.START, DensityUtils.dp2px(20f))

            constraintSet.connect(R.id.iv_light, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP, DensityUtils.dp2px(15f))
            constraintSet.connect(R.id.iv_light, ConstraintSet.START, ConstraintSet.PARENT_ID, ConstraintSet.START, 0)
            constraintSet.connect(R.id.iv_light, ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END, 0)

            constraintSet.applyTo(findViewById(R.id.root))

            val lpLight = iv_light.layoutParams as LayoutParams
            lpLight.width = DensityUtils.dp2px(74f)
            lpLight.height = DensityUtils.dp2px(35f)
            iv_light.layoutParams = lpLight

            cancelMoveAnim()
        } else {
            isUpgrade = true
            tv_level.visibility = View.GONE
            tv_level_desc.text = "立即升级"
            tv_level_desc.background = ContextCompat.getDrawable(context, R.drawable.bg_yxs_level_float_txt)
            iv_plane.setImageResource(R.drawable.yxs_float_plane_upgrade)
            iv_light.background = ContextCompat.getDrawable(context, R.drawable.yxs_float_light_upgrade)

            val constraintSet = ConstraintSet()
            constraintSet.clone(findViewById<ConstraintLayout>(R.id.root))

            constraintSet.clear(R.id.iv_plane, ConstraintSet.END)
            constraintSet.connect(R.id.iv_plane, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP, DensityUtils.dp2px(5f))
            constraintSet.connect(R.id.iv_plane, ConstraintSet.START, R.id.iv_red_bg, ConstraintSet.START, 0)
            constraintSet.connect(R.id.iv_plane, ConstraintSet.END, R.id.iv_red_bg, ConstraintSet.END, 0)

            constraintSet.connect(R.id.iv_light, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP, DensityUtils.dp2px(8f))
            constraintSet.connect(R.id.iv_light, ConstraintSet.START, ConstraintSet.PARENT_ID, ConstraintSet.START, DensityUtils.dp2px(3f))
            constraintSet.connect(R.id.iv_light, ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END, 0)

            constraintSet.applyTo(findViewById(R.id.root))

            val lpLight = iv_light.layoutParams as LayoutParams
            lpLight.width = DensityUtils.dp2px(55f)
            lpLight.height = DensityUtils.dp2px(32f)
            iv_light.layoutParams = lpLight
            cancelMoveAnim()
            planeMoveAnim.start()
            lightMoveAnim.start()
        }
        pb_percent.progress = p
        tv_percent.text = "${p}%"
    }

    fun toSmall() {
        cancelMoveAnim()
        if (!isLargerStatus) return
        isLargerStatus = false
        lay_percent.visibility = View.GONE
        iv_light.visibility = View.GONE
        setPivot()

        // alpha动画
        val alphaPlane = ObjectAnimator.ofFloat(iv_plane, "alpha", 1f, 0.5f)
        val alphaRedBg = ObjectAnimator.ofFloat(iv_red_bg, "alpha", 1f, 0.5f)
        val alphaRingBg = ObjectAnimator.ofFloat(iv_ring_bg, "alpha", 0f, 0.5f)
        // 缩放动画
        val scaleRedBgX = ObjectAnimator.ofFloat(iv_red_bg, "scaleX", 1f, scale)
        val scaleRedBgY = ObjectAnimator.ofFloat(iv_red_bg, "scaleY", 1f, scale)
        val scaleRingBgX = ObjectAnimator.ofFloat(iv_ring_bg, "scaleX", 1f, scale)
        val scaleRingBgY = ObjectAnimator.ofFloat(iv_ring_bg, "scaleY", 1f, scale)
        // 平移动画
        val translationX = ObjectAnimator.ofFloat(this, "translationX", DensityUtils.dp2px(10f).toFloat())
        val planeTranslationX = ObjectAnimator.ofFloat(iv_plane, "translationX", DensityUtils.dp2px(if (isUpgrade) 4f else -3.5f).toFloat())
        val planeTranslationY = ObjectAnimator.ofFloat(iv_plane, "translationY", DensityUtils.dp2px(if (isUpgrade) 8f else 8f).toFloat())
        // 动画集合
        val set = AnimatorSet()
        set.interpolator = OvershootInterpolator()
        set.playTogether(
            alphaPlane,
            alphaRedBg,
            alphaRingBg,
            scaleRedBgX,
            scaleRedBgY,
            scaleRingBgX,
            scaleRingBgY,
            translationX,
            planeTranslationX,
            planeTranslationY
        )
        set.duration = 400
        set.start()
    }

    fun toLarge() {
        if (isLargerStatus) return
        isLargerStatus = true
        lay_percent.visibility = View.VISIBLE
        iv_light.visibility = View.VISIBLE
        // alpha动画
        val alphaPlane = ObjectAnimator.ofFloat(iv_plane, "alpha", 1f)
        val alphaRedBg = ObjectAnimator.ofFloat(iv_red_bg, "alpha", 1f)
        val alphaRingBg = ObjectAnimator.ofFloat(iv_ring_bg, "alpha", 0f)
        // 缩放动画
        val scaleRedBgX = ObjectAnimator.ofFloat(iv_red_bg, "scaleX", 1f)
        val scaleRedBgY = ObjectAnimator.ofFloat(iv_red_bg, "scaleY", 1f)
        val scaleRingBgX = ObjectAnimator.ofFloat(iv_ring_bg, "scaleX", 1f)
        val scaleRingBgY = ObjectAnimator.ofFloat(iv_ring_bg, "scaleY", 1f)
        // 平移动画
        val translationX = ObjectAnimator.ofFloat(this, "translationX", 0f)
        val planeTranslationX = ObjectAnimator.ofFloat(iv_plane, "translationX", 0f)
        val planeTranslationY = ObjectAnimator.ofFloat(iv_plane, "translationY", 0f)
        // 动画集合
        val set = AnimatorSet()
        set.interpolator = OvershootInterpolator()
        set.playTogether(
            alphaPlane,
            alphaRedBg,
            alphaRingBg,
            scaleRedBgX,
            scaleRedBgY,
            scaleRingBgX,
            scaleRingBgY,
            translationX,
            planeTranslationX,
            planeTranslationY
        )
        set.duration = 400
        set.start()
        set.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                super.onAnimationEnd(animation)
                if (isUpgrade) {
                    cancelMoveAnim()
                    planeMoveAnim.start()
                    lightMoveAnim.start()
                }
            }
        })
    }

    /**
     * 设置缩放中心点为右上角
     */
    private fun setPivot() {
        pivotX = width.toFloat()
        pivotY = 0f
        iv_red_bg.pivotX = iv_red_bg.width.toFloat()
        iv_red_bg.pivotY = 0f
        iv_ring_bg.pivotX = iv_ring_bg.width.toFloat()
        iv_ring_bg.pivotY = 0f
    }

    override fun onDetachedFromWindow() {
        cancelMoveAnim()
        super.onDetachedFromWindow()
    }

    private fun cancelMoveAnim(){
        if (planeMoveAnim.isRunning) {
            try {
                planeMoveAnim.cancel()
                lightMoveAnim.cancel()
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
}