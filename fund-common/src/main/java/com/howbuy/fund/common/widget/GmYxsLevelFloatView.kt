package com.howbuy.fund.common.widget

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.view.animation.OvershootInterpolator
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.content.ContextCompat
import com.howbuy.fund.common.R
import com.howbuy.lib.utils.DensityUtils
import com.howbuy.lib.utils.MathUtils
import kotlin.math.min
import android.view.LayoutInflater
import android.view.ViewGroup
import com.howbuy.fund.common.databinding.GmYxsLevelFloatViewBinding

/**
 * @Description 研习社-首页-任务等级气泡
 * <AUTHOR>
 * @Date 2023/4/3
 * @Version v816
 */
class GmYxsLevelFloatView @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0) :
    ConstraintLayout(context, attrs, defStyleAttr) {

    private lateinit var binding: GmYxsLevelFloatViewBinding

    private val scale = 0.85f//宽高104->88

    /** 是否为可升级状态 */
    private var isUpgrade = false

    /** 是否为放大状态（默认放大） */
    private var isLargerStatus = true


    private val planeMoveAnim by lazy {
        ObjectAnimator.ofFloat(binding.ivPlane, "translationY", 0f, -15f, 0f)
    }
    private val lightMoveAnim by lazy {
        ObjectAnimator.ofFloat(binding.ivLight, "translationY", 0f, 15f, 0f)
    }

    init {
        inflate(context, R.layout.gm_yxs_level_float_view, this)
        binding = GmYxsLevelFloatViewBinding.inflate(LayoutInflater.from(context))
        planeMoveAnim.apply {
            repeatCount = -1
//            interpolator = OvershootInterpolator()
            duration = 800
        }
        lightMoveAnim.apply {
            repeatCount = -1
//            interpolator = OvershootInterpolator()
            duration = 800
        }
    }

    fun setLevel(level: String?, percent: String?) {
        val p = min(MathUtils.forValF(percent, 0F).toInt(), 100)
        if (p < 100) {
            isUpgrade = false
            binding.tvLevel.visibility = View.VISIBLE
            binding.tvLevel.text = "LV${level}"
            binding.tvLevelDesc.text = "升级任务"
            binding.tvLevelDesc.background = ContextCompat.getDrawable(context, R.drawable.bg_yxs_level_float_txt_bottom)
            binding.ivPlane.setImageResource(R.drawable.gm_yxs_float_plane)
            binding.ivLight.background = ContextCompat.getDrawable(context, R.drawable.yxs_float_light)

            val constraintSet = ConstraintSet()
            constraintSet.clone(findViewById<ConstraintLayout>(R.id.root))

            constraintSet.clear(R.id.iv_plane, ConstraintSet.END)
            constraintSet.connect(R.id.iv_plane, ConstraintSet.TOP, R.id.iv_red_bg, ConstraintSet.TOP, 0)
            constraintSet.connect(R.id.iv_plane, ConstraintSet.START, R.id.iv_red_bg, ConstraintSet.START, DensityUtils.dp2px(20f))

            constraintSet.connect(R.id.iv_light, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP, DensityUtils.dp2px(15f))
            constraintSet.connect(R.id.iv_light, ConstraintSet.START, ConstraintSet.PARENT_ID, ConstraintSet.START, 0)
            constraintSet.connect(R.id.iv_light, ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END, 0)

            constraintSet.applyTo(findViewById(R.id.root))

            val lpLight = binding.ivLight.layoutParams as LayoutParams
            lpLight.width = DensityUtils.dp2px(74f)
            lpLight.height = DensityUtils.dp2px(35f)
            binding.ivLight.layoutParams = lpLight

            cancelMoveAnim()
        } else {
            isUpgrade = true
            binding.tvLevel.visibility = View.GONE
            binding.tvLevelDesc.text = "立即升级"
            binding.tvLevelDesc.background = ContextCompat.getDrawable(context, R.drawable.bg_yxs_level_float_txt)
            binding.ivPlane.setImageResource(R.drawable.gm_yxs_float_plane_upgrade)
            binding.ivLight.background = ContextCompat.getDrawable(context, R.drawable.yxs_float_light_upgrade)

            val constraintSet = ConstraintSet()
            constraintSet.clone(findViewById<ConstraintLayout>(R.id.root))

            constraintSet.clear(R.id.iv_plane, ConstraintSet.END)
            constraintSet.connect(R.id.iv_plane, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP, DensityUtils.dp2px(3f))
            constraintSet.connect(R.id.iv_plane, ConstraintSet.START, R.id.iv_red_bg, ConstraintSet.START, 0)
            constraintSet.connect(R.id.iv_plane, ConstraintSet.END, R.id.iv_red_bg, ConstraintSet.END, 0)

            constraintSet.connect(R.id.iv_light, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP, DensityUtils.dp2px(8f))
            constraintSet.connect(R.id.iv_light, ConstraintSet.START, ConstraintSet.PARENT_ID, ConstraintSet.START, DensityUtils.dp2px(3f))
            constraintSet.connect(R.id.iv_light, ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END, 0)

            constraintSet.applyTo(findViewById(R.id.root))

            val lpLight = binding.ivLight.layoutParams as LayoutParams
            lpLight.width = DensityUtils.dp2px(55f)
            lpLight.height = DensityUtils.dp2px(32f)
            binding.ivLight.layoutParams = lpLight
            cancelMoveAnim()

            planeMoveAnim.start()
            lightMoveAnim.start()
        }
        binding.pbPercent.progress = p
        binding.tvPercent.text = "${p}%"
    }

    fun toSmall() {
        cancelMoveAnim()
        if (!isLargerStatus) return
        isLargerStatus = false
//        if (smallAnim) return
        binding.layPercent.visibility = View.GONE
        binding.ivLight.visibility = View.GONE
        setPivot()
//        smallAnim = true

        // alpha动画
        val alphaPlane = ObjectAnimator.ofFloat(binding.ivPlane, "alpha", 1f, 0.5f)
        val alphaRedBg = ObjectAnimator.ofFloat(binding.ivRedBg, "alpha", 1f, 0.5f)
        val alphaRingBg = ObjectAnimator.ofFloat(binding.ivRingBg, "alpha", 0f, 0.5f)
        // 缩放动画
        val scaleRedBgX = ObjectAnimator.ofFloat(binding.ivRedBg, "scaleX", 1f, scale)
        val scaleRedBgY = ObjectAnimator.ofFloat(binding.ivRedBg, "scaleY", 1f, scale)
        val scaleRingBgX = ObjectAnimator.ofFloat(binding.ivRingBg, "scaleX", 1f, scale)
        val scaleRingBgY = ObjectAnimator.ofFloat(binding.ivRingBg, "scaleY", 1f, scale)
        // 平移动画
        val translationX = ObjectAnimator.ofFloat(this, "translationX", DensityUtils.dp2px(10f).toFloat())
        val planeTranslationX = ObjectAnimator.ofFloat(binding.ivPlane, "translationX", DensityUtils.dp2px(if (isUpgrade) 4f else -3.5f).toFloat())
        val planeTranslationY = ObjectAnimator.ofFloat(binding.ivPlane, "translationY", DensityUtils.dp2px(if (isUpgrade) 8f else 8f).toFloat())
        // 动画集合
        val set = AnimatorSet()
        set.interpolator = OvershootInterpolator()
        set.playTogether(
            alphaPlane,
            alphaRedBg,
            alphaRingBg,
            scaleRedBgX,
            scaleRedBgY,
            scaleRingBgX,
            scaleRingBgY,
            translationX,
            planeTranslationX,
            planeTranslationY
        )
        set.duration = 400
        set.start()
        set.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                super.onAnimationEnd(animation)
//                smallAnim = false
            }
        })
    }

    fun toLarge() {
        if (isLargerStatus) return
        isLargerStatus = true
//        if (largeAnim) return
        binding.layPercent.visibility = View.VISIBLE
        binding.ivLight.visibility = View.VISIBLE
//        largeAnim = true
        // alpha动画
        val alphaPlane = ObjectAnimator.ofFloat(binding.ivPlane, "alpha", 1f)
        val alphaRedBg = ObjectAnimator.ofFloat(binding.ivRedBg, "alpha", 1f)
        val alphaRingBg = ObjectAnimator.ofFloat(binding.ivRingBg, "alpha", 0f)
        // 缩放动画
        val scaleRedBgX = ObjectAnimator.ofFloat(binding.ivRedBg, "scaleX", 1f)
        val scaleRedBgY = ObjectAnimator.ofFloat(binding.ivRedBg, "scaleY", 1f)
        val scaleRingBgX = ObjectAnimator.ofFloat(binding.ivRingBg, "scaleX", 1f)
        val scaleRingBgY = ObjectAnimator.ofFloat(binding.ivRingBg, "scaleY", 1f)
        // 平移动画
        val translationX = ObjectAnimator.ofFloat(this, "translationX", 0f)
        val planeTranslationX = ObjectAnimator.ofFloat(binding.ivPlane, "translationX", 0f)
        val planeTranslationY = ObjectAnimator.ofFloat(binding.ivPlane, "translationY", 0f)
        // 动画集合
        val set = AnimatorSet()
        set.interpolator = OvershootInterpolator()
        set.playTogether(
            alphaPlane,
            alphaRedBg,
            alphaRingBg,
            scaleRedBgX,
            scaleRedBgY,
            scaleRingBgX,
            scaleRingBgY,
            translationX,
            planeTranslationX,
            planeTranslationY
        )
        set.duration = 400
        set.start()
        set.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                super.onAnimationEnd(animation)
//                largeAnim = false
                if (isUpgrade) {
                    cancelMoveAnim()

                    planeMoveAnim.start()
                    lightMoveAnim.start()
                }
            }
        })
    }

    /**
     * 设置缩放中心点为右上角
     */
    private fun setPivot() {
        pivotX = width.toFloat()
        pivotY = 0f
        binding.ivRedBg.pivotX = binding.ivRedBg.width.toFloat()
        binding.ivRedBg.pivotY = 0f
        binding.ivRingBg.pivotX = binding.ivRingBg.width.toFloat()
        binding.ivRingBg.pivotY = 0f
    }

    override fun onDetachedFromWindow() {
        cancelMoveAnim()
        super.onDetachedFromWindow()
    }

    private fun cancelMoveAnim(){
        if (planeMoveAnim.isRunning) {
            try {
                planeMoveAnim.cancel()
                lightMoveAnim.cancel()
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
}