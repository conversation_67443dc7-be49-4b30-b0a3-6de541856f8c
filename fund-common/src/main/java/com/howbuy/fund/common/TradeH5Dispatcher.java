package com.howbuy.fund.common;

import android.app.Activity;
import android.os.Bundle;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.google.gson.Gson;
import com.howbuy.arouter_intercept_api.IInterceptCode;
import com.howbuy.fund.base.config.ApkConfig;
import com.howbuy.fund.base.config.ValConfig;
import com.howbuy.fund.base.nav.NavCompleteCallback;
import com.howbuy.fund.base.nav.NavHelper;
import com.howbuy.fund.base.router.AtyRouterPath;
import com.howbuy.fund.base.router.RouterHelper;
import com.howbuy.fund.base.router.fragpath.WebRouterPath;
import com.howbuy.fund.common.h5nav.FundPkIndexStrategy;
import com.howbuy.fund.common.h5nav.FundPkResultStrategy;
import com.howbuy.fund.common.h5nav.H5UrlModifyStrategy;
import com.howbuy.fund.common.h5nav.HelpNewStrategy;
import com.howbuy.fund.common.h5nav.ParseUrlWithFirstParameter;
import com.howbuy.fund.common.h5nav.RiskHighProtocolStrategy;
import com.howbuy.fund.impl.IAnalyticsProvider;
import com.howbuy.fund.logupload.ElkReportService;
import com.howbuy.fund.net.util.UrlUtils;
import com.howbuy.h5.H5UrlKeyConfig;
import com.howbuy.h5.entity.Html5FileDescript;
import com.howbuy.h5.h5config.ParserUriZipResource;
import com.howbuy.lib.utils.LogUtils;
import com.howbuy.lib.utils.StrUtils;
import com.howbuy.router.provider.IWebProvider;
import com.howbuy.router.proxy.Invoker;
import com.howbuy.userdata.business.ApiHelperKt;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.HashMap;
import java.util.Map;

import html5.AbsFragWebView;
import io.reactivex.Observable;
import io.reactivex.disposables.Disposable;

/**
 * 该类是交易H5模块跳转分发处理类
 * Created by tao.liang on 2016/3/15.
 */
public class TradeH5Dispatcher {
    /**
     * bundle 中带有的navType信息
     */
    private static final String KEY_NAV_TYPE = "KEY_NAV_TYPE";
    public static final int NAV_TO_WEBVIEW_NORMAL = 1;
    public static final int NAV_TO_WEBVIEW_TRADE = 2;
    public static final int NAV_TO_WEBVIEW_LOGIN = 3;

    /**
     * 保存每个key对应的Url特殊处理的逻辑实现
     */
    private static final Map<String, H5UrlModifyStrategy> urlModifyStrategy = new HashMap<>();
    /**
     * 从参数中动态获取urlKey的逻辑封装
     */
    private static final Map<String, H5UrlModifyStrategy> urlKeyPickStrategy = new HashMap<>();

    static {
        urlModifyStrategy.put(H5UrlKeyConfig.HELP_NEW, new HelpNewStrategy());
        urlModifyStrategy.put(H5UrlKeyConfig.RISK_HIGH_PROTOCOL, new RiskHighProtocolStrategy());
        urlModifyStrategy.put(H5UrlKeyConfig.FUND_PK_INDEX, new FundPkIndexStrategy());
        urlModifyStrategy.put(H5UrlKeyConfig.FUND_PK_RESULT, new FundPkResultStrategy());

        urlKeyPickStrategy.put(H5UrlKeyConfig.PORTFOLIO_ASSET, new ParseUrlWithFirstParameter());
        urlKeyPickStrategy.put(H5UrlKeyConfig.PORTFOLIO_ASSET_NO_ACCESS, new ParseUrlWithFirstParameter());
        urlKeyPickStrategy.put(H5UrlKeyConfig.SHORT_H5_KEY, new ParseUrlWithFirstParameter());
    }

    /**
     * 组装交易h5的参数
     * return Bundle
     *
     * 使用方式:
     *         val bundle = get(ICommonProvider::class.java).buildH5Params(
     *             activity,
     *             H5UrlKeyConfig.RISK_KYC_HZ,
     *             null,
     *             null
     *         )
     *         get(IWebProvider::class.java).launchWebView(
     *             activity,
     *             bundle
     *         ) { resultCode: Int, _: Bundle?, _: INavMgr? ->
     *             //在h5 合格投资认定做完后,返回本地页面,刷新当前用户数据
     *             targetAction?.let {
     *                 onResult(resultCode, it)
     *             }
     *         }
     */
    public static Observable<Bundle> buildTradeH5(Object cx, String tradeType,
                                      Object fundCodeOrBean,
                                      String from, Object... bundArg) {
        final String fundCode = (fundCodeOrBean instanceof String)? (String) fundCodeOrBean : null;
        final Object[] args = bundArg == null ? new Object[0] : bundArg;

        Invoker.getInstance().navigation(IAnalyticsProvider.class).pushLastTrade(fundCode, from);
        return parseUrl(tradeType, args)
                .flatMap(url -> {
                    //参数传递方式二: 通过url拼接参数形式 map 对象
                    HashMap<String, String> forUrlMap = new HashMap<>();
                    //用于保存jsParams方式传递参数，用于分批修改参数传递方式
                    HashMap<String, Object> injectMap = new HashMap<>();
                    Bundle bundle = getBundle(url, tradeType, fundCode, forUrlMap, injectMap, args);
                    return Observable.just(bundle);
                });
//        return buildLauncherArgs(cx, false, tradeType, null,
//                fundCodeOrBean, from, bundArg);
    }

    /**
     * 跳转的交易H5
     * use {@link #launchTradeH5WithCallback} instead.
     */
    @Deprecated
    public static void launchTradeH5(Object cx, String tradeType, Object fundCodeOrBean,
                                     String from, Object... bundArg) {
        launchTradeH5WithCallback(cx, tradeType, null, fundCodeOrBean , from, bundArg);
    }

    /**
     * 跳转的交易H5
     * @param callback 跳转回调
     */
    public static void launchTradeH5WithCallback(Object cx, String tradeType,
                                                 NavCompleteCallback callback,
                                                 Object fundCodeOrBean,
                                                 String from,  Object... bundArg) {
        buildLauncherArgs(cx, tradeType, callback, fundCodeOrBean, from, bundArg);
    }

    private static void buildLauncherArgs(Object cx, String tradeType,
                                          NavCompleteCallback callback,
                                          Object fundCodeOrBean,
                                          String from, Object... bundArg) {
        String fundCode = null;
        if (fundCodeOrBean instanceof String) {
            fundCode = (String) fundCodeOrBean;
        }
        Invoker.getInstance().navigation(IAnalyticsProvider.class).pushLastTrade(fundCode, from);
        dispatcherTradeH5(cx, tradeType, callback, fundCode, bundArg);
    }


    /**
     * 交易H5 分发处理 , 组装参数,获取url
     * 说明: 交易H5 分两种:
     * a. 掌基交易: TI 开头的 KEY 是 JS API 方式进行操作的(后期将废弃，替代为url传递，保证一一对应的参数传递)
     * b. 储蓄罐交易: 是h5页面使用 原来的 hb 来处理参数.
     * 2023-05-17之后，将对jsParams方式传递业务参数进行改造，修改为url传递参数，防止参数取值错误.参数传递可能涉及到url的修改，例如参数直接为Path的一部分而非query参数。
     * 因此，逻辑处理流程如下：
     * 1.修改url(如果需要)
     * 2.拼接query参数
     */
    private static void dispatcherTradeH5(Object cx,
                                          String urlKey,
                                          NavCompleteCallback callback,
                                          String fundCode,
                                          Object... args) {

        //防止java向kotlin传递null
        final Object[] argsArr = args == null ? new Object[0] : args;

        //根据key解析对应的url
        Disposable disposable = parseUrl(urlKey, args)
                .subscribe(url -> {
                            //参数传递方式二: 通过url拼接参数形式 map 对象
                            HashMap<String, String> forUrlMap = new HashMap<>();
                            //用于保存jsParams方式传递参数，用于分批修改参数传递方式
                            HashMap<String, Object> injectMap = new HashMap<>();
                            Bundle bundle = getBundle(url, urlKey, fundCode, forUrlMap, injectMap, argsArr);
                            launcherToWeb(cx, urlKey, bundle, callback);
                        },
                        err -> {
                            callback.onNavComplete(Activity.RESULT_CANCELED, null, null);
                            if (ApkConfig.getDebugLog()) {
                                err.printStackTrace();
                            }
                            StringBuilder sb = new StringBuilder();
                            sb.append("TradeH5Dispatcher.dispatcherTradeH5 error: ")
                                    .append(err.getMessage())
                                    .append("\nstack:");
                            StringWriter writer = new StringWriter();
                            PrintWriter printWriter = new PrintWriter(writer);
                            err.printStackTrace(printWriter);
                            Throwable cause = err.getCause();
                            while (cause != null) {
                                cause.printStackTrace(printWriter);
                                cause = cause.getCause();
                            }
                            printWriter.close();
                            String result = writer.toString();
                            sb.append(result);
                            Invoker.getInstance().navigation(ElkReportService.class).reportAction("business_trace", "H5页面跳转", sb.toString(), false);
                        });


    }

    @NonNull
    private static Bundle getBundle(String url,
                                    String urlKey,
                                    String fundCode,
                                    HashMap<String, String> forUrlMap,
                                    HashMap<String, Object> injectMap,
                                    Object[] args) {

        //某些特殊的url拼接，进行特殊处理
        url = SpecialUrlConditions(urlKey, url, fundCode, args);

        //定义一个变量来判断当前模块是需要某些标志(登录,绑卡)
        int navType = NAV_TO_WEBVIEW_TRADE; //默认值

        //根据每个KEY，处理对应的参数传递逻辑
        switch (urlKey) {
            case H5UrlKeyConfig.FUND_DETAIL_FROZEN:
                forUrlMap.put("fundCode", fundCode);
                break;
            case H5UrlKeyConfig.ASSETS_INCOME_DETAIL:
                if (args.length == 1) {
                    forUrlMap.put("fundCode", (String) args[0]);
                    forUrlMap.put("productType", "JJ");
                }
                navType = NAV_TO_WEBVIEW_TRADE;
                break;
            case H5UrlKeyConfig.SXY_LESSON_PROFILE:
                if (args != null && args.length == 1) {
                    forUrlMap.put("chapterNo", (String) args[0]);
                }
                navType = NAV_TO_WEBVIEW_NORMAL;
                break;
            case H5UrlKeyConfig.SELL_FUND_TAX: //个人养老基金卖出
                forUrlMap.put("fundCode", fundCode);
                if (args != null && args.length > 0) {
                    forUrlMap.put("fundShareClass", (String) args[0]);
                }
                break;
            case H5UrlKeyConfig.FUND_FIX_STOP: //止盈定投合约
            case H5UrlKeyConfig.FUND_FIX_NORMAL: //普通定投合约
                if (args != null && args.length > 0) {
                    forUrlMap.put("acctPlanId", (String) args[0]);
                }
                break;
            case H5UrlKeyConfig.TI_BUY_FUND: //买基金
            case H5UrlKeyConfig.TI_BUY_SIMU_FUND: //私募基金买入
            case H5UrlKeyConfig.TI_FIXED_INVESTMENT: //定投
                injectMap.put("fundCode", fundCode);
                if (args != null) {
                    if (args.length >= 1) {
                        injectMap.put("fundShareClass", args[0]);
                    }
                    if (args.length >= 2) {
                        injectMap.put("appSourcePage", args[1]);
                    }
                }
                break;
            case H5UrlKeyConfig.REGULAR_CALCULATOR:
                forUrlMap.put("fundCode", fundCode);
                if (args != null && args.length == 2) {//基金名称+是否止盈
                    forUrlMap.put("fundName", (String) args[0]);
                    forUrlMap.put("supportTarget", (String) args[1]);
                }
                break;
            case H5UrlKeyConfig.TI_CREATE_OBJSURPLUS:
            case H5UrlKeyConfig.TI_CREATE_MOVSURPLUS:
                if (args != null && args.length == 1) {
                    injectMap.put("fundShareClass", args[0]);
                }
            case H5UrlKeyConfig.RISK_HIGH_PROTOCOL:
                injectMap.put("fundCode", fundCode);
                break;
            case H5UrlKeyConfig.TI_CHANGE_OBJSURPLUS:
            case H5UrlKeyConfig.TI_CHNANGE_MOVSURPLUS:
                if (args != null && args.length == 1) {
                    injectMap.put("acctPlanId", args[0]);
                }
                break;
            case H5UrlKeyConfig.TI_SELL_FUND: //卖基金;
            case H5UrlKeyConfig.TI_QUICK_SELL: //快速卖出
                if (args != null && args.length == 1 && args[0] instanceof Map) {
                    Map rawMap = (Map) args[0];
                    if (!rawMap.isEmpty()) {
                        rawMap.keySet();
                        boolean isStringKey = true;
                        boolean isValueKey = true;
                        for (Object key : rawMap.keySet()) {
                            isStringKey = (key instanceof String);
                        }
                        for (Object value : rawMap.keySet()) {
                            isValueKey = value instanceof String;
                        }
                        if (isStringKey && isValueKey) {
                            //noinspection unchecked
                            injectMap.putAll(rawMap);
                        }
                    }
                } else {
                    if (args != null && args.length == 4) {
                        injectMap.put("protocalNo", args[0]);
                        injectMap.put("allowRedeemDt", args[1]);
                        injectMap.put("fundTxAcctNo", args[2]);
                        injectMap.put("fundShareClass", args[3]);
                    }
                    injectMap.put("fundCode", fundCode);
                }
                break;
            case H5UrlKeyConfig.NEW_APPOINT_SALE_FUND: // 预约卖出 修改预约
                if (args != null && args.length == 1 && args[0] instanceof Map) {
                    Map rawMap = (Map) args[0];
                    if (!rawMap.isEmpty()) {
                        rawMap.keySet();
                        boolean isStringKey = true;
                        boolean isValueKey = true;
                        for (Object key : rawMap.keySet()) {
                            isStringKey = (key instanceof String);
                        }
                        for (Object value : rawMap.keySet()) {
                            isValueKey = value instanceof String;
                        }
                        if (isStringKey && isValueKey) {
                            //noinspection unchecked
                            injectMap.putAll(rawMap);
                        }
                    }
                } else {
                    if (args != null) {
                        if (args.length == 4) {
                            injectMap.put("protocalNo", args[0]);
                            injectMap.put("planTime", args[1]);
                            injectMap.put("fundTxAcctNo", args[2]);
                            injectMap.put("fundShareClass", args[3]);
                        } else if (args.length == 6) {
                            injectMap.put("protocalNo", args[0]);
                            injectMap.put("planTime", args[1]);
                            injectMap.put("fundTxAcctNo", args[2]);
                            injectMap.put("fundShareClass", args[3]);
                            injectMap.put("planId", args[4]);
                            injectMap.put("redeemVol", args[5]);
                        }
                    }
                    injectMap.put("fundCode", fundCode);
                }
                break;
            case H5UrlKeyConfig.TI_FIXED_INVESTMENT_NEW:
            case H5UrlKeyConfig.TI_MODIFY_AIP_CONTRACT: //修改定投合约
                boolean isRobot = false;
                if (args != null && (args.length == 2 || args.length == 3)) {
                    for (Object arg : args) {
                        if (StrUtils.equals("1", arg)) {
                            isRobot = true;
                            break;
                        }
                    }
                    if (isRobot && args.length == 3) {
                        injectMap.put("acctPlanId", args[0]);
                        injectMap.put("contractFlag", args[1]);
                        injectMap.put("fundCode", args[2]);
                    } else {
                        injectMap.put("acctPlanId", args[0]);
                        injectMap.put("acctPlanNo", args[1]);
                    }
                }
                break;
            case H5UrlKeyConfig.PORTFOLIO_CHANGEPLAN:
            case H5UrlKeyConfig.SHORTBUND_FIXEDINVEST:
                if (args != null && args.length == 2) {
                    forUrlMap.put("productCode", (String) args[0]);
                    forUrlMap.put("planId", (String) args[1]);
                    forUrlMap.put("source", "3");
                }
                break;
            case H5UrlKeyConfig.TI_FUND_EXCHANGE: //基金转换
                if (args != null && args.length == 5) {
                    injectMap.put("protocalNo", args[0]); //投资协议号
                    injectMap.put("shareClass", args[1]); //份额类型
                    injectMap.put("fundTxAcctNo", args[2]); //基金交易账号
                    injectMap.put("allowRedeDt", args[3]);//理财型基金可赎回日期
                    injectMap.put("inputFundCode", args[4]);//转入基金代码 （基金转换）
                }
                injectMap.put("fundCode", fundCode);
                break;
            case H5UrlKeyConfig.FORWARD_PAGE:
                if (args != null && args.length == 5) {
                    forUrlMap.put("protocalNo", (String) args[0]); //投资协议号
                    forUrlMap.put("shareClass", (String) args[1]); //份额类型
                    forUrlMap.put("fundTxAcctNo", (String) args[2]); //基金交易账号
                    forUrlMap.put("allowRedeDt", (String) args[3]);//理财型基金可赎回日期
                    forUrlMap.put("inputFundCode", (String) args[4]);//转入基金代码 （基金转换）
                }
                forUrlMap.put("fundCode", fundCode);
                break;
            case H5UrlKeyConfig.TI_MODIFY_BONUS_METHOD: //修改分红方式
                if (args != null && args.length == 4) {
                    injectMap.put("fundName", args[0]); //基金名称
                    injectMap.put("curBounsType", args[1]); //分红方式
                    injectMap.put("hasRobot", args[2]); //分红方式
                    injectMap.put("fundShareClass", args[3]); //分红方式
                }
                injectMap.put("fundCode", fundCode);
                break;

            case H5UrlKeyConfig.TI_BATCH_EXCHANGE:
                if (args != null && args.length == 3) {
                    injectMap.put("transWay", args[0]); //转换方式
                    injectMap.put("holds", args[1]); //持仓
                    injectMap.put("trans", args[2]); //目标持仓
                }
                break;

            case H5UrlKeyConfig.TI_SAVE_MONEY: //存钱可以把金额带过去
                if (args != null && args.length == 1) {
                    injectMap.put("hqAmount", args[0]);
                }
                break;
            case H5UrlKeyConfig.NEW_APPOINT_LIST:
                injectMap.put("fundCode", fundCode);
                if (args != null && args.length == 2) {
                    injectMap.put("fundName", args[0]);
                    injectMap.put("fundShareClass", args[1]);
                }
                break;

            case H5UrlKeyConfig.TI_RISK_EVALUATION: //风险评测
                if (args != null && args.length == 2) {
                    injectMap.put("isShowResult", args[0]);//是否显示Kyc结果页面（0，不是；1，是）
                    injectMap.put("examType", args[1]);// 问卷类型（1=高端、2=零售），未知可不传
                }
                navType = NAV_TO_WEBVIEW_NORMAL;
                break;
            case H5UrlKeyConfig.PORTFOLIO_ASSET:
                if (args != null && args.length == 2) {
                    if (args[1] != null && args[1] instanceof Map) {
                        forUrlMap.putAll((Map<? extends String, ? extends String>) args[1]);
                    }
                }
                break;
            case H5UrlKeyConfig.PORTFOLIO_ASSET_NO_ACCESS:
                if (args != null && args.length == 2) {
                    if (args[1] != null && args[1] instanceof Map) {
                        forUrlMap.putAll((Map<? extends String, ? extends String>) args[1]);
                    }
                }
                navType = NAV_TO_WEBVIEW_NORMAL;
                break;
            case H5UrlKeyConfig.TI_ZH_TRADE_RECORD_NEW: //组合交易记录
                if (args != null) {
                    if (args.length == 2) {
                        //跳转到指定tab（通过链接传递参数）
                        forUrlMap.put("recordTab", (String) args[0]);
                        //组合订单号，筛选出单支组合的交易记录（通过链接传递参数）[注:这个单词定义的时候拼写错误,与之前的protocalNo不一样]
                        forUrlMap.put("protocolNo", (String) args[1]);
                        //跳转的来源
                        forUrlMap.put("desc", "zj");//这个参数直接写死zj(掌基)
                        forUrlMap.put("type", "pb");//这个参数直接写死zj(掌基)
                    } else if (args.length == 1) {
                        //组合订单号，筛选出单支组合的交易记录（通过]链接传递参数）[注:这个单词定义的时候拼写错误,与之前的protocalNo不一样]
                        forUrlMap.put("protocolNo", (String) args[0]);
                        //跳转的来源
                        forUrlMap.put("desc", "zj");//这个参数直接写死zj(掌基)
                        forUrlMap.put("type", "pb");//这个参数直接写死zj(掌基)
                    }
                }
                break;

            case H5UrlKeyConfig.MY_POLICY_SIMU_FUND://私募保单
                navType = NAV_TO_WEBVIEW_NORMAL;
                if (args != null && args.length == 1) {
                    forUrlMap.put("backdepth", (String) args[0]);
                }
                break;

            case H5UrlKeyConfig.TI_ORDER_SIMU_FUND: //私募 我的预约
            case H5UrlKeyConfig.TI_PGBK_TRADE_RECORD: //储蓄罐交易记录
            case H5UrlKeyConfig.TI_PGBK_TRADE_DETAIL: //储蓄罐交易详情
            case H5UrlKeyConfig.TI_MY_SIMU_FUND: //私募首页
            case H5UrlKeyConfig.NEW_TI_FIXED_PRODUCT_LIST: //固收产品列表
            case H5UrlKeyConfig.REGULAR_ASSIST: //定期持仓
            case H5UrlKeyConfig.HQPLUS_ASSIST: //活期+持仓
            case H5UrlKeyConfig.HAODOU_NEW: //好豆
            case H5UrlKeyConfig.INVITE_FRIENDS: //邀请好友
            case H5UrlKeyConfig.NEW_PUBLIC_NOTICE: //主备机房上线预埋公告页h5配置地址
            case H5UrlKeyConfig.NEW_REGULAR_LIST://定投合约
            case H5UrlKeyConfig.BUSINESSCOLLEGE_SEARCH:
            case H5UrlKeyConfig.MEMBER:
            case H5UrlKeyConfig.TI_MY_SIMU_EMPOWER:
            case H5UrlKeyConfig.FUND_PK_RESULT://公募基金PK
            case H5UrlKeyConfig.FUND_PK_INDEX://公募基金PK
            case H5UrlKeyConfig.HAOZHEN_OPEN_ACCOUNT://假开户页面
            case H5UrlKeyConfig.HAOZHEN_ACCOUNT_MESSAGE://好臻个人中心
            case H5UrlKeyConfig.ACCOUNT_MAIL_ADDRESS://收货地址
                navType = NAV_TO_WEBVIEW_NORMAL;
                break;
            case H5UrlKeyConfig.RISK_KYC_HZ://好臻合格投资者认定
            case H5UrlKeyConfig.RISK_GUIDE_HZ://好臻风险测评引导
                forUrlMap.put("riskType", "hz");
                forUrlMap.put("from", "app");
                navType = NAV_TO_WEBVIEW_NORMAL;
                break;
            case H5UrlKeyConfig.RISK_AUTH_HZ://好臻风险测评授权页
                forUrlMap.put("from", "app");
                navType = NAV_TO_WEBVIEW_NORMAL;
                break;
            case H5UrlKeyConfig.UPLOAD_IDENTITY_CARD://H5身份证OCR
                forUrlMap.put("type", "openAccountFromNative");
                if (args.length == 1 && args[0] instanceof Boolean) {
                    boolean isHz = (boolean) args[0];
                    forUrlMap.put("operCorpId", isHz? "000006":"");
                }
                navType = NAV_TO_WEBVIEW_NORMAL;
                break;
            case H5UrlKeyConfig.HELP_NEW: //帮助中心
                if (args != null && args.length == 2) {
                    forUrlMap.put("qType", (String) args[0]);
                    forUrlMap.put("qTitle", (String) args[1]);
                }
                navType = NAV_TO_WEBVIEW_NORMAL;
                break;
            case H5UrlKeyConfig.NEW_POPUP_TIP: //H5弹出框
                if (args != null && args.length == 1) {
                    forUrlMap.put("tipId", (String) args[0]);
                }
                navType = NAV_TO_WEBVIEW_NORMAL;
                break;
            case H5UrlKeyConfig.TI_HBFD_TRADE_RECORD: //掌机交易记录
                //基金代码，筛选出单支基金的交易记录（通过链接传递参数）
                if (!StrUtils.isEmpty(fundCode)) {
                    forUrlMap.put("fundCode", fundCode);
                }
                if (args != null && args.length == 1) {
                    forUrlMap.put("confirmType", (String) args[0]);
                }
                navType = NAV_TO_WEBVIEW_NORMAL;
                break;
            case H5UrlKeyConfig.NEW_DT_COMPUTE_STATEMENT:
                forUrlMap.put("fundCode", fundCode);
                navType = NAV_TO_WEBVIEW_NORMAL;
                break;
            //定期, 活期+ 产品档案页面
            case H5UrlKeyConfig.REGULAR_DETAIL:
            case H5UrlKeyConfig.HQPLUS_DETAIL:
                forUrlMap.put("productId", fundCode);
                if (args != null && args.length == 1) {
                    //产品类型 1 金交所、2  保一、3 保二、4 券商、5 45天滚动产品、6 活期+、7中证
                    forUrlMap.put("type", (String) args[0]);
                }
                navType = NAV_TO_WEBVIEW_NORMAL;
                break;
            case H5UrlKeyConfig.SUPER_HOUSE:
                if (args != null && args.length == 1) {
                    forUrlMap.put("protocolNo", (String) args[0]);
                }
                navType = NAV_TO_WEBVIEW_NORMAL;
                break;
            case H5UrlKeyConfig.SHORT_H5_KEY:
                if (args != null && args.length == 2) {
                    if (args[1] != null && args[1] instanceof String) {
                        try {
                            Gson gson = new Gson();
                            HashMap<String, String> tempMap = gson.fromJson((String) args[1], HashMap.class);
                            if (tempMap != null) {
                                String loginStatus = tempMap.get("login");
                                Object urlParams = tempMap.get("urlParams");
                                if (!StrUtils.equals("1", loginStatus)) {
                                    navType = NAV_TO_WEBVIEW_NORMAL;
                                } else {
                                    navType = NAV_TO_WEBVIEW_LOGIN;
                                }
                                if (urlParams != null) {
                                    forUrlMap.putAll((Map<? extends String, ? extends String>) urlParams);
                                }
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
                break;
            case H5UrlKeyConfig.ZJZH_PRODUCT_DETAIL:
                if (args != null && args.length == 4) {
                    forUrlMap.put("zhid", (String) args[0]);
                    forUrlMap.put("zhmc", (String) args[1]);
                    forUrlMap.put("hbdr", (String) args[2]);
                    forUrlMap.put("income", (String) args[3]);
                }
                navType = NAV_TO_WEBVIEW_NORMAL;
                break;
            case H5UrlKeyConfig.ZJZH_ASSET_DETAIL:
                if (args != null && args.length == 2) {
                    forUrlMap.put("cpId", (String) args[0]);
                    forUrlMap.put("productNo", (String) args[1]);
                }
                navType = NAV_TO_WEBVIEW_NORMAL;
                break;
            case H5UrlKeyConfig.TZZH_PRODUCT_DETAIL:
            case H5UrlKeyConfig.CREATE_CONTRACT:
            case H5UrlKeyConfig.ADVISER_BUY:
                if (args != null && args.length == 1) {
                    forUrlMap.put("productCode", (String) args[0]);
                }
                navType = NAV_TO_WEBVIEW_NORMAL;
                break;
            case H5UrlKeyConfig.TZZH_ASSET_DETAIL:
                if (args != null && args.length == 2) {
                    forUrlMap.put("productCode", (String) args[0]);
                    forUrlMap.put("productNo", (String) args[1]);
                }
                navType = NAV_TO_WEBVIEW_NORMAL;
                break;
            case H5UrlKeyConfig.CONTRACT_DETAIL:
                if (args != null && args.length == 1) {
                    forUrlMap.put("scheId", (String) args[0]);
                }
                navType = NAV_TO_WEBVIEW_NORMAL;
                break;
            case H5UrlKeyConfig.ADVISER_SELL:
                if (args != null && args.length == 2) {
                    forUrlMap.put("productCode", (String) args[0]);
                    forUrlMap.put("protocolNo", (String) args[1]);
                }
                navType = NAV_TO_WEBVIEW_NORMAL;
                break;
            case H5UrlKeyConfig.TI_DRAW_MONEY:
                if (args != null && args.length == 1) {
                    forUrlMap.put("custBankId", (String) args[0]);
                }
                break;
            case H5UrlKeyConfig.FUND_HOLD_SHARES:
                forUrlMap.put("fundCode", fundCode);
                if (args != null && args.length > 3) {
                    //份额类型
                    forUrlMap.put("fundShareClass", (String) args[0]);
                    //是否是税延基金 1是其他不是
                    forUrlMap.put("isTax", (String) args[1]);
                    //是否已经升级 1是
                    forUrlMap.put("isUpGrade", (String) args[2]);
                    //是否是循环锁定期基金 1是
                    forUrlMap.put("hasLockPeriodYes", (String) args[3]);
                }
                break;
            case H5UrlKeyConfig.SELL_FUND_CONVERT:
                if (args != null && args.length == 4) {
                    forUrlMap.put("protocalNo", (String) args[0]); //投资协议号
                    forUrlMap.put("fundShareClass", (String) args[1]); //份额类型
                    forUrlMap.put("fundTxAcctNo", (String) args[2]); //基金交易账号
                    forUrlMap.put("allowRedeDt", (String) args[3]);//理财型基金可赎回日期
                }
                forUrlMap.put("fundCode", fundCode);
                break;
            case H5UrlKeyConfig.SELL_CONVERT_FUND:
                if (args != null && args.length == 6) {
                    forUrlMap.put("protocalNo", (String) args[0]); //投资协议号
                    forUrlMap.put("fundShareClass", (String) args[1]); //份额类型
                    forUrlMap.put("fundTxAcctNo", (String) args[2]); //基金交易账号
                    forUrlMap.put("allowRedeDt", (String) args[3]);//理财型基金可赎回日期
                    forUrlMap.put("inputProductCode", (String) args[4]);//转入基金代码 （基金转换）
                    forUrlMap.put("inputProductType", (String) args[5]);// 转入产品类型
                }
                forUrlMap.put("fundCode", fundCode);
                break;
            case H5UrlKeyConfig.TO_EXCHANGE_PORTION:
                if (args != null && args.length == 2) {
                    forUrlMap.put("changeType", (String) args[0]);
                    forUrlMap.put("fundShareClass", (String) args[1]);
                }
                forUrlMap.put("fundCode", fundCode);
                break;
            case H5UrlKeyConfig.ZJZH_CREATE_PROCESS:
                //noinspection DuplicateBranchesInSwitch
                navType = NAV_TO_WEBVIEW_NORMAL; //创建组合去除实名激活等检查
                break;
            case H5UrlKeyConfig.TO_WXPROMOTE_ACTIVITY_HOME:
                if (args != null && args.length == 1) {
                    forUrlMap.put("fromLogin", (String) args[0]); //投资协议号
                }
                break;
            case H5UrlKeyConfig.CCB_SIGN:
                if (args != null) {
                    //custBankId, bankCode, bankName, channelId
                    forUrlMap.put("custBankId", (String) args[0]);
                    forUrlMap.put("bankCode", (String) args[1]);
                    forUrlMap.put("bankName", (String) args[2]);
                    forUrlMap.put("channelId", (String) args[3]);
                }
                break;
            case H5UrlKeyConfig.ORDER_OF_DEDUCTION:
                if (args != null && args.length >= 1) {
                    forUrlMap.put("animation", (String) args[0]);
                }
                break;
            case H5UrlKeyConfig.COLLEGE_USER_LEVEL:
                if (args != null && args.length >= 1) {
                    forUrlMap.put("level", (String) args[0]);
                }
                navType = NAV_TO_WEBVIEW_NORMAL;
                break;
            case H5UrlKeyConfig.FUND_DIAGS_DETAIL: //基金诊断结果页
                forUrlMap.put("jjdm", fundCode);
                navType = NAV_TO_WEBVIEW_NORMAL;
                break;
            case H5UrlKeyConfig.TI_MY_SIMU_FUNDDETAIL://私募单基金持仓页-阳光私募
                //fundCode：基金code；
                //productsubtype：基金子类型；4 阳光私募
                //hkSaleFlag：是否香港基金；1 是 0 否
                //sfhwcxg：是否海外储蓄罐；1 是 0 否
                //disCode：基金分销渠道    HB000A001-好买   HZ开头-好臻
                forUrlMap.put("fundcode", fundCode);
                forUrlMap.put("val", "parent");
                forUrlMap.put("productsubtype", "4");
                forUrlMap.put("hkSaleFlag", "0");
                forUrlMap.put("sfhwcxg", "0");
                forUrlMap.put("disCode", "HB000A001");
                navType = NAV_TO_WEBVIEW_NORMAL;
                break;
            case H5UrlKeyConfig.HAOZHEN_TRADE_ORDER://好臻下单页
                forUrlMap.put("productCode", fundCode);
                navType = NAV_TO_WEBVIEW_NORMAL;
                break;
            case H5UrlKeyConfig.SIGNAL_REMINDER_SETTING: //基金-设置提醒 8.5.0
                String productCode = null;
                if (args.length >= 1) {
                    productCode = (String) args[0];
                    forUrlMap.put("productCode", (String) args[0]);
                }
                LogUtils.d("Remind-fund", "h5 navigate , productCode:" + productCode);
                navType = NAV_TO_WEBVIEW_NORMAL;
            case H5UrlKeyConfig.FUND_SELL_FOR_LOCKER:
                String jjdm = null;
                if (args.length >= 1) {
                    jjdm = (String) args[0];
                    forUrlMap.put("fundCode", (String) args[0]);
                }
                LogUtils.d("CycleLock", "h5 navigate , fundCode:" + jjdm + ", url:" + url);
            default:
                break;
        }

        //将参数为Map的，拆解为单个字段，并保存到url参数map中
        flatMap(injectMap, forUrlMap);

        //通过在url后面拼接参数,forurlMap有参数有拼接,没有就返回原来的字符串
        try {
            String params = UrlUtils.getUrlParams(forUrlMap);
            url = joinUrlParams(url, params);
        } catch (Exception e) {
            e.printStackTrace();
        }

        LogUtils.d(TradeH5Dispatcher.class.getSimpleName(), "跳转h5 --forUrlMap = " + forUrlMap + " 跳转地址 = " + url);

        //处理返回按钮、导航栏的显示隐藏逻辑
        Bundle bundle = NavHelper.obtainArg("", ValConfig.IT_URL, url);
        if (StrUtils.equals(H5UrlKeyConfig.TI_HBFD_TRADE_RECORD, urlKey)) {
            if (!TextUtils.isEmpty(url) && url.contains(Html5FileDescript.EntrancesUrlDes.VALUE_ISHIDDENNAV)) {
                bundle.putBoolean(ValConfig.IT_WEB_BACK_BTN, false);
            }
        } else if (StrUtils.equals(urlKey, H5UrlKeyConfig.HELP_NEW)) { //跳转到帮助中心，去除原生导航栏，添加圆形返回键
            bundle.putBoolean(ValConfig.IT_TYPE, false);
            bundle.putBoolean(ValConfig.IT_WEB_BACK_BTN, true);
        } else if (TextUtils.equals(urlKey, H5UrlKeyConfig.HAOZHEN_ACCOUNT_MESSAGE)) {
            //好臻个人中心需要拦截显示中转页
            bundle.putBoolean(ValConfig.IT_TARGET_STOCK, true);
            bundle.putBoolean(ValConfig.IT_TARGET_HZ_HEGUI_IGNORE, true);
        }

        switch (navType) {
            case NAV_TO_WEBVIEW_NORMAL:
                //正常不添加参数
                break;
            case NAV_TO_WEBVIEW_TRADE:
                bundle.putIntArray(ValConfig.IT_INTERCEPT_CODE, IInterceptCode.ARRAY_TRADE_CODE);
                break;
            case NAV_TO_WEBVIEW_LOGIN:
                bundle.putIntArray(ValConfig.IT_INTERCEPT_CODE, new int[]{IInterceptCode.CHECK_LOGIN});
                break;
        }

        return bundle;
    }

    /**
     * 封装url解析逻辑
     *
     * @param urlKey key
     * @param args   参数
     * @return 解析后的url链接
     */
    private static Observable<String> parseUrl(String urlKey, Object[] args) {
//        String url;
        H5UrlModifyStrategy strategy = urlKeyPickStrategy.get(urlKey);
        if (strategy instanceof ParseUrlWithFirstParameter && args != null && args.length > 0) {
            urlKey = strategy.modify(urlKey, null, args);
        }
        return ParserUriZipResource.parseKey(urlKey);
    }

    /**
     * 跳转webview页面,有两种样式,一种普通的h5页面,还有一种H5弹框样式,
     * h5弹框样式需要本地使用一个透明的Activity加载
     */
    private static void launcherToWeb(Object cx, String urlKey, Bundle bundle, NavCompleteCallback callback) {
        if (StrUtils.equals(urlKey, H5UrlKeyConfig.NEW_POPUP_TIP) || StrUtils.equals(urlKey, H5UrlKeyConfig.NEW_NEWUSER_DIALOG)) {
            if (bundle != null) {
                bundle.putBoolean(AbsFragWebView.WEBVIEW_POP, true);//并且不显示
                bundle.putInt(ValConfig.IT_THEME, R.style.Theme_fund_ransparent);
            }
            RouterHelper.launchFrag(cx, AtyRouterPath.PATH_ATY_H5_DIALOG_NO_STATE_BAR, WebRouterPath.PATH_FRAG_WEBVIEW, bundle, 0, 0);
        } else {
            ApiHelperKt.get(IWebProvider.class).launchWebView(cx, bundle, callback);
        }
    }

    /**
     * 处理特殊情况下 URL
     */
    private static String SpecialUrlConditions(String urlKey, String url, String fundCode, Object... args) {
        H5UrlModifyStrategy strategy = urlModifyStrategy.get(urlKey);
        if (null != strategy && null != url) {
            return strategy.modify(url, fundCode, args);
        }
        return url;
    }


    /**
     * 拼接h5的key对应url和参数
     *
     * @param urlOrigin
     * @param h5UrlParam
     * @return
     */
    public static String joinUrlParams(String urlOrigin, String h5UrlParam) {
        if (!StrUtils.isEmpty(h5UrlParam) && h5UrlParam.startsWith("?")) {
            h5UrlParam = h5UrlParam.substring(1);
        }
        if (!StrUtils.isEmpty(urlOrigin)) {
            if (urlOrigin.contains("?#")) {
                String[] endUrl = urlOrigin.split("\\?#");
                String lastendUrl = "";
                if (endUrl != null && endUrl.length > 0) {
                    lastendUrl = endUrl[endUrl.length - 1];
                }
                if (!StrUtils.isEmpty(h5UrlParam)) {
                    if (lastendUrl.endsWith("?")) {//是否问号结尾
                        urlOrigin = urlOrigin + h5UrlParam;
                    } else if (lastendUrl.contains("?")) {//中间包含问号
                        urlOrigin = urlOrigin + "&" + h5UrlParam;
                    } else {
                        urlOrigin = urlOrigin + "?" + h5UrlParam;
                    }
                }
            } else if (!StrUtils.isEmpty(h5UrlParam)) {
                if (urlOrigin.endsWith("?")) {//是否问号结尾
                    urlOrigin = urlOrigin + h5UrlParam;
                } else if (urlOrigin.contains("?")) {//中间包含问号
                    urlOrigin = urlOrigin + "&" + h5UrlParam;
                } else {
                    urlOrigin = urlOrigin + "?" + h5UrlParam;
                }
            }
        }
        return urlOrigin;
    }

    /**
     * 将map中的value为Map类型的数据展开
     *
     * @param mapOrigin 需要展开的map
     * @param target    目标map
     */
    public static void flatMap(@NonNull Map<String, Object> mapOrigin, @NonNull Map<String, String> target) {
        if (mapOrigin.isEmpty()) return;
        for (Map.Entry<String, Object> entry : mapOrigin.entrySet()) {
            Object value = entry.getValue();
            if (value instanceof String) {
                target.put(entry.getKey(), (String) value);
            } else if (value instanceof Map) {
                //noinspection unchecked
                flatMap((Map<String, Object>) value, target);
            } else {
                LogUtils.e("DebugH5Params", "params value type is not String or Map<String, String>");
            }
        }
    }
}
