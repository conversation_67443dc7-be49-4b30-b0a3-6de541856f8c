package com.howbuy.fund.common;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Message;
import android.text.TextUtils;
import android.webkit.WebView;

import androidx.annotation.Nullable;
import androidx.core.util.Consumer;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.google.gson.GsonUtils;
import com.google.gson.reflect.TypeToken;
import com.howbuy.base.entity.FundInfoItem;
import com.howbuy.fund.base.CommentType;
import com.howbuy.fund.base.aty.AtyEmpty;
import com.howbuy.fund.base.config.ValConfig;
import com.howbuy.fund.base.dialog.DialogUpdateApp;
import com.howbuy.fund.base.entity.CollectionBean;
import com.howbuy.fund.base.frag.AbsHbFrag;
import com.howbuy.fund.base.nav.NavCompleteCallback;
import com.howbuy.fund.base.nav.NavHelper;
import com.howbuy.fund.base.push.PushDispatch;
import com.howbuy.fund.base.push.PushDispatchHelper;
import com.howbuy.fund.base.router.RouterHelper;
import com.howbuy.fund.base.router.fragpath.CommonRouterPath;
import com.howbuy.fund.base.utils.SimpleSingleThread;
import com.howbuy.fund.common.audio.ThirdPartAudioFocusManager;
import com.howbuy.fund.common.comment.ReplyCommentHelper;
import com.howbuy.fund.common.entity.FundCommentActionBean;
import com.howbuy.fund.common.gray.H5RemoteHelper;
import com.howbuy.fund.common.gray.NoFastClickFeature;
import com.howbuy.fund.common.information.InfoHelper;
import com.howbuy.fund.common.upgrade.InitHelper;
import com.howbuy.fund.storage.FundData;
import com.howbuy.lib.aty.AtyMgr;
import com.howbuy.lib.compont.GlobalApp;
import com.howbuy.lib.utils.ContextUtil;
import com.howbuy.lib.utils.LogUtils;
import com.howbuy.lib.utils.StrUtils;
import com.howbuy.login.api.LoginParams;
import com.howbuy.login.api.LoginService;
import com.howbuy.login.api.LoginType;
import com.howbuy.router.path.UserRouterPath;
import com.howbuy.router.provider.ICommonProvider;
import com.howbuy.router.provider.IUserProvider;
import com.howbuy.router.provider.IWebProvider;
import com.howbuy.router.proxy.Invoker;
import com.howbuy.userdata.business.ApiHelperKt;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import html5.AbsFragWebView;
import html5.WebViewUtils;
import html5.action.HandleCallbackHelper;
import html5.action.ParamsMessage;
import io.reactivex.Observable;

/**
 * Create by zsm on 2019/1/7.
 **/
@Route(path = "/common/provider")
public class CommonProvider implements ICommonProvider {

    /**
     * use {@link #launchTradeH5WithCallback} instead.
     * 该方式跳转到H5页面,返回原生页面后, 无法感知是否需要刷新当前页面
     */
    @Deprecated
    @Override
    public void launchTradeH5(Object cx, String tradeType, Object fundCodeOrBean,
                              String from, Object... bundArg) {
        TradeH5Dispatcher.launchTradeH5(cx, tradeType, fundCodeOrBean, from, bundArg);
    }

    @Override
    public Observable<Bundle> buildH5Params(Object cx, String tradeType, Object fundCodeOrBean, String from, Object... bundArg) {
        return TradeH5Dispatcher.buildTradeH5(cx, tradeType, fundCodeOrBean, from, bundArg);
    }

    @Override
    public void launchTradeH5WithCallback(Object cx, String tradeType, NavCompleteCallback callback, Object fundCodeOrBean, String from, Object... bundArg) {
        TradeH5Dispatcher.launchTradeH5WithCallback(cx, tradeType, callback, fundCodeOrBean, from, bundArg);
    }

    @Override
    public void syncCollectionFromDbToServer(Object items) {
        InfoHelper.syncCollectionFromDbToServer((List<FundInfoItem>) items);
    }

    @Override
    public void launchComment(Object fragOrAty, String itemId, String itemType, String theme,
                              String itemSource, String subItemSource) {
        Bundle bundle = NavHelper.obtainArg("全部评论",
                ValConfig.IT_ID, itemId,
                ValConfig.IT_TYPE, itemType,
                ReplyCommentHelper.KEY_THEME, theme);
        if (StrUtils.equals(itemSource, CommentType.COMMENT_TYPE_SM_AUDIO.getCode())) {
            bundle.putString(ReplyCommentHelper.KEY_SUBSOURCE_TYPE, subItemSource);
        } else {
            bundle.putString(ReplyCommentHelper.KEY_SUBSOURCE_ID, subItemSource);
        }
        RouterHelper.launchFrag(fragOrAty, CommonRouterPath.FRAG_COMMENT_LIST_NEW, bundle);
    }

    @Override
    public void launchComment(Object fragOrAty, String commentStr) {
        //外部跳转页面跳转过来
        try {
            FundCommentActionBean comment = GsonUtils.toObj(commentStr, FundCommentActionBean.class);
            Bundle bundle = NavHelper.obtainArg("全部评论",
                    ValConfig.IT_ID, comment.getContentId(),
                    ValConfig.IT_TYPE, comment.getSourceId(),
                    ReplyCommentHelper.KEY_THEME, comment.getTheme() == null ? comment.getTitle() : comment.getTheme(),
                    ReplyCommentHelper.KEY_SUBSOURCE_ID, comment.getSubSourceId(),
                    ReplyCommentHelper.KEY_REPLY_COMMENT_ID, comment.getReplyCommentId(),
                    ReplyCommentHelper.KEY_SUBSOURCE_TYPE, comment.getSubSourceType());
            RouterHelper.launchFrag(fragOrAty, CommonRouterPath.FRAG_COMMENT_LIST_NEW, bundle, NavHelper.REQ_CODE_OTHER);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public String getInfoUrl(String infoId, int infoType) {
        int type = infoType;
        if (infoType == 1) {
            type = InfoHelper.ARTICLA_NEWS;
        } else if (infoType == 2) {
            type = InfoHelper.ARTICLA_RESEARCH;
        }
        return InfoHelper.getInfoUrl(infoId, type);
    }

    @Override
    public void init(Context context) {
    }

    @Override
    public void showQuickComment(Fragment fragment, String param, final WebView mWebView, final Object pm) {
        try {
            FundCommentActionBean commentActionBea = GsonUtils.toObj(param, FundCommentActionBean.class);
            ReplyCommentHelper.showQuickComment((AbsHbFrag) fragment, commentActionBea, new ReplyCommentHelper.InputCommentListener() {
                @Override
                public void onCommitComment(String content) {
                    //LogUtils.pop("提交成功，审核通过后即可对外展示");
                    WebViewUtils.loadJs(mWebView, ((ParamsMessage) pm).getCallback());
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void handleComments(Message msg, Fragment mFragment) {
        ParamsMessage pm = (ParamsMessage) msg.obj;
        try {
            FundCommentActionBean comment = GsonUtils.toObj(pm.getParams(), FundCommentActionBean.class);
            Bundle bundle = NavHelper.obtainArg("全部评论",
                    ValConfig.IT_ID, comment.getContentId(),
                    ValConfig.IT_TYPE, comment.getSourceId(),
                    ReplyCommentHelper.KEY_THEME, comment.getTheme() == null ? comment.getTitle() : comment.getTheme(),
                    ReplyCommentHelper.KEY_SUBSOURCE_ID, comment.getSubSourceId(),
                    ReplyCommentHelper.KEY_REPLY_COMMENT_ID, comment.getReplyCommentId(),
                    ReplyCommentHelper.KEY_SUBSOURCE_TYPE, comment.getSubSourceType());

            RouterHelper.launchFrag(mFragment, CommonRouterPath.FRAG_COMMENT_LIST_NEW, bundle, NavHelper.REQ_CODE_OTHER);
        } catch (Exception e) {
            e.printStackTrace();
            LogUtils.d(WebViewUtils.TAG, "handleComments--》" + "参数解析错误");
        }
    }

    @Override
    public void handlerAddNewsOptional(Message msg, Fragment mFragment, final WebView mWebview) {
        if (msg.obj == null) {
            return;
        }
        if (ApiHelperKt.isLogined()) {
            ParamsMessage pm = (ParamsMessage) msg.obj;
            String params = pm.getParams();
            final String cb = pm.getCallback();
            if (!StrUtils.isEmpty(params)) {
                try {
                    final FundInfoItem fundInfoItem = GsonUtils.toObj(params, FundInfoItem.class);
                    InfoHelper.fundInfoItemDownToUp(fundInfoItem);
                    SimpleSingleThread.getInstance().execCache(new Runnable() {
                        @Override
                        public void run() {
                            if (fundInfoItem != null) {
                                String actionType = fundInfoItem.getActionType();
                                if (StrUtils.equals("1", actionType)) {
                                    //添加收藏
                                    InfoHelper.saveCollection(fundInfoItem, new InfoHelper.IFavCallBack() {
                                        @Override
                                        public void dealOver(boolean isSuccessful) {
                                            if (isSuccessful) {
                                                HandleCallbackHelper.callJsFunc(HandleCallbackHelper.CODE_SUCCESS, null, cb, mWebview, null);
                                                LogUtils.pop("收藏成功");
                                            } else {
                                                HandleCallbackHelper.callJsFunc(HandleCallbackHelper.CODE_FAIL, null, cb, mWebview, null);
                                            }
                                        }
                                    });
                                } else {
                                    //取消收藏
                                    List<FundInfoItem> removeList = new ArrayList<>(1);
                                    removeList.add(fundInfoItem);
                                    InfoHelper.deleteCollection(removeList, new InfoHelper.IFavCallBack() {
                                        @Override
                                        public void dealOver(boolean isSuccessful) {
                                            if (isSuccessful) {
                                                HandleCallbackHelper.callJsFunc(HandleCallbackHelper.CODE_SUCCESS, null, cb, mWebview, null);
                                                LogUtils.pop("取消收藏");
                                            } else {
                                                HandleCallbackHelper.callJsFunc(HandleCallbackHelper.CODE_FAIL, null, cb, mWebview, null);
                                            }
                                        }
                                    });
                                }
                            }
                        }
                    });
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        } else {
            ApiHelperKt.get(ICommonProvider.class).launchLogin(mFragment, false, null, null);
        }
    }

    @Override
    public void handlerIsCollection(Message msg, final WebView mWebview) {
        if (msg.obj == null) {
            return;
        }
        ParamsMessage pm = (ParamsMessage) msg.obj;
        String params = pm.getParams();
        final String cb = pm.getCallback();

        List<CollectionBean> listData = GsonUtils.toObj(params,
                new TypeToken<List<CollectionBean>>() {
                }.getType());

        if (listData != null && listData.size() > 0) {
            final CollectionBean item = listData.get(0);
            String itemId = item.getItemId();
            String sourceType = item.getSourceType();
            InfoHelper.isCollected(itemId, sourceType, new InfoHelper.IFavCallBack() {
                @Override
                public void dealOver(boolean isSuccessful) {
                    ArrayList<CollectionBean> result = new ArrayList<CollectionBean>();
                    if (isSuccessful) {
                        result.add(item);
                    }
                    HashMap<String, Object> dataMap = new HashMap<String, Object>();
                    dataMap.put("collected", result);
                    HandleCallbackHelper.callJsFunc(HandleCallbackHelper.CODE_SUCCESS, null, cb, mWebview, dataMap);
                }
            });
        }
    }

    @Override
    public String joinUrlParams(String urlOrigin, String h5UrlParam) {
        return TradeH5Dispatcher.joinUrlParams(urlOrigin, h5UrlParam);
    }

    @Override
    public void doUpdate() {
        boolean isFirstStartApp = InitHelper.isFirstStart();
        if (!isFirstStartApp && InitHelper.isNeedUpdate()) {
            InitHelper.doUpdate();
        }
    }

    @Override
    public void execFileOpt() {
        InitHelper initHelper = new InitHelper(GlobalApp.getApp());
        initHelper.doTask();
    }


    @Override
    public void requestGmGrayList() {
        //登录后调用
        CommonInfoManager.requestGmGrayList("2");
    }

    @Override
    public boolean hasAudioGain() {
        return ThirdPartAudioFocusManager.getInstance().hasAudioGain();
    }

    @Override
    public void fixH5Res(Context context, Callback callback) {
        InitHelper initHelper = new InitHelper(context);
        initHelper.doFixH5Res(callback);
    }

    @Override
    public boolean isFastClickFeatureNotEnable() {
        return !NoFastClickFeature.getSwitchOn();
    }


    @Override
    public String getRemoteUrlIfMatched(String oldH5Path) {
        return H5RemoteHelper.Companion.getInstance().getRemoteUrl(oldH5Path);
    }

    @Override
    public void launchBigImagePreview(Context context, String url, int drawableRes) {
        if (context == null) {
            return;
        }

        ARouter.getInstance()
                .build(UserRouterPath.PATH_ACTIVITY_BIT_IMAGE_PREVIEW)
                .withString("url", url)
                .withInt("drawableResId", drawableRes)
                .navigation(context);
        if (context instanceof Activity){
            ((Activity)context).overridePendingTransition(0, 0);
        }
    }

    @Override
    public void showValidCmdDlg(FragmentManager fm) {
        DialogUpdateApp.newInstance().show(fm, null);
    }

    @Override
    public void removeCacheAndFile(String cacheKey) {
        FundData.getData().removeCacheAndFile(cacheKey);
    }

    @Override
    public String getCache(String key) {
        return FundData.getData().getCache(key);
    }

    @Override
    public void saveToCache(String value, String netUrl) {
        FundData.getData().saveToCache(value, netUrl);
    }

    @Override
    public void saveToCache(Object value, String netUrl, boolean updateMemoryCache) {
        FundData.getData().saveToCache(value, netUrl, updateMemoryCache);
    }

    @Override
    public void clearAllUserCache() {
        FundData.getData().clearAllUserCache();
    }

    @Override
    public void launcherToWebByCmd(Context context, String cmdOrUrl, String msg, boolean needTitle) {
        if (!TextUtils.isEmpty(cmdOrUrl)) {
            IWebProvider webProvider = Invoker.getInstance().navigation(IWebProvider.class);
            if (webProvider.isValidUri(cmdOrUrl)) {
                Bundle b = NavHelper.obtainArg(msg, ValConfig.IT_URL, cmdOrUrl, ValConfig.IT_TYPE, needTitle);
                ApiHelperKt.get(IWebProvider.class).launchWebView(context, b, null);
            } else if (cmdOrUrl.startsWith("/wapapp/")) {
                //本地命令
                Bundle b = NavHelper.obtainArg(msg, ValConfig.IT_URL, webProvider.joinLocalUrl(cmdOrUrl));
                ApiHelperKt.get(IWebProvider.class).launchWebView(context, b, null);
            } else {
                PushDispatchHelper.dispatchEvent(context, cmdOrUrl, msg, needTitle);
                new PushDispatch(context).dispatchEvent(cmd, msg, needTitle);
            }
        }
    }

    @Override
    public void launchLogin(Object fragOrAty, boolean toCaptcha, Bundle bundle, @Nullable Consumer<Boolean> loginResultConsume) {
        if (ApiHelperKt.isLogined()) {
            if (loginResultConsume != null) {
                loginResultConsume.accept(true);
            }
            return;
        }
        Context context = ContextUtil.getContext(fragOrAty);
        if (!(context instanceof Activity)) {
            if (loginResultConsume != null) {
                loginResultConsume.accept(false);
            }
            return;
        }
        LoginType loginType = toCaptcha? LoginType.captcha : LoginType.password;
        if (Invoker.getInstance().navigation(IUserProvider.class).needLoginBridge()) {
            loginType = null;
        }
        ApiHelperKt.get(LoginService.class).login(new LoginParams((Activity) context, loginType, bundle), result -> {
            //登录有登录成功,登录失败(是页面中的提示状态),跟登录取消(页面销毁的回调状态)
            if (loginResultConsume != null) {
                //登录失败不用回调,登录页面销毁只有两种结果,登录成功,登录取消
                if (result.getSuccess()) {
                    loginResultConsume.accept(result.getSuccess());
                } else if (result.getCancelled()) {
                    loginResultConsume.accept(false);
                }

            }
        });
    }

    @Override
    public void launchRegister(Object fragOrAty,boolean toCaptcha, Bundle bundle, Consumer<Boolean> loginResultConsume) {
        Context context = ContextUtil.getContext(fragOrAty);
        if (!(context instanceof Activity)) {
            if (loginResultConsume != null) {
                loginResultConsume.accept(false);
            }
            return;
        }
        LoginType loginType = toCaptcha? LoginType.captcha : LoginType.password;
        if (Invoker.getInstance().navigation(IUserProvider.class).needLoginBridge()) {
            loginType = null;
        }
        ApiHelperKt.get(LoginService.class).login(new LoginParams((Activity) context, loginType, bundle), result -> {
            if (loginResultConsume != null) {
                loginResultConsume.accept(result.getSuccess());
            }
        });
    }

    @Override
    public String getCurrentShowPage() {
        String result;
        Activity topActivity = AtyMgr.getFirstExistAty();
        boolean atyEmptyInstance = topActivity instanceof AtyEmpty;
        if (atyEmptyInstance) {
            Fragment fragment = ((AtyEmpty) topActivity).getCurrentFragment();
            if (fragment instanceof AbsHbFrag) {
                boolean isWebPage = fragment instanceof AbsFragWebView;
                if (isWebPage) {
                    Intent intent = topActivity.getIntent();
                    Bundle pageBundle = intent == null ? null : intent.getBundleExtra(RouterHelper.KEY_FRAG_ARG);
                    result = pageBundle == null ? "" : pageBundle.getString(ValConfig.IT_URL);
                } else {
                    result = fragment.getClass().getName();
                }
            } else {
                //普通fragment直接取name
                result = fragment == null ? "" : fragment.getClass().getName();
            }
        } else {
            result = topActivity == null ? "" : topActivity.getClass().getName();
        }
        LogUtils.d("FragTest", "当前fragmentName = " + result);
        return result;
    }
}
