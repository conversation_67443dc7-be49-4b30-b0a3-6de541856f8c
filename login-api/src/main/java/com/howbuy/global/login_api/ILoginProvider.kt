package com.howbuy.global.login_api

import android.app.Activity
import android.content.Context
import android.os.Bundle
import android.widget.TextView
import androidx.core.util.Consumer
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import com.alibaba.android.arouter.facade.callback.NavigationCallback
import com.alibaba.android.arouter.facade.template.IProvider
import com.howbuy.fund.base.nav.NavCompleteCallback
import com.howbuy.gesture.consumer.FingerResultConsumer

/**
 * description.
 * tao.liang
 * 2024/2/26
 */
interface ILoginProvider : IProvider {


    /**
     * 从本地sf中获取登录用户信息(杀进程,启动app,需要从这里获取之前用户的登录状态数据)
     * @param fromMemory true:从内存中获取数据对象; false:从sf中获取数据
     */
    fun getLoginInfo(fromMemory: Boolean): LoginTypeInf?

    fun saveLoginInfo(
        hkCustNo: String,
        loginType: LoginType,
        accountType: String,
        acountNo: String,
        phoneAreaCode: String
    )

    /**
     * 客户号
     */
    fun getHkCustNo(): String?

    /**
     * 用户是否已登录
     */
    fun isLogined(): Boolean

    /**
     * 跳转到主页面
     * @param tabIndex 定位下标
     * @param bundle 跳转参数
     * @param 跳转时的参数
     * @param 跳转后,callback回调
     * @param navCompleteCallback 完成回调
     */
    fun launcherToAtyMain(tabIndex: Int, bundle: Bundle? = null, navCompleteCallback: NavigationCallback? = null)

    /**
     * 是否满足盅激活(前提: 已经登录)
     * return true:满足
     */
    fun canLauncherActivePage(): Boolean

    /**
     * 跳转到激活(包含登录和交易 激活)
     * @param loginType 登录方式: 验证码/密码
     * @param forceActivedH5PageLink 强制指定跳转激活的页面(登录/交易 激活H5两个不同的地址, 不传会根据用户状态指定url)
     * @param callback 完成回调
     */
    fun gotoActivePage(
        activity: Any?, forceActivedH5PageLink: String?, fromPage: String,
        callback: NavCompleteCallback? = null
    )

    /**
     * 跳转到登录激活页面
     */
    fun gotoLoginActivePage(activity: Any?, fromPage: String, callback: NavCompleteCallback? = null)

    /**
     * 执行登录
     * @param params 参数封装
     * @param fromPage 调起登录模块页面来源(用于追踪调用栈)
     * @param callback 登录完成回调
     */
    fun login(params: LoginParams, fromPage: String?, callback: LoginCallback? = null)

    /**
     * 退出登录
     */
    fun loginOut()

    /**
     * 目标面是否存在
     */
    fun getTargetActivity(activityPath: String): Activity?
    fun getTargetClass(activityPath: String?): Class<*>?

    /**
     * 获取MainActivity 下标定位参数key: "TAB_INDEX"
     */
    fun getMainActivityParamskeyIndex(): String

    /**
     * 交易账号未激活弹框
     */
    fun tradeUnActivedDlg()

    /**
     * 是否要强制登录
     */
    fun needForceRelogin(): Boolean

    /**
     * 设置当前是否有"重新登录"标识
     */
    fun setNeedForceRelogin(needRelogin: Boolean)

    /**
     * 是否有指纹模块
     */
    fun hasFinger(context: Context): Boolean

    /**
     * 显示提醒设置二级密码弹框
     * callback 为空则内部处理选择跳转，不为空则回调外部选择的类型 EMPTY为不设置
     * DlgGestureHint.GESTURE 手势
     * DlgGestureHint.FINGER 指纹
     * DlgGestureHint.EMPTY 不设置
     */
    fun showGestureSettingHintDialog(childFragmentManager: FragmentManager, callback: ((Int) -> Unit)? = null)

    fun launchFingerOrGestureLogin(
        context: Context,
        fingerResultConsumer: FingerResultConsumer
    ): Boolean

    fun launchModifyGestureSetting(activity: Activity?)

    fun executeGestureLogin(
        activity: Activity,
        fingerResultConsumer: FingerResultConsumer
    ): Boolean

    /**
     * 显示 检查用户激活状态提示弹框
     */
    fun showCheckActiveStatusDlg(
        activity: Activity?, btnLeft: String, btnRight: String,
        title: String, msg: String, callback: NavCompleteCallback?
    )

    /**
     * 选择手机号区号
     */
    fun showPhoneArea(activity: Activity, callback: NavCompleteCallback?)

    /**
     * 组装 H5 JS交互函数 jsParams() 需要的所有参数
     * @param userInfoForJs 如果为空,则取 用户管理中的缓存数据,如果不为空,强制将当前参数中的内容值给map字段
     */
    fun getUserInfoParams(userInfoForJs: TempUserInfoForJs?): MutableMap<String, Any?>

    /**
     * 登录协议
     */
    fun loginAgreementCheck(
        view: TextView,
        consumer: Consumer<Boolean>,
        analyticsCallback: Consumer<String>? = null
    ): ILoginPrivateAgreementDelegate

    fun afterLoginSuccess(fragment: Fragment)
}