package com.howbuy.global.login_api;

/**
 * description.
 * 登录页面路径
 * tao.liang
 * 2024/2/26
 */
public class LoginRouterPath {
    //登录页面
    public final static String PATH_USER_FRAG_LOGIN_BRIDGE = "/login/global/FragLoginPage";

    //登录相关provider
    public static final String PATH_LOGIN_PROVIDER = "/login/global/LoginProvider";

    //指纹/手势密码 provider
    public static final String PATH_GESTURE_PROVIDER = "/login/global/GestureProvider";

    //MainActivity 路由
    public static final String PATH_ACTIVITY_MAIN = "/global/main/MainActivity";

    //绑定手机号
    public static final String PATH_BIND_PHONE = "/login/global/FragBindPhone";

    //账号绑定列表
    public static final String PATH_FRAG_THIRD_AUTH_LIST = "/login/global/FragThirdAuthList";

}
