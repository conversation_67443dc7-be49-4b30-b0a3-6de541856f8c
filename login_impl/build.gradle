plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'

    id 'com.alibaba.arouter' //arouter
}

apply from: "${rootProject.projectDir}/arouter-kotlin-config.gradle"

android {
    namespace 'com.howbuy.global.login_impl'
    compileSdkVersion rootProject.ext.commonVersions.compileSdkVersion
    buildToolsVersion rootProject.ext.commonVersions.buildToolsVersion
    defaultConfig {
        minSdkVersion rootProject.ext.commonVersions.minSdkVersion
        targetSdkVersion rootProject.ext.commonVersions.targetSdkVersion
    }

}


dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation project(path: ':data-api')
    implementation project(path: ':login-api')
    implementation project(path: ':global-upgrade')
    implementation project(path: ':global-base')
    implementation project(path: ':analytics_api')
    rootProject.ext.depUtil.implementation("share_api", dependencies)
    rootProject.ext.depUtil.implementation("arouter_intercept_api", dependencies)
    rootProject.ext.depUtil.implementation('hBRouter', dependencies)
    rootProject.ext.depUtil.implementation('hBComponent', dependencies)
    //依赖了AbsHbFrag,同时,需要依赖IHbAnalytics, 和 WrapperListener
//    rootProject.ext.depUtil.implementation('fund-base', dependencies)
    rootProject.ext.depUtil.implementation("agentweb_api", dependencies)
    //IHbAnalytics
    implementation(rootProject.ext.dependencies['analytics-annotation'])
    implementation(rootProject.ext.dependencies['hb-analysis'])


    implementation(rootProject.ext.dependencies['loginobserver'])
    implementation rootProject.ext.dependencies["user_data_api"]
    implementation rootProject.ext.dependencies['recyclerView-adapter-helper']
    implementation rootProject.ext.dependencies['hb-net']
    implementation rootProject.ext.dependencies['hb-indexbar']
    //debug 后门修改地址
    implementation rootProject.ext.dependencies['modify-url']

    implementation rootProject.ext.dependencies["support-appcompat"]
    implementation rootProject.ext.dependencies["support-design"]
    implementation rootProject.ext.dependencies["constraint-layout"]

}