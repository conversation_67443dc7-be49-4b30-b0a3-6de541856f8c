package com.howbuy.global.login_impl.password

import android.os.Bundle
import android.text.InputType
import android.text.TextUtils
import android.text.method.PasswordTransformationMethod
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.EditText
import com.howbuy.account.UserDataHelper
import com.howbuy.account.remote.FetchAction
import com.howbuy.analytics.common.ACTIVE_TYPE
import com.howbuy.component.widgets.ClearableEdittext.IEditChanged
import com.howbuy.fund.base.IWechatProvider
import com.howbuy.fund.base.analytics.HbAnalytics
import com.howbuy.fund.base.config.ValConfig
import com.howbuy.fund.base.entity.WeChatLoginResult
import com.howbuy.fund.base.frag.AbsFragViewBinding
import com.howbuy.fund.base.nav.NavHelper
import com.howbuy.fund.base.router.RouterHelper
import com.howbuy.fund.net.entity.common.HeaderInfo
import com.howbuy.fund.net.entity.http.ReqNetOpt
import com.howbuy.fund.net.entity.http.ReqResult
import com.howbuy.fund.net.util.HandleErrorMgr
import com.howbuy.global.data_api.DataIds
import com.howbuy.global.data_api.get
import com.howbuy.global.login_api.ILoginPrivateAgreementDelegate
import com.howbuy.global.login_api.ILoginProvider
import com.howbuy.global.login_api.LoginRouterPath
import com.howbuy.global.login_api.LoginType
import com.howbuy.global.login_api.constanst.LoginConstants.FIND_PASS_WORD
import com.howbuy.global.login_api.constanst.LoginConstants.LOGIN_AREA_CHECKED
import com.howbuy.global.login_impl.Constants.ERROR_CERT_UNIQUE
import com.howbuy.global.login_impl.Constants.ERROR_PASSWORD
import com.howbuy.global.login_impl.Constants.ERROR_PASSWORD_LIMIT
import com.howbuy.global.login_impl.Constants.ERROR_PASSWORD_NOT_EXIST
import com.howbuy.global.login_impl.Constants.ERROR_REGISTERY
import com.howbuy.global.login_impl.Constants.ERROR_USERNAME_NOT_EXIST
import com.howbuy.global.login_impl.FragLoginPage
import com.howbuy.global.login_impl.LoginAccountMgr
import com.howbuy.global.login_impl.LoginBuilder
import com.howbuy.global.login_impl.LoginInfMgr
import com.howbuy.global.login_impl.LoginPrivateAgreementDelegate
import com.howbuy.global.login_impl.R
import com.howbuy.global.login_impl.databinding.FragLoginWithPasswordLayoutBinding
import com.howbuy.global.login_impl.entity.CountryBody
import com.howbuy.global.login_impl.entity.EmailSuffixBody
import com.howbuy.global.login_impl.entity.IdCardTypeBody
import com.howbuy.global.login_impl.entity.LoginBody
import com.howbuy.global.login_impl.password.country.DlgSelectCountry
import com.howbuy.global.login_impl.password.country.DlgSelectIdCardType
import com.howbuy.lib.utils.DensityUtils
import com.howbuy.lib.utils.InputTargetVisable
import com.howbuy.lib.utils.JsonUtils
import com.howbuy.lib.utils.LogUtils
import com.howbuy.lib.utils.ViewUtils
import com.howbuy.router.proxy.Invoker


/**
 * description.
 * 密码登录页面
 * tao.liang
 * 2024/2/19
 */
class FragLoginWithPassword : AbsFragViewBinding<FragLoginWithPasswordLayoutBinding>(),
    IEditChanged {

    //国家数据
    private var mCountryBody: CountryBody? = null

    //邮箱数据
    private var mEmailBody: EmailSuffixBody? = null

    //勾选协议
    private var mLoginPrivateAgreementDelegate: ILoginPrivateAgreementDelegate? = null

    //默认的登录类型: 手机号
    private var mCurSelectedAccountType: AccountType = AccountType.PHONE

    //用户点击登录按钮时,记录用户账号输入框中输入的内容
    private var account: String? = ""

    //登录账号类型
    private var loginAcctType: String? = ""

    //账号不唯一时,用户选择证件类型id
    private var idType: String? = ""

    //账号不唯一时,用户选择证件地区id
    private var idAreaCode: String? = ""

    //手机登录时的 账号
    private var mLastPhoneContent: String = ""

    //手机登录时的手机区号
    private var mLastPhoneArea: String? = "+86" //默认+86

    //证件登录时的 账号
    private var mLastCardContent: String = ""

    //控件证件登录时的 校验规则
    private var mAccountEditTextFilter: AccountEditTextFilter? = null

    // 证件号登录时: 标记当前是否非唯一用户状态, 切换登录类型时,保存之前的操作状态
    private var uniqueUserSense = false

    //证件类型时, 账号不唯一 选择的国家/地区
    private var mCurCountry: CountryBody.Item? = null

    //证件类型时, 账号不唯一 选择的证件类型
    private var mCurCardType: IdCardTypeBody.Item? = null

    //邮箱登录时的 账号
    private var mLastEmailContent: String = ""

    //邮箱类型,联想内容
    private var mAutoEmailCompletePop: EmailAutoCompletePop? = null
    private var hkCustNo: String? = ""


    override fun createViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragLoginWithPasswordLayoutBinding {
        return FragLoginWithPasswordLayoutBinding.inflate(inflater, container, false)
    }

    override fun getFragLayoutId(): Int {
        return R.layout.frag_login_with_password_layout
    }

    private var mCurInputTypeVisibleFlag = false

    override fun stepAllViews(root: View?, savedInstanceState: Bundle?) {
        super.stepAllViews(root, savedInstanceState)
        // 设置输入框不被键盘挡住
        activity?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
        binding.tvLogin.isEnabled = false
        binding.cbAgreement.isChecked = false
        binding.inputAccount.setEditChangedListener(this)
        binding.cetPwd.setEditChangedListener(this)
        binding.cetPwd.filters = arrayOf(NoChineseFilter())
        InputTargetVisable(binding.tvLogin)
            .addVeriyType(
                InputTargetVisable.FieldVeriyType(
                    InputTargetVisable.FieldVeriyType.TYPE_NULL,
                    binding.inputAccount
                )
            )
            .addVeriyType(
                InputTargetVisable.FieldVeriyType(
                    InputTargetVisable.FieldVeriyType.TYPE_MIN_LENGTH,
                    6,
                    binding.cetPwd
                )
            )
        initLoginTypeClick()
        initClick()
        // 设置输入过滤器(只能输入 字母或者数字)
        mAccountEditTextFilter = AccountEditTextFilter()

        binding.llContent.setOnTouchListener { v, event ->
            clearEditTextFocus(v)
            false
        }

        binding.ivChangePwdType.setImageResource(com.howbuy.component.R.drawable.btn_register_eye_close)

        binding.ivChangePwdType.setOnClickListener {
            if (mCurInputTypeVisibleFlag) {
                // 当前显示的密码,点击后,就隐藏
                mCurInputTypeVisibleFlag = false
                binding.cetPwd.transformationMethod =
                    PasswordTransformationMethod.getInstance()
                binding.ivChangePwdType.setImageResource(com.howbuy.component.R.drawable.btn_register_eye_close)
            } else {
                // 当前隐藏密码模式,点击后,就显示
                mCurInputTypeVisibleFlag = true
                binding.cetPwd.transformationMethod = null
                binding.ivChangePwdType.setImageResource(com.howbuy.component.R.drawable.btn_register_eye_open)
            }
            binding.cetPwd.setSelection(binding.cetPwd.text?.length ?: 0)
        }


        val showWechatLogin =
            Invoker.getInstance().navigation(IWechatProvider::class.java).wechatEnable()
        binding.thirdLogin.visibility = if (showWechatLogin) View.VISIBLE else View.GONE
        binding.thirdLogin.findViewById<View>(R.id.wechat_auth).setOnClickListener {
            HbAnalytics.onClick("613620")
            if (binding.cbAgreement.isChecked) {
                showAlermDlg("加载中", true, true)
                get(IWechatProvider::class.java).wechatLogin(
                    activity
                ) { t ->
                    showAlermDlg(null, 0)
                    val wechatLoginResult = t?.mData as? WeChatLoginResult
                    //LogUtils.pop("登录成功:${wechatLoginResult != null}")
                    //是否绑定手机,没绑定手机之前都算未登录
                    if (wechatLoginResult?.isBindHk == "1") {
                        //已绑定客户号,算登录成功
                        if (TextUtils.isEmpty(wechatLoginResult.hkCustNo)) {
                            //返回的香港客户号为空,不正常
                            return@wechatLogin
                        }
                        //refreshByWeChatLogin = true
                        //先保存用户信息,后边查用户信息需要
                        get(ILoginProvider::class.java).saveLoginInfo(
                            wechatLoginResult.hkCustNo!!,
                            LoginType.wechat,
                            "",
                            "",
                            ""
                        )
                        //afterLoginSuccess最终会触发LoginObserver
                        get(ILoginProvider::class.java).afterLoginSuccess(this@FragLoginWithPassword)
                        UserDataHelper.scheduleRemoteFetch(
                            DataIds.ID_USER_INFO,
                            FetchAction.dataChanged
                        )
                        UserDataHelper.scheduleRemoteFetch(
                            DataIds.ID_USER_LICENSEE,
                            FetchAction.dataChanged
                        )
                        UserDataHelper.scheduleRemoteFetch(
                            DataIds.ID_USER_MESSAGE,
                            FetchAction.dataChanged
                        )
                        UserDataHelper.scheduleRemoteFetch(
                            DataIds.ID_USER_HOLD,
                            FetchAction.dataChanged
                        )
                        //刷新微信绑定信息
                        UserDataHelper.getDataManager(DataIds.ID_USER_WECHAT_BIND)
                            .scheduleRemoteFetch(FetchAction.dataChanged)
                        HbAnalytics.onActive(ACTIVE_TYPE.ACTIVE_LOGIN, hkCustNo, true)

                    } else {
                        //未绑定手机号,跳转绑定手机号
                        RouterHelper.launchFrag(this, LoginRouterPath.PATH_BIND_PHONE)
                    }


                }
            } else {
                binding.layBindHint.visibility = View.VISIBLE
                mLoginPrivateAgreementDelegate?.startAnimationAndShowTips(binding.layBindHint)
            }
        }

    }

    fun clearEditTextFocus(v: View?) {
        ViewUtils.showKeybord(v, false)
        binding.inputAccount.clearFocus()
        binding.cetPwd.clearFocus()
    }

    override fun parseArgment(arg: Bundle?) {
        mLoginPrivateAgreementDelegate = LoginPrivateAgreementDelegate(binding.cbAgreement, { t ->
            (parentFragment as? FragLoginPage)?.mCheckLiveData?.postValue(t)
        },null)
        mLoginPrivateAgreementDelegate?.create()
        (parentFragment as? FragLoginPage)?.mCheckLiveData?.observe(this) {
            binding.cbAgreement.isChecked = it
            if (it) {
                binding.layBindHint.visibility = View.GONE
            }
        }
    }

    private fun initLoginTypeClick() {
        binding.tvLoginType.setOnClickListener {
            //选择登录的开户类型(手机号,证件号,邮箱)弹框
            val curTitle = binding.tvLoginType.text.toString()
            when (curTitle) {
                AccountType.PHONE.desc -> {
                    mLastPhoneContent = binding.inputAccount.text.toString()
                    mLastPhoneArea = binding.tvLoginPhoneArea.text.toString()
                }

                AccountType.EMAIL.desc -> {
                    mLastEmailContent = binding.inputAccount.text.toString()
                }

                else -> {
                    mLastCardContent = binding.inputAccount.text.toString()
                }
            }
            clearEditTextFocus(it)
            val bundle = NavHelper.obtainArg(curTitle)
            DlgSelectAccountType.getInstance(
                bundle,
                object : DlgSelectAccountType.ISelectedListener {
                    override fun onSelected(accountType: AccountType) {
                        mCurSelectedAccountType = accountType
                        binding.tvLoginType.text = accountType.desc
                        updateAccountTextUIByType(accountType.desc)
                    }
                }).show(childFragmentManager, null)
        }
    }

    /**
     * 初始化邮箱输入后缀联想相关
     */
    private fun initEmailPop() {
        activity ?: return
        if (mAutoEmailCompletePop == null) {
            mAutoEmailCompletePop = EmailAutoCompletePop(
                activity, object : EmailAutoCompletePop.ISelectEmail {
                    override fun onSelectEmail(email: String?) {
                        binding.inputAccount.setText(email)
                        if ((binding.inputAccount.text?.toString()?.length ?: 0) > 0) {
                            binding.inputAccount.setSelection(binding.inputAccount.text.toString().length)
                        }
                    }
                })
        }

    }

    /**
     * 根据用户选择的登录类型,切换不同的 账号类型提示
     */
    private fun updateAccountTextUIByType(title: String) {
        binding.layLoginSelectCountry.visibility = View.GONE
        binding.layLoginSelectTypeByCountry.visibility = View.GONE
        when (title) {
            AccountType.PHONE.desc -> {
                //若账号类型选中“手机号”，则打开数字键盘，不限制输入位数
                binding.tvLoginPhoneArea.visibility = View.VISIBLE
                binding.inputAccount.hint = "请输入手机号"
                binding.inputAccount.setText(mLastPhoneContent)
                binding.tvLoginPhoneArea.text = mLastPhoneArea
                if ((binding.inputAccount.text?.toString()?.length ?: 0) > 0) {
                    binding.inputAccount.setSelection(binding.inputAccount.text.toString().length)
                }
                binding.inputAccount.inputType = InputType.TYPE_CLASS_NUMBER
                binding.tvLogin.isEnabled = countrySelected(false)
            }

            AccountType.EMAIL.desc -> {
                //则默认打开字母键盘，可切换至其他格式的键盘，不限制输入格式
                binding.tvLoginPhoneArea.visibility = View.GONE
                binding.inputAccount.hint = "请输入邮箱地址"
                binding.inputAccount.filters = arrayOf()
                binding.inputAccount.inputType = InputType.TYPE_CLASS_TEXT
                binding.inputAccount.setText(mLastEmailContent)
                if ((binding.inputAccount.text?.toString()?.length ?: 0) > 0) {
                    binding.inputAccount.setSelection(binding.inputAccount.text.toString().length)
                }
                binding.tvLogin.isEnabled = countrySelected(false)
                if (mEmailBody == null) {
                    LoginBuilder.reqEmailSuffix(0) {
                        if (activity == null || activity?.isFinishing == true) {
                            return@reqEmailSuffix
                        }
                        if (it.isSuccess && it.mData != null) {
                            mEmailBody = it.mData as EmailSuffixBody
                        }
                    }
                }

            }

            else -> {
                //则默认打开默认键盘，可切换至其他格式的键盘，限制输入数字/字母以外的字符
                binding.tvLoginPhoneArea.visibility = View.GONE
                binding.inputAccount.hint = "请输入开户证件号"
                binding.inputAccount.filters = arrayOf(mAccountEditTextFilter)
                binding.inputAccount.inputType = InputType.TYPE_CLASS_TEXT
                binding.inputAccount.setText(mLastCardContent)
                if ((binding.inputAccount.text?.toString()?.length ?: 0) > 0) {
                    binding.inputAccount.setSelection(binding.inputAccount.text.toString().length)
                }
                if (uniqueUserSense) {
                    binding.layLoginSelectCountry.visibility = View.VISIBLE
                    binding.layLoginSelectTypeByCountry.visibility = View.VISIBLE
                }
                binding.tvLogin.isEnabled = countrySelected(true)
            }
        }
        binding.inputAccount.clearFocus()
    }

    override fun onFocusChange(v: EditText?, hasFocus: Boolean) {
        if (mCurSelectedAccountType == AccountType.EMAIL && v?.id == R.id.input_account) {
            if (hasFocus) {
                showEmailAutoCompletePop(v.text.toString())
            } else {
                mAutoEmailCompletePop?.dismiss()
            }
        }
        if (v == binding.cetPwd && hasFocus) {
            binding.scvContent.postDelayed({
                if (isBindNotExisted()) {
                    return@postDelayed
                }
                var targetScrollY = 0
                val contentBtm: Int = binding.scvContent.bottom
                val targetBtm: Int = binding.cetPwd.bottom
                targetScrollY = targetBtm - contentBtm
                binding.scvContent.smoothScrollTo(0, targetScrollY + 100)
            }, 250)
        }
    }

    override fun onSelectionChanged(v: EditText?, selStart: Int, selEnd: Int) {

    }

    override fun onTextChanged(
        v: EditText?,
        s: CharSequence?,
        start: Int,
        before: Int,
        count: Int
    ) {
        if (isBindNotExisted()) {
            return
        }
        if (mCurSelectedAccountType == AccountType.EMAIL && v?.id == R.id.input_account) {
            initEmailPop()
            if (binding.inputAccount.hasFocus()) {
                showEmailAutoCompletePop(s?.toString() ?: "")
            }
            mAutoEmailCompletePop?.updateInputContent(s.toString(), mEmailBody?.emailSuffixList)
        } else if (mCurSelectedAccountType == AccountType.CARD && v?.id == R.id.input_account) {
            //修改了证件号,就清空 选择国家/地区 相关数据
            if (!TextUtils.equals(mLastCardContent, s?.toString())){
                clearUniqueUserSenseInfo()
            }
        }
    }

    /**
     * 显示邮箱自动填充后缀弹框
     */
    private fun showEmailAutoCompletePop(content: String?) {
        if (content.isNullOrEmpty() || content.contains("@")) {
            //当输入的内容中,为空或者 带有@符号时, 要关闭联想内容,否则就显示
            mAutoEmailCompletePop?.dismiss()
        } else {
            if (mEmailBody != null && mAutoEmailCompletePop?.isShowing != true) {
                mAutoEmailCompletePop?.showAsDropDown(
                    binding.inputAccount,
                    0,
                    DensityUtils.dp2px(1f)
                )
            }
        }
    }

    private fun initClick() {
        //选择手机号 区号
        binding.tvLoginPhoneArea.setOnClickListener {
            clearEditTextFocus(it)
            LoginAccountMgr.launcherWebPageByUriKey(
                activity,
                LOGIN_AREA_CHECKED,
                "", false,null,
            ) { _, resultData, _ ->
                val data = resultData?.getString(ValConfig.IT_ENTITY)
                val js = JsonUtils.getObject(data)
                var areaCode: String? = ""
                if (js != null) {
                    //解析H5返回的json内容,字段
                    areaCode = JsonUtils.getString(js, "areaCode")
                }
                if (!TextUtils.isEmpty(areaCode)) {
                    mLastPhoneArea = areaCode
                    binding.tvLoginPhoneArea.text = mLastPhoneArea
                }
            }
        }

        //忘记密码
        binding.tvLoginForgetPwd.setOnClickListener {
            clearEditTextFocus(it)
            val map = HashMap<String, String>()
            //跳转忘记密码,需要固定拼接以下两个参数
            map["passWordType"] = "findLoginPassWord"
            map["from"] = "login"
            LoginAccountMgr.launcherWebPageByUriKey(activity, FIND_PASS_WORD, "", false, map)
            HbAnalytics.onClick("610030")
        }

        //登录
        binding.tvLogin.setOnClickListener {
            clickLogin()
            HbAnalytics.onClick("610020")
        }
    }

    //点击登陆
    private fun clickLogin() {
        clearEditTextFocus(binding.tvLogin)
        if (!checkPrivateAgreementState()) {
            return
        }
        (parentFragment as? FragLoginPage)?.mLoginType = LoginType.password
        showAlermDlg("加载中...", false, false)
        binding.tvLogin.isEnabled = false
        var mobile: String? = ""
        var email: String? = ""
        var idNo: String? = ""
        account = binding.inputAccount.text.toString()
        val pwd = binding.cetPwd.text.toString()
        loginAcctType = mCurSelectedAccountType.code
        when (loginAcctType) {
            AccountType.PHONE.code -> {
                mLastPhoneArea = binding.tvLoginPhoneArea.text.toString()//手机区号
                mobile = account
                mLastPhoneContent = binding.inputAccount.text.toString()
            }

            AccountType.EMAIL.code -> {
                email = account
                mLastEmailContent = binding.inputAccount.text.toString()
            }

            else -> {
                idNo = account
                idAreaCode = mCurCountry?.countryCode
                idType = mCurCardType?.idType
                mLastCardContent = binding.inputAccount.text.toString()
            }
        }
        sendLogin(
            loginAcctType, pwd, mLastPhoneArea, mobile, idAreaCode,
            idType, idNo, email
        )
    }

    //判断是否勾选了协议, 如果没有勾选, 需要提示,动画效果
    private fun checkPrivateAgreementState(): Boolean {
        val checked: Boolean = binding.cbAgreement.isChecked
        if (!checked) {
            binding.layBindHint.visibility = View.VISIBLE
            mLoginPrivateAgreementDelegate?.startAnimationAndShowTips(binding.layBindHint)
        }
        return checked
    }

    /**
     * 点击登录后, 满足“账号类型=开户证件号”，且登录证件号在数据库中不唯一:
     * 需要显示: 国家地区/证件类型,再次验证用户
     */
    private fun uniqueUserSense() {
        uniqueUserSense = true
        LogUtils.pop("请选择开户证件所属国家/地区及证件类型")
        binding.layLoginSelectCountry.visibility = View.VISIBLE
        binding.layLoginSelectTypeByCountry.visibility = View.VISIBLE
        if (mCurCountry != null) {
            binding.tvLoginSelectTypeByCountry.isEnabled = true
            binding.tvLoginSelectTypeByCountry.alpha = 1.0f
        } else {
            binding.tvLoginSelectTypeByCountry.isEnabled = false
            binding.tvLoginSelectTypeByCountry.alpha = 0.3f
        }
        binding.tvLoginSelectCountry.setOnClickListener {
            //如果选择了国家/地区, 证件类型按钮才可点击(要接收返回的数据)
            clearEditTextFocus(it)
            if (mCountryBody == null) {
                //如果国家接口未响应,不让用户选择
                LogUtils.pop("系统异常，请稍后再试")
                return@setOnClickListener
            }
            val bundle = NavHelper.obtainArg(
                "",
                ValConfig.IT_FROM,
                mCurCountry?.countryCode
            )
            val countryDlg =
                DlgSelectCountry.getInstance(bundle, object : DlgSelectCountry.ISelectedListener {
                    override fun onSelected(country: CountryBody.Item?) {
                        if (mCurCountry != country) {
                            //如果修改了国家, 需要清空之前选择的证件类型
                            binding.tvLoginSelectTypeByCountry.isEnabled = true
                            binding.tvLoginSelectTypeByCountry.alpha = 1.0f
                            binding.tvLoginSelectTypeByCountry.text = ""
                            binding.tvLoginSelectCountry.text = country?.chineseName
                            if (mCurCountry != null && mCurCardType != null) {
                                LogUtils.pop("请重新选择证件类型")
                            }
                            mCurCountry = country
                            mCurCardType = null
                        }
                        binding.tvLogin.isEnabled = countrySelected(true)
                    }
                });
            countryDlg.show(childFragmentManager, null)
            countryDlg.setCountryBody(mCountryBody)
        }

        binding.tvLoginSelectTypeByCountry.setOnClickListener {
            //选择证件类型
            clearEditTextFocus(it)
            val bundle = NavHelper.obtainArg(
                "",
                ValConfig.IT_FROM,
                mCurCardType?.idType,
                ValConfig.IT_TYPE,
                mCurCountry?.countryCode
            )
            DlgSelectIdCardType.getInstance(bundle, object : DlgSelectIdCardType.ISelectedListener {
                override fun onSelected(cardType: IdCardTypeBody.Item) {
                    mCurCardType = cardType
                    binding.tvLoginSelectTypeByCountry.text = cardType.idTypeDesc
                    binding.tvLogin.isEnabled = countrySelected(true)
                }
            }).show(childFragmentManager, null)
        }
    }

    /**
     * 清空用户选择国家/地区数据
     */
    private fun clearUniqueUserSenseInfo() {
        binding.layLoginSelectCountry.visibility = View.GONE
        binding.layLoginSelectTypeByCountry.visibility = View.GONE
        binding.tvLoginSelectCountry.text = ""
        binding.tvLoginSelectTypeByCountry.text = ""
        mCurCountry = null
        mCurCardType = null
    }

    /**
     * 证件号登录时, 如果账号不唯一:
     * 选择了国家/地区和证件类型,登录按钮才可点击
     */
    private fun countrySelected(card: Boolean): Boolean {
        return if (uniqueUserSense && card) {
            (mCurCardType != null && mCurCountry != null
                    && binding.inputAccount.text.isNotEmpty()
                    && binding.cetPwd.text.length >= 6)
        } else {
            (binding.inputAccount.text.isNotEmpty()
                    && binding.cetPwd.text.length >= 6)
        }
    }

    /**
     * 密码登录
     * @param loginAcctType 登录方式 必须，0-证件；1-手机 2-邮箱
     * @param loginPassword 登录密码 必须
     * @param areaCode 地区码 手机号非空时必须(登录方式=1)
     * @param mobile 手机号 登录方式=1-手机时必须(登录方式=1)
     * @param idAreaCode 证件地区码(登录方式=0)
     * @param idType 证件类型 证件号码非空时必须(登录方式=0)
     * @param idNo 证件号码 登录方式=0-证件时必须
     * @param email 邮箱
     */
    private fun sendLogin(
        loginAcctType: String?,
        loginPassword: String?,
        areaCode: String?,
        mobile: String?,
        idAreaCode: String?,
        idType: String?,
        idNo: String?,
        email: String?
    ) {
        LoginBuilder.reqLoginWithPassword(
            loginAcctType, loginPassword, areaCode, mobile, idAreaCode, idType, idNo, email, 0
        ) {
            if (activity == null || activity?.isFinishing == true) {
                return@reqLoginWithPassword
            }
            binding.tvLogin.isEnabled = true
            if (it.isSuccess && it.mData != null) {
                val body = it.mData as LoginBody
                hkCustNo = body.hkCustNo
                setLoginInfo()
                //请求用户信息
                (parentFragment as? FragLoginPage)?.reqUserInfoByFetchCallback()
                UserDataHelper.scheduleRemoteFetch(DataIds.ID_USER_INFO, FetchAction.dataChanged)
                UserDataHelper.scheduleRemoteFetch(DataIds.ID_USER_LICENSEE, FetchAction.dataChanged)
                UserDataHelper.scheduleRemoteFetch(DataIds.ID_USER_MESSAGE, FetchAction.dataChanged)
                UserDataHelper.scheduleRemoteFetch(DataIds.ID_USER_HOLD, FetchAction.dataChanged)
                HbAnalytics.onActive(ACTIVE_TYPE.ACTIVE_LOGIN, hkCustNo, true)
            } else {
                showAlermDlg(null, 0)
                buzErrHandle(it)
            }
        }


    }

    /**
     * 保存用户登录信息
     */
    private fun setLoginInfo() {
        LoginInfMgr.loginTypeInfo?.refreshUserInfo(
            hkCustNo, LoginType.password,
            loginAcctType, account, mLastPhoneArea
        )
        LoginAccountMgr.saveLoginInfo(LoginInfMgr.loginTypeInfo)
    }

    /**
     * 异常分支流程处理
     */
    private fun buzErrHandle(it: ReqResult<ReqNetOpt>) {
        val headInfo = it.mErr.extras as? HeaderInfo
        if (TextUtils.equals(headInfo?.responseCode, ERROR_REGISTERY)) {
            LoginAccountMgr.showAccountUnRegistered(this@FragLoginWithPassword)
        } else if (TextUtils.equals(headInfo?.responseCode, ERROR_CERT_UNIQUE)) {
            // ②检验2：是否满足“账号类型=开户证件号”，且登录证件号在数据库中不唯一
            reqCountryList()
            uniqueUserSense()
            binding.tvLogin.isEnabled = countrySelected(true)
        } else if (TextUtils.equals(
                headInfo?.responseCode,
                ERROR_USERNAME_NOT_EXIST
            )
        ) {
            //点击“登录”需根据“所属国家/地区”+“证件类型”+“证件号”校验账号是不存在
            LoginAccountMgr.showAccountNotExist(this@FragLoginWithPassword)
        } else if (TextUtils.equals(headInfo?.responseCode, ERROR_PASSWORD_NOT_EXIST)) {
            //若登录密码为空
            if (mCurSelectedAccountType.code == AccountType.PHONE.code
                && TextUtils.equals("+86", mLastPhoneArea)
            ) {
                // a.若账号类型=“大陆手机号”
                LogUtils.pop("该账号未设置登录密码，您可以使用验证码登录。")
            } else {
                // b.若账号类型≠“大陆手机号”
                LogUtils.pop("该账号未设置登录密码，您可以点击“忘记密码”功能设置密码后再登录。")
            }
        } else if (TextUtils.equals(headInfo?.responseCode, ERROR_PASSWORD)) {
            //账户/密码错误，请重新输入。密码错误6次后账号将被锁定，还剩余#{time}次机会。
            val pwdErrStr = HandleErrorMgr.handErrorMsg(it.mErr, true)
            LogUtils.pop(pwdErrStr)
        } else if (TextUtils.equals(headInfo?.responseCode, ERROR_PASSWORD_LIMIT)) {
            //输错密码达上限
            var pwdLimitErrStr = HandleErrorMgr.handErrorMsg(it.mErr, true)
            if (TextUtils.isEmpty(pwdLimitErrStr)) {
                pwdLimitErrStr = "账户/密码错误次数已达上限，请2小时后再试。如有疑问，请咨询客服。"
            }
            LogUtils.pop(pwdLimitErrStr)
        } else {
            LogUtils.pop("系统异常，请稍后再试")
        }
    }

    /**
     * 当账号不唯一,时 触发接口请求
     */
    private fun reqCountryList() {
        LoginBuilder.reqCountryList(0) {
            if (it.isSuccess && it.mData != null) {
                mCountryBody = it.mData as CountryBody
            }
        }
    }

}