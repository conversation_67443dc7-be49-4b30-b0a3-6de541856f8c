package com.howbuy.global.login_impl;

import static com.howbuy.global.login_api.constanst.LoginConstants.PROTOCOL_HK_PRIVATE;

import android.annotation.SuppressLint;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.method.LinkMovementMethod;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.DecelerateInterpolator;
import android.view.animation.TranslateAnimation;
import android.widget.CheckBox;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.core.util.Consumer;

import com.howbuy.fund.base.analytics.HbAnalytics;
import com.howbuy.fund.base.config.ValConfig;
import com.howbuy.fund.base.nav.NavHelper;
import com.howbuy.fund.base.utils.HBURLSpan;
import com.howbuy.global.login_api.ILoginPrivateAgreementDelegate;
import com.howbuy.h5.h5config.ParserUriZipResource;
import com.howbuy.lib.utils.ColorUtils;
import com.howbuy.lib.utils.SpanBuilder;
import com.howbuy.lib.utils.ViewUtils;
import com.howbuy.router.provider.IWebProvider;
import com.howbuy.router.proxy.Invoker;

/**
 * 好买全球 个人信息收集隐私协议
 */
public class LoginPrivateAgreementDelegate implements ILoginPrivateAgreementDelegate {
    private static final String TAG = "LoginPrivateAgreement";

    private final TextView mTarget;
    private final Consumer<Boolean> mConsumer;
    private final Consumer<String> mAnalyticsCallback;
    /**
     * 动画距离
     */
    public static final int TRANSLATE_X = 10;

    public LoginPrivateAgreementDelegate(TextView target, Consumer<Boolean> consumer, @Nullable  Consumer<String> analyticsCallback) {
        mTarget = target;
        mConsumer = consumer;
        mAnalyticsCallback = analyticsCallback;
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public LoginPrivateAgreementDelegate create() {
        String rawContent = "我已阅读并同意《个人资料收集声明》";
        SpanBuilder spanBuilder = new SpanBuilder(rawContent);
        int startIndexForUser = 7;
        int spanColor = ColorUtils.parseColor("#556db9");
        spanBuilder.color(startIndexForUser, rawContent.length(), spanColor, false);
        SpannableString spannableString = spanBuilder.getmSp();
        HBURLSpan hburlSpanForUser = new HBURLSpan();
        hburlSpanForUser.setColor(spanColor);
        hburlSpanForUser.setSpanClick(view -> {
            if(mAnalyticsCallback != null) {
                mAnalyticsCallback.accept("PROTOCOL");
            }
            ViewUtils.showKeybord(view, false);
            String link = ParserUriZipResource.getUrlWithKey(PROTOCOL_HK_PRIVATE);
            Invoker.getInstance().navigation(IWebProvider.class).launchWebView(mTarget.getContext(),
                    NavHelper.obtainArg("个人资料收集声明", ValConfig.IT_URL, link), null);
        });

        spannableString.setSpan(
                hburlSpanForUser,
                startIndexForUser,
                rawContent.length(),
                Spanned.SPAN_INCLUSIVE_INCLUSIVE
        );

        mTarget.setText(spannableString);
        mTarget.setMovementMethod(LinkMovementMethod.getInstance());

        mTarget.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ViewUtils.showKeybord(v, false);
                if (mConsumer != null) {
                    mConsumer.accept(((CheckBox) mTarget).isChecked());
                }
            }
        });
        return this;
    }

    @Override
    public void startAnimationAndShowTips(View tipsView) {
        TranslateAnimation buildAnimation = buildAnimation();
        mTarget.startAnimation(buildAnimation);
        tipsView.startAnimation(buildAnimation);
    }

    private TranslateAnimation buildAnimation() {
        TranslateAnimation translateAnimation = new TranslateAnimation(-TRANSLATE_X, TRANSLATE_X, 0F, 0F);
        translateAnimation.setFillAfter(false);
        translateAnimation.setDuration(100);
        translateAnimation.setRepeatCount(3);
        translateAnimation.setRepeatMode(Animation.REVERSE);
        translateAnimation.setInterpolator(new DecelerateInterpolator());
        return translateAnimation;
    }

}
