package com.howbuy.global.login_impl.bind

import android.graphics.Color
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import com.alibaba.android.arouter.facade.annotation.Route
import com.howbuy.account.UserDataHelper
import com.howbuy.account.remote.FetchAction
import com.howbuy.analytics.common.ACTIVE_TYPE
import com.howbuy.component.widgets.ClearableEdittext
import com.howbuy.fund.base.IWechatProvider
import com.howbuy.fund.base.analytics.HbAnalytics
import com.howbuy.fund.base.config.ApkConfig
import com.howbuy.fund.base.config.ValConfig
import com.howbuy.fund.base.frag.AbsFragViewBinding
import com.howbuy.fund.base.router.AtyBridgeHelper
import com.howbuy.fund.net.entity.common.HeaderInfo
import com.howbuy.global.data_api.DataIds
import com.howbuy.global.data_api.get
import com.howbuy.global.login_api.ILoginPrivateAgreementDelegate
import com.howbuy.global.login_api.ILoginProvider
import com.howbuy.global.login_api.LoginRouterPath.PATH_BIND_PHONE
import com.howbuy.global.login_api.LoginType
import com.howbuy.global.login_impl.Constants
import com.howbuy.global.login_impl.FragLoginPage
import com.howbuy.global.login_impl.LoginBuilder
import com.howbuy.global.login_impl.LoginPrivateAgreementDelegate
import com.howbuy.global.login_impl.R
import com.howbuy.global.login_impl.VerifyCodeManager
import com.howbuy.global.login_impl.captcha.DlgRelativeHboneNoErr
import com.howbuy.global.login_impl.databinding.FragBindPhoneBinding
import com.howbuy.global.login_impl.entity.LoginBody
import com.howbuy.global.login_impl.entity.LoginSmsBody
import com.howbuy.lib.utils.ColorUtils
import com.howbuy.lib.utils.InputTargetVisable
import com.howbuy.lib.utils.LogUtils
import com.howbuy.lib.utils.StatusBarUtil
import com.howbuy.lib.utils.StrUtils
import com.howbuy.lib.utils.VerifyUtil
import com.howbuy.lib.utils.ViewUtils

/**
 * author: gen.li
 * date  : 2025/4/27
 * desc  :
 */
@Route(path = PATH_BIND_PHONE)
class FragBindPhone : AbsFragViewBinding<FragBindPhoneBinding>(), ClearableEdittext.IEditChanged {
    //是否显示了 收不到短信提示文案
    private var changePhoneNum = ""
    private var mVerifyCodeManger: VerifyCodeManager? = null
    private var mLoginPrivateAgreementDelegate: ILoginPrivateAgreementDelegate? = null
    private var unionId: String = ""

    //当切换 tab时失去焦点时,验证码登录页面,不要弹出这个 11位大陆号的提示
    var mNeedShowHint = true

    private var hkCustNo: String? = ""
    private var mInputPhoneNum: String? = ""

    override fun createViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragBindPhoneBinding {
        return FragBindPhoneBinding.inflate(inflater, container, false)
    }

    override fun getFragLayoutId(): Int {
        return R.layout.frag_bind_phone
    }

    override fun stepAllViews(root: View?, savedInstanceState: Bundle?) {
        super.stepAllViews(root, savedInstanceState)
        activity?.let {
            val atyEmptyApi = AtyBridgeHelper.getAtyEmptyApi(it)
            atyEmptyApi.actionBarToolBar?.setNavigationIcon(R.drawable.ic_close_black)
            atyEmptyApi.setToolbarLineVisibility(false)
            atyEmptyApi.actionBarToolBar?.setBackgroundColor(ColorUtils.parseColor("#ffffff"))
            StatusBarUtil.setStatusBarLightMode(it, Color.WHITE, true)
        }

        binding.tvLogin.isEnabled = false
        binding.cbAgreement.isChecked = false
        binding.inputPhone.setEditChangedListener(this)
        binding.cetAuthCode.setEditChangedListener(this)
        InputTargetVisable(binding.tvLogin)
            .addVeriyType(
                InputTargetVisable.FieldVeriyType(
                    InputTargetVisable.FieldVeriyType.TYPE_PHONE,
                    11,
                    binding.inputPhone
                )
            )
            .addVeriyType(
                InputTargetVisable.FieldVeriyType(
                    InputTargetVisable.FieldVeriyType.TYPE_MIN_LENGTH,
                    6,
                    binding.cetAuthCode
                )
            )
        binding.tvAuthCodeSender.isEnabled = false
        binding.viewClickVerify.visibility = View.VISIBLE
        binding.tvAuthCodeSender.setOnClickListener {
            val curPhoneNum = binding.inputPhone.text.toString()
            changePhoneNum = curPhoneNum
            clickSendSmScode()
        }
        binding.viewClickVerify.setOnClickListener {
            LogUtils.pop("请输入11位数字的大陆手机号")
        }

        binding.tvLogin.setOnClickListener {
            clickLogin()
            //HbAnalytics.onClick("610010")
        }
        binding.llContent.setOnTouchListener { v, event ->
            clearEditTextFocus(v)
            false
        }

    }

    fun clearEditTextFocus(v: View?) {
        ViewUtils.showKeybord(v, false)
        binding.inputPhone.clearFocus()
        binding.cetAuthCode.clearFocus()
    }

    override fun parseArgment(arg: Bundle?) {
        unionId = arg?.getString(ValConfig.IT_VALUE_1, "") ?: ""
        mVerifyCodeManger = VerifyCodeManager(binding.tvAuthCodeSender)
        mLoginPrivateAgreementDelegate = LoginPrivateAgreementDelegate(
            binding.cbAgreement, {
                binding.cbAgreement.isChecked = it
                if (it) {
                    binding.layBindHint.visibility = View.GONE
                }
            }, null
        )
        mLoginPrivateAgreementDelegate?.create()

    }

    //点击登陆
    private fun clickLogin() {
        if (!checkPrivateAgreementState()) {
            return
        }

        binding.tvLogin.isEnabled = false
        (parentFragment as? FragLoginPage)?.mLoginType = LoginType.captcha
        clearEditTextFocus(binding.tvLogin)
        showAlermDlg("加载中...", false, false)
        mInputPhoneNum = binding.inputPhone.text.toString()
        val smsCode = binding.cetAuthCode.text.toString()
        LoginBuilder.reqWechatBindPhone(unionId, mInputPhoneNum, smsCode, 0) {
            if (activity == null || activity?.isFinishing == true) {
                return@reqWechatBindPhone
            }
            val responseBody = it.mData as? LoginBody
            val bindOther = TextUtils.equals(responseBody?.hasBindotherAccount, "1")
            if (bindOther) {
                //被其他绑定
                binding.tvLogin.isEnabled = true
                showAlermDlg(null, 0)
                get(IWechatProvider::class.java).showBindDialog(
                    "该微信已绑定其他账号，需要取消原绑定关系后，才可以绑定当前微信",
                    true,
                    this@FragBindPhone
                )
                return@reqWechatBindPhone
            }

            if (it.isSuccess && it.mData != null) {
                val body = it.mData as LoginBody
                hkCustNo = body.hkCustNo
                if (body.custState == "1") {
                    LogUtils.pop("注册成功")
                }
                get(ILoginProvider::class.java).saveLoginInfo(
                    hkCustNo ?: "",
                    LoginType.captcha,
                    "",
                    mInputPhoneNum ?: "",
                    "+86"
                )
                get(ILoginProvider::class.java).afterLoginSuccess(this@FragBindPhone)
                UserDataHelper.scheduleRemoteFetch(DataIds.ID_USER_INFO, FetchAction.dataChanged)
                UserDataHelper.scheduleRemoteFetch(DataIds.ID_USER_MESSAGE, FetchAction.dataChanged)
                UserDataHelper.scheduleRemoteFetch(DataIds.ID_USER_HOLD, FetchAction.dataChanged)
                //刷新微信绑定信息
                UserDataHelper.getDataManager(DataIds.ID_USER_WECHAT_BIND)
                    .scheduleRemoteFetch(FetchAction.dataChanged)
                HbAnalytics.onActive(ACTIVE_TYPE.ACTIVE_LOGIN, hkCustNo, true)
                showAlermDlg(null, 0)
            } else {
                showAlermDlg(null, 0)
                val headInfo = it.mErr.extras as? HeaderInfo
                if (TextUtils.equals(headInfo?.responseCode, Constants.ERROR_MSG_CODE)) {
                    LogUtils.pop("验证码错误，请重新输入")
                } else if (TextUtils.equals(headInfo?.responseCode, Constants.ERROR_MSG_CODE_TIMEOUT)) {
                    LogUtils.pop("验证码已过期，请重新获取")
                } else if (TextUtils.equals(
                        headInfo?.responseCode,
                        Constants.ERROR_LOGIN_HBONENO_RELATED_ERROR
                    )
                ) {
                    DlgRelativeHboneNoErr.Companion.getInstance().show(childFragmentManager, null)
                } else {
                    LogUtils.pop("验证失败，请稍后再试")
                }
            }
            binding.tvLogin.isEnabled = true
        }
    }


    //判断是否勾选了协议, 如果没有勾选, 需要提示,动画效果
    private fun checkPrivateAgreementState(): Boolean {
        val checked: Boolean = binding.cbAgreement.isChecked
        if (!checked) {
            binding.layBindHint.visibility = View.VISIBLE
            mLoginPrivateAgreementDelegate?.startAnimationAndShowTips(binding.layBindHint)
        }
        return checked
    }

    //发送验证码
    private fun clickSendSmScode() {
        val errMsg = VerifyUtil.verifyMobile(binding.inputPhone.text.toString())
        if (errMsg != null) {
            LogUtils.pop("请输入11位数字的大陆手机号")
        } else {
            sendVerifyCode()
        }
    }

    private fun sendVerifyCode() {
        if (null == mVerifyCodeManger) return
        mVerifyCodeManger?.execute()
        binding.tvAuthCodeSender.isEnabled = false
        val phoneNum = binding.inputPhone.text.toString()
        LoginBuilder.reqSmsVerifyCode(phoneNum, 0) {
            if (activity == null || activity?.isFinishing == true) {
                return@reqSmsVerifyCode
            }
            if (it.isSuccess && it.mData != null) {
                val smsBody = it.mData as LoginSmsBody
                //发送失败,不需要提示
                if (smsBody.sendSuccess()) {
                    LogUtils.pop("验证码已发送至您的手机，请注意查收~")
                    if (ApkConfig.getDebugType()) {
                        binding.cetAuthCode.setText("111111")
                    }
                } else {
                    LogUtils.pop("系统异常，请稍后再试")
                }
            } else {
                mVerifyCodeManger?.resetByError(it.mErr)
                LogUtils.pop("系统异常，请稍后再试")
            }
        }
    }

    override fun onFocusChange(v: EditText?, hasFocus: Boolean) {
        /*
         * 输入框失焦时校验格式:
         * 若输入的手机号格式为11位数字 且 首数字为1，则校验通过；
         * 否则toast提示：请输入11位数字的大陆手机号
         */
        if (v == binding.inputPhone && !hasFocus) {
            if (userVisibleHint && !StrUtils.isMobileTel(binding.inputPhone.text.toString())) {
                if (mNeedShowHint) {
                    LogUtils.pop("请输入11位数字的大陆手机号")
                }
            }
        }
        mNeedShowHint = true
    }

    override fun onSelectionChanged(v: EditText?, selStart: Int, selEnd: Int) {
        if (v?.id == R.id.input_account) {
            if (!TextUtils.isEmpty(binding.inputPhone.text?.toString())) {
                binding.inputPhone.setSelection(binding.inputPhone.text.toString().length)
            }
        }
    }

    override fun onTextChanged(
        v: EditText?,
        s: CharSequence?,
        start: Int,
        before: Int,
        count: Int
    ) {
        if (v == binding.inputPhone) {
            setAuthCodeEnable(s.toString())
        }
    }

    //设置验证码是否可点(输入的内容是11位,且为1开头)
    private fun setAuthCodeEnable(strPhone: String?) {
        if (isBindNotExisted()) {
            return
        }
        val codeSend: String = binding.tvAuthCodeSender.text.toString()
        val isSending = !StrUtils.isEmpty(codeSend) && codeSend.contains("s") //是否正在倒计时
        binding.tvAuthCodeSender.isEnabled = StrUtils.isMobileTel(strPhone) && !isSending
        mVerifyCodeManger?.setCurPhoneNum(strPhone)
        binding.viewClickVerify.visibility = if (StrUtils.isMobileTel(strPhone)) {
            View.GONE
        } else {
            View.VISIBLE
        }
    }

}