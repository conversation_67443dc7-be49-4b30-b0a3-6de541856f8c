package com.howbuy.global.login_impl.captcha

import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import com.howbuy.account.UserDataHelper
import com.howbuy.account.remote.FetchAction
import com.howbuy.analytics.common.ACTIVE_TYPE
import com.howbuy.component.widgets.ClearableEdittext.IEditChanged
import com.howbuy.fund.base.IWechatProvider
import com.howbuy.fund.base.analytics.HbAnalytics
import com.howbuy.fund.base.config.ApkConfig
import com.howbuy.fund.base.entity.WeChatLoginResult
import com.howbuy.fund.base.frag.AbsFragViewBinding
import com.howbuy.fund.base.router.RouterHelper
import com.howbuy.fund.net.entity.common.HeaderInfo
import com.howbuy.global.data_api.DataIds
import com.howbuy.global.data_api.get
import com.howbuy.global.login_api.ILoginPrivateAgreementDelegate
import com.howbuy.global.login_api.ILoginProvider
import com.howbuy.global.login_api.LoginRouterPath
import com.howbuy.global.login_api.LoginType
import com.howbuy.global.login_impl.Constants.ERROR_LOGIN_HBONENO_RELATED_ERROR
import com.howbuy.global.login_impl.Constants.ERROR_MSG_CODE
import com.howbuy.global.login_impl.Constants.ERROR_MSG_CODE_TIMEOUT
import com.howbuy.global.login_impl.FragLoginPage
import com.howbuy.global.login_impl.LoginAccountMgr
import com.howbuy.global.login_impl.LoginBuilder
import com.howbuy.global.login_impl.LoginInfMgr
import com.howbuy.global.login_impl.LoginPrivateAgreementDelegate
import com.howbuy.global.login_impl.R
import com.howbuy.global.login_impl.VerifyCodeManager
import com.howbuy.global.login_impl.databinding.FragLoginWithCaptchaLayoutBinding
import com.howbuy.global.login_impl.entity.LoginBody
import com.howbuy.global.login_impl.entity.LoginSmsBody
import com.howbuy.lib.utils.InputTargetVisable
import com.howbuy.lib.utils.LogUtils
import com.howbuy.lib.utils.StrUtils
import com.howbuy.lib.utils.VerifyUtil
import com.howbuy.lib.utils.ViewUtils
import com.howbuy.router.proxy.Invoker

/**
 * description.
 * tao.liang
 * 2024/2/19
 */
class FragLoginWithCaptcha : AbsFragViewBinding<FragLoginWithCaptchaLayoutBinding>(), IEditChanged {

    //是否显示了 收不到短信提示文案
    private var changePhoneNum = ""
    private var mVerifyCodeManger: VerifyCodeManager? = null
    private var mLoginPrivateAgreementDelegate: ILoginPrivateAgreementDelegate? = null

    //当切换 tab时失去焦点时,验证码登录页面,不要弹出这个 11位大陆号的提示
    var mNeedShowHint = true

    private var hkCustNo: String? = ""
    private var mInputPhoneNum: String? = ""

    override fun createViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragLoginWithCaptchaLayoutBinding {
        return FragLoginWithCaptchaLayoutBinding.inflate(inflater, container, false)
    }

    override fun getFragLayoutId(): Int {
        return R.layout.frag_login_with_captcha_layout
    }

    override fun stepAllViews(root: View?, savedInstanceState: Bundle?) {
        super.stepAllViews(root, savedInstanceState)
        binding.tvLogin.isEnabled = false
        binding.cbAgreement.isChecked = false
        binding.inputPhone.setEditChangedListener(this)
        binding.cetAuthCode.setEditChangedListener(this)
        InputTargetVisable(binding.tvLogin)
            .addVeriyType(
                InputTargetVisable.FieldVeriyType(
                    InputTargetVisable.FieldVeriyType.TYPE_PHONE,
                    11,
                    binding.inputPhone
                )
            )
            .addVeriyType(
                InputTargetVisable.FieldVeriyType(
                    InputTargetVisable.FieldVeriyType.TYPE_MIN_LENGTH,
                    6,
                    binding.cetAuthCode
                )
            )
        binding.tvAuthCodeSender.isEnabled = false
        binding.viewClickVerify.visibility = View.VISIBLE
        binding.tvAuthCodeSender.setOnClickListener {
            val curPhoneNum = binding.inputPhone.text.toString()
            if (TextUtils.equals("重新发送", binding.tvAuthCodeSender.text.toString())) {
                //若修改手机号，首次点“获取验证码”按钮，隐藏提示语；再次点击“重新发送”按钮，展示提示语。
                if (!TextUtils.equals(changePhoneNum, curPhoneNum)) {
                    binding.trgVerifyCodeHint.visibility = View.GONE
                    binding.tvVerifyCodeHint.visibility = View.GONE
                } else {
                    binding.trgVerifyCodeHint.visibility = View.VISIBLE
                    binding.tvVerifyCodeHint.visibility = View.VISIBLE
                }
            }
            changePhoneNum = curPhoneNum
            clickSendSmScode()
        }
        binding.viewClickVerify.setOnClickListener {
            LogUtils.pop("请输入11位数字的大陆手机号")
        }

        binding.tvLogin.setOnClickListener {
            clickLogin()
            HbAnalytics.onClick("610010")
        }
        binding.llContent.setOnTouchListener { v, event ->
            clearEditTextFocus(v)
            false
        }

        val showWechatLogin =
            Invoker.getInstance().navigation(IWechatProvider::class.java).wechatEnable()
        binding.thirdLogin.visibility = if (showWechatLogin) View.VISIBLE else View.GONE
        binding.thirdLogin.findViewById<View>(R.id.wechat_auth).setOnClickListener {
            HbAnalytics.onClick("613620")
            if (binding.cbAgreement.isChecked) {
                showAlermDlg("加载中", true, true)
                get(IWechatProvider::class.java).wechatLogin(
                    activity
                ) { t ->
                    showAlermDlg(null, 0)
                    val wechatLoginResult = t?.mData as? WeChatLoginResult
                    //LogUtils.pop("登录成功:${wechatLoginResult != null}")
                    //是否绑定手机,没绑定手机之前都算未登录
                    if (wechatLoginResult?.isBindHk == "1") {
                        //已绑定客户号,算登录成功
                        if (TextUtils.isEmpty(wechatLoginResult.hkCustNo)) {
                            //返回的香港客户号为空,不正常
                            return@wechatLogin
                        }
                        //refreshByWeChatLogin = true
                        //先保存用户信息,后边查用户信息需要
                        get(ILoginProvider::class.java).saveLoginInfo(
                            wechatLoginResult.hkCustNo!!,
                            LoginType.wechat,
                            "",
                            "",
                            ""
                        )
                        //afterLoginSuccess最终会触发LoginObserver
                        get(ILoginProvider::class.java).afterLoginSuccess(this@FragLoginWithCaptcha)
                        UserDataHelper.scheduleRemoteFetch(
                            DataIds.ID_USER_INFO,
                            FetchAction.dataChanged
                        )
                        UserDataHelper.scheduleRemoteFetch(
                            DataIds.ID_USER_LICENSEE,
                            FetchAction.dataChanged
                        )
                        UserDataHelper.scheduleRemoteFetch(
                            DataIds.ID_USER_MESSAGE,
                            FetchAction.dataChanged
                        )
                        UserDataHelper.scheduleRemoteFetch(
                            DataIds.ID_USER_HOLD,
                            FetchAction.dataChanged
                        )
                        //刷新微信绑定信息
                        UserDataHelper.getDataManager(DataIds.ID_USER_WECHAT_BIND)
                            .scheduleRemoteFetch(FetchAction.dataChanged)
                        HbAnalytics.onActive(ACTIVE_TYPE.ACTIVE_LOGIN, hkCustNo, true)

                    } else {
                        //未绑定手机号,跳转绑定手机号
                        RouterHelper.launchFrag(this, LoginRouterPath.PATH_BIND_PHONE)
                    }


                }
            } else {
                binding.layBindHint.visibility = View.VISIBLE
                mLoginPrivateAgreementDelegate?.startAnimationAndShowTips(binding.layBindHint)
            }
        }

    }

    fun clearEditTextFocus(v: View?) {
        ViewUtils.showKeybord(v, false)
        binding.inputPhone.clearFocus()
        binding.cetAuthCode.clearFocus()
    }

    override fun parseArgment(arg: Bundle?) {
        mVerifyCodeManger = VerifyCodeManager(binding.tvAuthCodeSender)
        mLoginPrivateAgreementDelegate = LoginPrivateAgreementDelegate(
            binding.cbAgreement,
            { t -> (parentFragment as? FragLoginPage)?.mCheckLiveData?.postValue(t) }, null
        )
        mLoginPrivateAgreementDelegate?.create()
        (parentFragment as? FragLoginPage)?.mCheckLiveData?.observe(this) {
            binding.cbAgreement.isChecked = it
            if (it) {
                binding.layBindHint.visibility = View.GONE
            }
        }
    }

    //点击登陆
    private fun clickLogin() {
        if (!checkPrivateAgreementState()) {
            return
        }
        binding.tvLogin.isEnabled = false
        (parentFragment as? FragLoginPage)?.mLoginType = LoginType.captcha
        clearEditTextFocus(binding.tvLogin)
        showAlermDlg("加载中...", false, false)
        mInputPhoneNum = binding.inputPhone.text.toString()
        val smsCode = binding.cetAuthCode.text.toString()
        LoginBuilder.reqLoginWithCaptcha(mInputPhoneNum, smsCode, 0) {
            if (activity == null || activity?.isFinishing == true) {
                return@reqLoginWithCaptcha
            }
            if (it.isSuccess && it.mData != null) {
                val body = it.mData as LoginBody
                hkCustNo = body.hkCustNo
                setLoginInfo()
                //请求用户信息
                (parentFragment as? FragLoginPage)?.reqUserInfoByFetchCallback()
                UserDataHelper.scheduleRemoteFetch(DataIds.ID_USER_INFO, FetchAction.dataChanged)
                UserDataHelper.scheduleRemoteFetch(DataIds.ID_USER_LICENSEE, FetchAction.dataChanged)
                UserDataHelper.scheduleRemoteFetch(DataIds.ID_USER_MESSAGE, FetchAction.dataChanged)
                UserDataHelper.scheduleRemoteFetch(DataIds.ID_USER_HOLD, FetchAction.dataChanged)
                HbAnalytics.onActive(ACTIVE_TYPE.ACTIVE_LOGIN, hkCustNo, true)
            } else {
                showAlermDlg(null, 0)
                val headInfo = it.mErr.extras as? HeaderInfo
                if (TextUtils.equals(headInfo?.responseCode, ERROR_MSG_CODE)) {
                    LogUtils.pop("验证码错误，请重新输入")
                } else if (TextUtils.equals(headInfo?.responseCode, ERROR_MSG_CODE_TIMEOUT)) {
                    LogUtils.pop("验证码已过期，请重新获取")
                } else if (TextUtils.equals(headInfo?.responseCode, ERROR_LOGIN_HBONENO_RELATED_ERROR)) {
                    DlgRelativeHboneNoErr.getInstance().show(childFragmentManager, null)
                } else {
                    LogUtils.pop("验证失败，请稍后再试")
                }
            }
            binding.tvLogin.isEnabled = true
        }
    }

    /**
     * 保存用户登录信息
     */
    private fun setLoginInfo() {
        LoginInfMgr.loginTypeInfo?.refreshUserInfo(
            hkCustNo, LoginType.captcha,
            null, mInputPhoneNum, "+86"
        )
        LoginAccountMgr.saveLoginInfo(LoginInfMgr.loginTypeInfo)
    }


    //判断是否勾选了协议, 如果没有勾选, 需要提示,动画效果
    private fun checkPrivateAgreementState(): Boolean {
        val checked: Boolean = binding.cbAgreement.isChecked
        if (!checked) {
            binding.layBindHint.visibility = View.VISIBLE
            mLoginPrivateAgreementDelegate?.startAnimationAndShowTips(binding.layBindHint)
        }
        return checked
    }

    //发送验证码
    private fun clickSendSmScode() {
        val errMsg = VerifyUtil.verifyMobile(binding.inputPhone.text.toString())
        if (errMsg != null) {
            LogUtils.pop("请输入11位数字的大陆手机号")
        } else {
            sendVerifyCode()
        }
    }

    private fun sendVerifyCode() {
        if (null == mVerifyCodeManger) return
        mVerifyCodeManger?.execute()
        binding.tvAuthCodeSender.isEnabled = false
        val phoneNum = binding.inputPhone.text.toString()
        LoginBuilder.reqSmsVerifyCode(phoneNum, 0) {
            if (activity == null || activity?.isFinishing == true) {
                return@reqSmsVerifyCode
            }
            if (it.isSuccess && it.mData != null) {
                val smsBody = it.mData as LoginSmsBody
                //发送失败,不需要提示
                if (smsBody.sendSuccess()) {
                    LogUtils.pop("验证码已发送至您的手机，请注意查收~")
                    if (ApkConfig.getDebugType()) {
                        binding.cetAuthCode.setText("111111")
                    }
                } else {
                    LogUtils.pop("系统异常，请稍后再试")
                }
            } else {
                mVerifyCodeManger?.resetByError(it.mErr)
                LogUtils.pop("系统异常，请稍后再试")
            }
        }
    }

    override fun onFocusChange(v: EditText?, hasFocus: Boolean) {
        /*
         * 输入框失焦时校验格式:
         * 若输入的手机号格式为11位数字 且 首数字为1，则校验通过；
         * 否则toast提示：请输入11位数字的大陆手机号
         */
        if (v == binding.inputPhone && !hasFocus) {
            if (userVisibleHint && !StrUtils.isMobileTel(binding.inputPhone.text.toString())) {
                if (mNeedShowHint) {
                    LogUtils.pop("请输入11位数字的大陆手机号")
                }
            }
        }
        mNeedShowHint = true
    }

    override fun onSelectionChanged(v: EditText?, selStart: Int, selEnd: Int) {
        if (v?.id == R.id.input_account) {
            if (!TextUtils.isEmpty(binding.inputPhone.text?.toString())) {
                binding.inputPhone.setSelection(binding.inputPhone.text.toString().length)
            }
        }
    }

    override fun onTextChanged(
        v: EditText?,
        s: CharSequence?,
        start: Int,
        before: Int,
        count: Int
    ) {
        if (v == binding.inputPhone) {
            setAuthCodeEnable(s.toString())
        }
    }

    //设置验证码是否可点(输入的内容是11位,且为1开头)
    private fun setAuthCodeEnable(strPhone: String?) {
        if (isBindNotExisted()) {
            return
        }
        val codeSend: String = binding.tvAuthCodeSender.text.toString()
        val isSending = !StrUtils.isEmpty(codeSend) && codeSend.contains("s") //是否正在倒计时
        binding.tvAuthCodeSender.isEnabled = StrUtils.isMobileTel(strPhone) && !isSending
        mVerifyCodeManger?.setCurPhoneNum(strPhone)
        binding.viewClickVerify.visibility = if (StrUtils.isMobileTel(strPhone)) {
            View.GONE
        } else {
            View.VISIBLE
        }
    }

}