package com.howbuy.global.login_impl.wechatlogin

import android.app.Activity
import com.howbuy.lib.utils.LogUtils
import com.umeng.socialize.UMAuthListener
import com.umeng.socialize.UMShareAPI
import com.umeng.socialize.UMShareConfig
import com.umeng.socialize.bean.SHARE_MEDIA

private const val TAG = "WechatAuthUtil"

/**
 * 微信鉴权
 */
object WechatAuthUtil {

    fun auth(context: Activity?, listener: AuthListener<MutableMap<String, String>?>? = null) {
        if (context == null) {
            listener?.onFailed()
            return
        }
        val config = UMShareConfig()
        config.isNeedAuthOnGetUserInfo(true)
        UMShareAPI.get(context).setShareConfig(config)
        UMShareAPI
            .get(context)
            .getPlatformInfo(context, SHARE_MEDIA.WEIXIN, object : UMAuthListener {
                override fun onStart(p0: SHARE_MEDIA?) {
                    LogUtils.d(TAG, "onStart-$p0")
                }

                override fun onComplete(
                    p0: SHARE_MEDIA?,
                    p1: Int,
                    p2: MutableMap<String, String>?
                ) {
                    /**
                     * country=,
                     * unionid=oSx6K1Esux78QH-umCTdDyy_tHWQ,
                     * gender=0,
                     * city=,
                     * openid=o4Jch69RZTNx-38g7Cyije9wCizI,
                     * language=,
                     * profile_image_url=https://thirdwx.qlogo.cn/mmopen/vi_32/nHTeBrP98wxWY9ccCpuHVkURtUFKKYDZAgLnicXiaX5410Txgy6Ju2tMW0P9UKssAwwrzeHStXlxibYXRbVSAmTBg/132,
                     * accessToken=91_Y9odjAfxs2EX0RFhPFCF57zZIrQTfoG-dyXd8oBMYDN3Xv6o0XLESNdhV3SWh3GGFSHsF11R_0PCt-ZaIxQxwu8B_eeLYzbZIONxlxeJ5js,
                     * access_token=91_Y9odjAfxs2EX0RFhPFCF57zZIrQTfoG-dyXd8oBMYDN3Xv6o0XLESNdhV3SWh3GGFSHsF11R_0PCt-ZaIxQxwu8B_eeLYzbZIONxlxeJ5js,
                     * uid=oSx6K1Esux78QH-umCTdDyy_tHWQ,
                     * province=,
                     * screen_name=蓝色记忆,
                     * name=蓝色记忆,
                     * iconurl=https://thirdwx.qlogo.cn/mmopen/vi_32/nHTeBrP98wxWY9ccCpuHVkURtUFKKYDZAgLnicXiaX5410Txgy6Ju2tMW0P9UKssAwwrzeHStXlxibYXRbVSAmTBg/132,
                     * expiration=1745738090827,
                     * expires_in=1745738090827,
                     * refreshToken=91_2tajmjBJtSVb2B7jsqcmaTDPb2H_ib_SyaP4B5vvccKuYtyRwVMa7nLnsdX1gtccxgSPf2Cu343Ws7Q7qq1mEOdiw8xG-EXnDiVcfN94Z6I
                     */
                    LogUtils.d(TAG, "onComplete:unionId: ${p2?.get("unionid")}")
                    listener?.onSuccess(p2)
                }

                override fun onError(p0: SHARE_MEDIA?, p1: Int, p2: Throwable?) {
                    LogUtils.d(TAG, "onError-$p2")
                    listener?.onFailed()
                }

                override fun onCancel(p0: SHARE_MEDIA?, p1: Int) {
                    LogUtils.d(TAG, "onCancel")
                    listener?.onCancelled()
                }
            })
    }

    fun deleteAuth(
        context: Activity?,
        listener: AuthListener<MutableMap<String, String>?>? = null
    ) {
        if (context == null) {
            return
        }
        UMShareAPI
            .get(context)
            .deleteOauth(context, SHARE_MEDIA.WEIXIN, object : UMAuthListener {
                override fun onStart(p0: SHARE_MEDIA?) {
                    LogUtils.d(TAG, "deleteAuth onStart")
                }

                override fun onComplete(
                    p0: SHARE_MEDIA?,
                    p1: Int,
                    p2: MutableMap<String, String>?
                ) {
                    LogUtils.d(TAG, "deleteAuth onComplete")
                    listener?.onSuccess(null)
                }

                override fun onError(p0: SHARE_MEDIA?, p1: Int, p2: Throwable?) {
                    LogUtils.d(TAG, "deleteAuth onError")
                    listener?.onFailed()
                }

                override fun onCancel(p0: SHARE_MEDIA?, p1: Int) {
                    LogUtils.d(TAG, "deleteAuth onCancel")
                    listener?.onCancelled()
                }
            })
    }

}
