package com.howbuy.global.login_impl.bind.adapter

import androidx.appcompat.widget.SwitchCompat
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.howbuy.global.login_impl.R
import com.howbuy.global.login_impl.entity.ThirdAuthInfo

/**
 * 第三方授权列表适配器
 */
class AdpThirdAuth : BaseQuickAdapter<ThirdAuthInfo, BaseViewHolder>(R.layout.item_third_auth) {


    override fun convert(holder: BaseViewHolder, item: ThirdAuthInfo) {
        holder.setText(R.id.tv_name, item.authName)
        holder.getView<SwitchCompat>(R.id.switch_wechat_auth).isChecked = item.isBinding

    }
} 