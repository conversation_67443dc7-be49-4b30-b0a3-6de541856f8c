package com.howbuy.global.login_impl

import com.howbuy.fund.base.mvp.rx.RxHttpObservable
import com.howbuy.fund.net.HttpCaller
import com.howbuy.fund.net.cache.CacheMode
import com.howbuy.fund.net.entity.http.ReqNetOpt
import com.howbuy.fund.net.entity.http.ReqResult
import com.howbuy.fund.net.http.ReqParams
import com.howbuy.fund.net.http.RequestContentType
import com.howbuy.fund.net.interfaces.IReqNetFinished
import com.howbuy.fund.net.util.XUtils
import com.howbuy.global.data_api.apiUserInfo
import com.howbuy.global.login_impl.entity.BindOtherAccountBody
import com.howbuy.global.login_impl.entity.CountryBody
import com.howbuy.global.login_impl.entity.EmailSuffixBody
import com.howbuy.global.login_impl.entity.IdCardTypeBody
import com.howbuy.global.login_impl.entity.LoginBody
import com.howbuy.global.login_impl.entity.LoginSmsBody
import com.howbuy.lib.utils.SysUtils
import io.reactivex.Observable
import java.lang.reflect.Type

/**
 * description.
 * 登录模块接口请求统一管理
 * tao.liang
 * 2024/2/21
 */
object LoginBuilder {

    //验证码登录接口
    private const val CRM_CGI_HKACCOUNT_LOGIN_LOGINAPPBYMOBILEANDVERIFYCODE =
        "CRM_CGI_HKACCOUNT_LOGIN_LOGINAPPBYMOBILEANDVERIFYCODE"

    //微信登录绑定手机号
    private const val CRM_CGI_HKACCOUNT_LOGIN_AND_BIND = "CRM_CGI_HKACCOUNT_LOGIN_AND_BIND"

    //获取验证码
    private const val CRM_CGI_HKACCOUNT_VERIFYCODE_GETAPPLOGINMSGVERIFYCODE =
        "CRM_CGI_HKACCOUNT_VERIFYCODE_GETAPPLOGINMSGVERIFYCODE"

    //证件号登录
    private const val CRM_CGI_HKACCOUNT_LOGIN_LOGINAPPBYMOBILEORIDNO = "CRM_CGI_HKACCOUNT_LOGIN_LOGINAPPBYMOBILEORIDNO"

    //邮件后缀
    private const val CRM_CGI_HKACCOUNT_COMMON_GETEMAILSUFFIXLIST = "CRM_CGI_HKACCOUNT_COMMON_GETEMAILSUFFIXLIST"

    //国家列表
    private const val CRM_CGI_HKACCOUNT_COMMON_GETCOUNTRY = "CRM_CGI_HKACCOUNT_COMMON_GETCOUNTRY"

    //根据国家,查询证件支持的证件类型
    private const val CRM_CGI_HKACCOUNT_COMMON_GETIDTYPELIST = "CRM_CGI_HKACCOUNT_COMMON_GETIDTYPELIST"

    /**
     * 解绑三方账号
     * /hkAccount/otherLogin/unBindHkCustNo
     */
    const val CRM_CGI_HKACCOUNT_OHTERLGION_UNBINDHKCUSTNO = "CRM_CGI_HKACCOUNT_OHTERLGION_UNBINDHKCUSTNO"

    /**
     * 绑定三方账号
     * /hkAccount/otherLogin/bindHkCustNo
     */
    const val CRM_CGI_HKACCOUNT_OHTERLGION_BINDHKCUSTNO = "CRM_CGI_HKACCOUNT_OHTERLGION_BINDHKCUSTNO"


    /**
     * 创建请求参数对象
     */
    private fun createReqParams(
        uri: String?,
        clazz: Type?,
        post: Boolean,
        cacheMode: CacheMode?,
        tradeParseMode: Boolean,
        handType: Int,
        callback: IReqNetFinished?,
        paramsMap: HashMap<String, Any?>?
    ): ReqParams {
        val url = XUtils.getUrl(uri)
        val safePolicy = XUtils.getSafePolicy(uri)
        val encrypt = XUtils.isNeedEncryption(uri)
        val needEnvelope = XUtils.isNeedEnvelope(uri)
        val needSign = XUtils.isNeedSign(uri)
        val reqParams = ReqParams()
        reqParams.url = url
        reqParams.uriKey = uri
        reqParams.cls = clazz
        reqParams.isPost = post
        reqParams.reqTag = uri
        reqParams.isTradeParseMode = tradeParseMode
        //所有接口都传公共参数
        reqParams.needPublicParams = true
        reqParams.safePolicy = safePolicy
        //post by json 格式
        reqParams.requestContentType = RequestContentType.JSON
        reqParams.isEncrypt = encrypt
        reqParams.cacheMode = cacheMode
        reqParams.params = paramsMap
        reqParams.bytes = null
        reqParams.handType = handType
        reqParams.reqNetFinished = callback
        reqParams.startTime = System.currentTimeMillis()
        reqParams.isNeedEnvelope = needEnvelope
        reqParams.isNeedSign = needSign
        return reqParams
    }

    /**
     * 验证码登录
     * @param mobile 用户输入的手机号
     * @param verifyCode 短信验证码
     */
    fun reqLoginWithCaptcha(
        mobile: String?,
        verifyCode: String,
        handType: Int,
        callback: IReqNetFinished?
    ) {
        val map = HashMap<String, Any?>()
        map["mobile"] = mobile
        map["verifyCode"] = verifyCode
        map["systemVersion"] = SysUtils.getRelease()
        map["deviceName"] = SysUtils.getDeviceName()

        val params = createReqParams(
            CRM_CGI_HKACCOUNT_LOGIN_LOGINAPPBYMOBILEANDVERIFYCODE,
            LoginBody::class.java, true, null,
            true, handType, callback, map
        )
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 微信登录绑定手机号
     */
    fun reqWechatBindPhone(
        otherAccount: String,
        mobile: String?,
        verifyCode: String,
        handType: Int,
        callback: IReqNetFinished?
    ) {
        val map = HashMap<String, Any?>()
        map["mobile"] = mobile
        map["verifyCode"] = verifyCode
        map["systemVersion"] = SysUtils.getRelease()
        map["deviceName"] = SysUtils.getDeviceName()
        if (otherAccount.isNotEmpty()) {
            map["otherAccount"] = otherAccount
            map["otherAccountType"] = "weChat"
        }
        val params = createReqParams(
            CRM_CGI_HKACCOUNT_LOGIN_AND_BIND,
            LoginBody::class.java, true, null,
            true, handType, callback, map
        )
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 获取短信验证码
     * @param mobile 用户输入的手机号
     */
    fun reqSmsVerifyCode(
        mobile: String,
        handType: Int,
        callback: IReqNetFinished?
    ) {
        val map = HashMap<String, Any?>()
        map["mobile"] = mobile
        val params = createReqParams(
            CRM_CGI_HKACCOUNT_VERIFYCODE_GETAPPLOGINMSGVERIFYCODE,
            LoginSmsBody::class.java, true, null,
            true, handType, callback, map
        )
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 密码登录
     * @param loginAcctType 登录方式 必须，0-证件；1-手机 2-邮箱
     * @param loginPassword 登录密码 必须
     * @param areaCode 地区码 手机号非空时必须(登录方式=1)
     * @param mobile 手机号 登录方式=1-手机时必须(登录方式=1)
     * @param idAreaCode 证件地区码(登录方式=0)
     * @param idType 证件类型 证件号码非空时必须(登录方式=0)
     * @param idNo 证件号码 登录方式=0-证件时必须
     * @param email 邮箱
     */
    fun reqLoginWithPassword(
        loginAcctType: String?,
        loginPassword: String?,
        areaCode: String?,
        mobile: String?,
        idAreaCode: String?,
        idType: String?,
        idNo: String?,
        email: String?,
        handType: Int, callback: IReqNetFinished?
    ) {
        val map = HashMap<String, Any?>()
        map["loginAcctType"] = loginAcctType
        map["idNo"] = idNo
        map["loginPassword"] = loginPassword
        map["areaCode"] = areaCode
        map["mobile"] = mobile
        map["idAreaCode"] = idAreaCode
        map["idType"] = idType
        map["idNo"] = idNo
        map["email"] = email
        map["systemVersion"] = SysUtils.getRelease()
        map["deviceName"] = SysUtils.getDeviceName()
        val params = createReqParams(
            CRM_CGI_HKACCOUNT_LOGIN_LOGINAPPBYMOBILEORIDNO,
            LoginBody::class.java, true, null,
            true, handType, callback, map
        )
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 获取排名前10的邮箱后缀(非加密接口)
     */
    fun reqEmailSuffix(
        handType: Int,
        callback: IReqNetFinished?
    ) {
        val params = createReqParams(
            CRM_CGI_HKACCOUNT_COMMON_GETEMAILSUFFIXLIST,
            EmailSuffixBody::class.java, true, null,
            true, handType, callback, null
        )
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 获取登录时,选择国家列表(非加密接口)
     */
    fun reqCountryList(
        handType: Int,
        callback: IReqNetFinished?
    ) {
        val params = createReqParams(
            CRM_CGI_HKACCOUNT_COMMON_GETCOUNTRY,
            CountryBody::class.java, true, null,
            true, handType, callback, null
        )
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 根据选择的国家,获取登录时,可用的证件类型(非加密接口)
     * @param countryCode 国家码
     */
    fun reqIdCardListByCountry(
        countryCode: String?,
        handType: Int,
        callback: IReqNetFinished?
    ) {
        val map = HashMap<String, Any?>()
        map["countryCode"] = countryCode
        val params = createReqParams(
            CRM_CGI_HKACCOUNT_COMMON_GETIDTYPELIST,
            IdCardTypeBody::class.java, true, null,
            true, handType, callback, map
        )
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 绑定第三方账号
     * @param unionId unionId
     * @param bind true 绑定 false 解绑
     */
    fun bindOtherAccount(unionId: String): Observable<ReqResult<ReqNetOpt>> {
        val map = HashMap<String, Any?>()
        map["hkCustNo"] = apiUserInfo().getHkCustNo()
        map["otherAccount"] = "weChat"
        return RxHttpObservable.buildObservable(
            CRM_CGI_HKACCOUNT_OHTERLGION_BINDHKCUSTNO,
//            "https://m1.apifoxmock.com/m1/2829913-1212853-default/hkAccount/otherLogin/bindHkCustNo",
            BindOtherAccountBody::class.java, true, true, null,
            map
        )
    }

    /**
     * 绑定第三方账号
     * @param unionId unionId
     * @param bind true 绑定 false 解绑
     */
    fun unBindOtherAccount(unionId: String): Observable<ReqResult<ReqNetOpt>> {
        val map = HashMap<String, Any?>()
        map["hkCustNo"] = apiUserInfo().getHkCustNo()
        map["otherAccount"] = "weChat"
        return RxHttpObservable.buildObservable(
            CRM_CGI_HKACCOUNT_OHTERLGION_UNBINDHKCUSTNO,
//            "https://m1.apifoxmock.com/m1/2829913-1212853-default/hkAccount/otherLogin/unBindHkCustNo",
            BindOtherAccountBody::class.java,
            true,
            true,
            null,
            map
        )
    }
}