package com.howbuy.global.login_impl.bind

import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.facade.annotation.Route
import com.howbuy.account.UserDataHelper
import com.howbuy.account.remote.FetchAction
import com.howbuy.android.analytics.annotation.pv.PvInfo
import com.howbuy.arouter_intercept_api.IGlobalInterceptCode
import com.howbuy.arouter_intercept_api.Intercept
import com.howbuy.fund.base.IWechatProvider
import com.howbuy.fund.base.analytics.HbAnalytics
import com.howbuy.fund.base.arch.AutoReleaseObserver
import com.howbuy.fund.base.arch.ClearViewModel
import com.howbuy.fund.base.arch.ViewModelHelper
import com.howbuy.fund.base.frag.AbsFragViewBinding
import com.howbuy.fund.base.router.AtyBridgeHelper
import com.howbuy.fund.base.widget.xrecyclerdivider.builder.XLinearBuilder
import com.howbuy.fund.net.entity.http.ReqNetOpt
import com.howbuy.fund.net.entity.http.ReqResult
import com.howbuy.global.data_api.DataIds
import com.howbuy.global.data_api.get
import com.howbuy.global.login_api.LoginRouterPath.PATH_FRAG_THIRD_AUTH_LIST
import com.howbuy.global.login_impl.LoginBuilder
import com.howbuy.global.login_impl.R
import com.howbuy.global.login_impl.bind.adapter.AdpThirdAuth
import com.howbuy.global.login_impl.databinding.FragThirdAuthListBinding
import com.howbuy.global.login_impl.entity.BindOtherAccountBody
import com.howbuy.global.login_impl.entity.ThirdAuthInfo
import com.howbuy.global.login_impl.wechatlogin.AuthListener
import com.howbuy.global.login_impl.wechatlogin.WechatAuthUtil
import com.howbuy.lib.compont.GlobalApp
import com.howbuy.lib.utils.LogUtils
import com.howbuy.lib.utils.StatusBarUtil
import com.howbuy.router.proxy.Invoker

/**
 * author: gen.li
 * date  : 2025/4/28
 * desc  : 账号绑定列表
 */
@PvInfo(pageId = "622140", level = "3", name = "第三方账号绑定页面", className = "FragThirdAuthList")
@Intercept(interceptArray = [IGlobalInterceptCode.GLOBAL_CHECK_LOGIN, IGlobalInterceptCode.GLOBAL_CHECK_ACTIVE])
@Route(path = PATH_FRAG_THIRD_AUTH_LIST)
class FragThirdAuthList : AbsFragViewBinding<FragThirdAuthListBinding>() {

    private val mAdapter: AdpThirdAuth by lazy {
        AdpThirdAuth()
    }
    private val vm by lazy {
        ViewModelHelper.createViewModel(this, ClearViewModel::class.java)
    }

    //正在执行中
    private var taskDoing = false

    override fun createViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragThirdAuthListBinding {
        return FragThirdAuthListBinding.inflate(inflater, container, false)
    }

    override fun getFragLayoutId(): Int = R.layout.frag_third_auth_list

    override fun stepAllViews(root: View?, savedInstanceState: Bundle?) {
        super.stepAllViews(root, savedInstanceState)
        // 设置白色toolbar背景
        val toolBarColor = ContextCompat.getColor(GlobalApp.getApp(), R.color.white)
        StatusBarUtil.setStatusBarLightMode(activity ?: return, toolBarColor, true)
        // 设置标题
        activity?.let {
            AtyBridgeHelper.getAtyEmptyApi(it).actionBarToolBar?.setBackgroundColor(
                toolBarColor
            )
        }

        // 初始化RecyclerView
        binding.recyclerView.apply {
            layoutManager = LinearLayoutManager(activity)
            addItemDecoration(
                XLinearBuilder(context).setSpacing(0.5f)
                    .setShowLastLine(false)
                    .setColorRes(R.color.cl_e5e5e5).build()
            )
            adapter = mAdapter
        }

        // 加载数据
        loadAuthData()
        mAdapter.setOnItemClickListener { _, _, position ->
            handleItemClick(mAdapter.getItemOrNull(position), position)
        }
    }

    private fun handleItemClick(authInfo: ThirdAuthInfo?, position: Int) {
        if (authInfo == null) {
            return
        }
        when (authInfo.authType) {
            1 -> {
                if (authInfo.isBinding) {
                    Invoker.getInstance().navigation(IWechatProvider::class.java).showBindDialog(
                        "确定要解除好买全球与微信的绑定关系吗？",
                        false,
                        this@FragThirdAuthList
                    ) { t ->
                        if (t == true) {
                            doUnBind(authInfo, position)
                        }
                    }
                } else {
                    HbAnalytics.onClick("613640")
                    //微信绑定
                    //用户已经绑定微信的时候,不论用户是否安装微信,都显示入口.所以三方账户列表页存在用户未安装微信,但是能进入的场景
                    if (!get(IWechatProvider::class.java).wechatInstalled()) {
                        //未安装微信
                        LogUtils.pop("请先安装微信客户端")
                        return
                    }
                    showAlermDlg("加载中", true, true)
                    WechatAuthUtil.auth(
                        activity,
                        object : AuthListener<MutableMap<String, String>?> {
                            override fun onSuccess(data: MutableMap<String, String>?) {
                                val unionId = data?.get("unionid")
                                if (TextUtils.isEmpty(unionId)) {
                                    showAlermDlg(null, 0)
                                    return
                                }
                                doBind(authInfo, position, unionId!!)
                            }

                            override fun onFailed() {
                                showAlermDlg(null, 0)
                            }

                            override fun onCancelled() {
                                showAlermDlg(null, 0)
                            }
                        })


                }
            }

            else -> {}
        }

    }

    private fun doBind(authInfo: ThirdAuthInfo, position: Int, unionId: String) {
        if (taskDoing) {
            showAlermDlg(null, 0)
            return
        }
        if (TextUtils.isEmpty(unionId)) {
            showAlermDlg(null, 0)
            return
        }
        taskDoing = true
        //showAlermDlg("加载中", true, true)
        LoginBuilder.bindOtherAccount(unionId)
            .subscribe(object : AutoReleaseObserver<ReqResult<ReqNetOpt>?>(vm) {
                override fun onNext(t: ReqResult<ReqNetOpt>) {
                    taskDoing = false
                    showAlermDlg(null, 0)
                    //是否绑定成功
                    val bindResult = t.mData as? BindOtherAccountBody
                    if (bindResult?.isBindOtherHkCustNo == "1") {
                        //绑定了其他客户号,算绑定失败
                        get(IWechatProvider::class.java).showBindDialog(
                            "该微信已绑定其他账号，需要取消原绑定关系后，才可以绑定当前微信",
                            true,
                            this@FragThirdAuthList
                        )
                    } else if (TextUtils.equals(bindResult?.code, "0000")) {
                        authInfo.isBinding = true
                        LogUtils.pop("已绑定")
                        mAdapter.notifyItemChanged(position)
                        afterOperate()
                    }
                }

                override fun onError(e: Throwable) {
                    taskDoing = false
                    showAlermDlg(null, 0)
                }
            })
    }

    private fun doUnBind(authInfo: ThirdAuthInfo, position: Int) {
        if (taskDoing) {
            return
        }
        val unionId = get(IWechatProvider::class.java).getUnionId() ?: ""
        if (TextUtils.isEmpty(unionId)) {
            return
        }
        WechatAuthUtil.deleteAuth(activity)
        taskDoing = true
        showAlermDlg("加载中", true, true)
        LoginBuilder.unBindOtherAccount(unionId)
            .subscribe(object : AutoReleaseObserver<ReqResult<ReqNetOpt>?>(vm) {
                override fun onNext(t: ReqResult<ReqNetOpt>) {
                    taskDoing = false
                    showAlermDlg(null, 0)
                    authInfo.isBinding = false
                    mAdapter.notifyItemChanged(position)
                    afterOperate()
                    LogUtils.pop("已解除绑定")
                }

                override fun onError(e: Throwable) {
                    taskDoing = false
                    showAlermDlg(null, 0)
                }
            })
    }

    private fun afterOperate() {
        UserDataHelper.getDataManager(DataIds.ID_USER_WECHAT_BIND)
            .scheduleRemoteFetch(FetchAction.dataChanged)
    }


    /**
     * 加载第三方授权数据
     */
    private fun loadAuthData() {
        // 这里可以请求接口获取数据，这里先用模拟数据
        val authList = mutableListOf<ThirdAuthInfo>()
        //获取是否绑定微信
        val isBindWechat =
            Invoker.getInstance().navigation(IWechatProvider::class.java).wechatBindState()

        // 添加微信授权项
        authList.add(
            ThirdAuthInfo(
                authType = 1,
                authName = "微信",
                //是否绑定微信
                isBinding = isBindWechat
            )
        )
        // 设置数据到适配器
        mAdapter.setList(authList)
    }

    override fun parseArgment(arg: Bundle?) {
        // 可以解析传入的参数
    }
}