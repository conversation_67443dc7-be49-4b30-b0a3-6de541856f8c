package com.howbuy.global.login_impl

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import androidx.fragment.app.Fragment
import com.alibaba.android.arouter.facade.callback.NavigationCallback
import com.alibaba.android.arouter.launcher.ARouter
import com.google.gson.GsonUtils
import com.howbuy.account.UserDataHelper
import com.howbuy.account.remote.FetchAction
import com.howbuy.fund.base.config.SpConfig
import com.howbuy.fund.base.config.ValConfig
import com.howbuy.fund.base.dialog.GlobalHintDialog
import com.howbuy.fund.base.nav.NavCompleteCallback
import com.howbuy.fund.base.nav.NavHelper
import com.howbuy.fund.base.storage.CommonStorageUtils
import com.howbuy.fund.net.util.UrlUtils
import com.howbuy.global.data_api.DataIds
import com.howbuy.global.data_api.apiUserInfo
import com.howbuy.global.data_api.isLogined
import com.howbuy.global.login_api.ILoginProvider
import com.howbuy.global.login_api.LoginRouterPath.PATH_ACTIVITY_MAIN
import com.howbuy.global.login_api.LoginType
import com.howbuy.global.login_api.LoginTypeInf
import com.howbuy.global.login_api.constanst.LoginConstants.ACTIVE_LOGIN_IDX
import com.howbuy.global.login_api.constanst.LoginConstants.ACTIVE_PAY_IDX
import com.howbuy.h5.h5config.ParserUriZipResource
import com.howbuy.lib.utils.JsonUtils
import com.howbuy.lib.utils.LogUtils
import com.howbuy.login.LoginManagerImpl
import com.howbuy.router.provider.IWebProvider
import com.howbuy.router.proxy.Invoker
import com.vhall.business.VhallSDK

/**
 * description.
 * 用户登录,账号,状态管理相关
 * tao.liang
 * 2024/2/21
 */
internal object LoginAccountMgr {


    //保存用户登录信息(加密保存)
    const val SF_USER_LOGIN_INFO = "sf_user_login_info"

    /**
     * 是否需要强制登录(接口触发强制登录状态时,设置为true, 跳转到登录页后, 设置为false)
     */
    var needForceReLogin = false

    /**
     * 保存客户号
     */
    fun saveLoginInfo(loginInfo: LoginTypeInf?) {
        CommonStorageUtils.savDataBySpWithEncrypt(SF_USER_LOGIN_INFO, GsonUtils.toJson(loginInfo))
    }


    /**
     * 获取登录用户信息
     * @param fromMemory 是否从内存中获取,否则从sf中获取
     */
    fun getLoginInfoFrom(fromMemory: Boolean): LoginTypeInf? {
        if (fromMemory) {
            return LoginInfMgr.loginTypeInfo
        } else {
            val loginInfo = GsonUtils.toObj(
                CommonStorageUtils.getDataBySpWithDecrypt(SF_USER_LOGIN_INFO),
                LoginTypeInf::class.java
            )
            LoginInfMgr.loginTypeInfo?.refreshUserInfo(loginInfo)
            return LoginInfMgr.loginTypeInfo
        }
    }


    /**
     * 退出登录
     */
    fun loginOut() {
        //清空用户登录信息
        LoginInfMgr.loginTypeInfo?.refreshUserInfo(null)
        UserDataHelper.getDataManager(DataIds.ID_USER_INFO)?.clear()
        UserDataHelper.getDataManager(DataIds.ID_USER_ASSETS)?.clear()
        UserDataHelper.getDataManager(DataIds.ID_USER_HOLD)?.clear()
        //清除缓存文件中的用户信息数据
        CommonStorageUtils.putString(SF_USER_LOGIN_INFO, "")
        //清除二级密码相关设置
        CommonStorageUtils.remove(SpConfig.SF_GESTURE_HINT_DLG_TIME)
        CommonStorageUtils.remove(SpConfig.SF_GESTURE_HINT_NEVER_SHOW)
        CommonStorageUtils.remove(SpConfig.SF_PATTERN_SWITCH)
        CommonStorageUtils.remove(SpConfig.SF_FINGER_SWITCH)
        CommonStorageUtils.remove(SpConfig.SF_PATTERN_VALUE)
        CommonStorageUtils.remove(SpConfig.SF_FINGER_SKIP)
        vhallLogout()
        LoginManagerImpl.getInstance().publishLoginState(false)
    }

    private fun vhallLogout() {
        if (VhallSDK.isInit() && VhallSDK.isLogin()) {
            VhallSDK.logout()
        }
    }

    /**
     * 跳转到主页
     */
    fun launcherToAtyMain(
        tabIndex: Int,
        bundle: Bundle? = null,
        navCompleteCallback: NavigationCallback? = null
    ) {
        var targetBundle = Bundle()
        val paramsKeyIndex = Invoker.getInstance().navigation(ILoginProvider::class.java)
            .getMainActivityParamskeyIndex()
        if (bundle == null) {
            targetBundle.putInt(paramsKeyIndex, tabIndex)
        } else {
            targetBundle = bundle
            targetBundle.putInt(paramsKeyIndex, tabIndex)
        }

        ARouter.getInstance()
            .build(PATH_ACTIVITY_MAIN)
            .with(targetBundle)
            .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP)
            .navigation(null, navCompleteCallback)
    }

    fun canLauncherActivePage(): Boolean {
        val loginType = getLoginType()
        LogUtils.d(
            "LoginAccountMgr",
            "用户是否要激活,登录激活=${
                Invoker.getInstance().navigation(ILoginProvider::class.java)
                    .getLoginInfo(true)?.loginType
            }, 登录账号未激活=${apiUserInfo().loginAccountUnActive()},交易账号未激活=${apiUserInfo().tradeAccountUnActive()}"
        )
        val activeTypeStatus = if (loginType == LoginType.captcha) {
            //验证码登录, 只判断交易账号是否激活
            apiUserInfo().tradeAccountUnActive()
        } else {
            //密码登录, 登录和交易账号激活都要判断
            apiUserInfo().loginAccountUnActive() || apiUserInfo().tradeAccountUnActive()
        }
        // 是否需要激活(前提:已登录&已开户)
        return isLogined() && apiUserInfo().finishOpenAccount() && activeTypeStatus
    }

    private fun getLoginType(): LoginType? {
        return Invoker.getInstance().navigation(ILoginProvider::class.java)
            .getLoginInfo(true)?.loginType
    }


    /**
     * 跳转激活页面(H5)
     */
    fun gotoActivePage(
        activity: Any?, forceActivedH5PageLink:String?, fromPage: String,
        callback: NavCompleteCallback? = null
    ) {
        if (!isLogined()) {
            LogUtils.d("LoginAccountMgr", "未登录状态,激活流程不能调用,,请提前判断登录")
            return
        }
        var activeH5PageUrl = ""
        val loginType = getLoginType()
        if (canLauncherActivePage()) {
            if (TextUtils.isEmpty(forceActivedH5PageLink)){
                if (loginType == LoginType.captcha) {
                    //验证码登录,只需要处理交易账号是否激活
                    if (apiUserInfo().tradeAccountUnActive()) {
                        activeH5PageUrl = ACTIVE_PAY_IDX
                    }
                } else {
                    //密码登录,需要处理登录账号是否激活和交易账号是否激活
                    activeH5PageUrl = if (apiUserInfo().loginAccountUnActive()) {
                        ACTIVE_LOGIN_IDX
                    } else {
                        ACTIVE_PAY_IDX
                    }
                }
            }else{
                activeH5PageUrl = forceActivedH5PageLink?:""
            }
        }

        if (!TextUtils.isEmpty(activeH5PageUrl)) {
            LogUtils.d(
                "LoginAccountMgr",
                "已开户,未激活,跳转激活H5页面, fromPage=$fromPage"
            )
            //须要激活,但不能重复打开多个激活页面
            launcherWebPageByUriKey(activity, activeH5PageUrl, "", true,null) { _, resultData, _ ->
                val data = resultData?.getString(ValConfig.IT_ENTITY)
                val js = JsonUtils.getObject(data)
                var success = false
                if (js != null) {
                    success = TextUtils.equals("1", JsonUtils.getString(js, "resultStates"))
                }
                LogUtils.d(
                    "LoginAccountMgr",
                    "H5激活closeWeibWindow返回结果=$success, 给调用入口页面=$fromPage"
                )
                callback?.onNavComplete(
                    if (success) {
                        Activity.RESULT_OK
                    } else {
                        Activity.RESULT_CANCELED
                    },
                    null,
                    null
                )
                //激活成功,关闭登录页面,回传成功状态到启动EntryActivty入口处
                if (success) {
                    LogUtils.d(
                        "LoginAccountMgr",
                        "激活成功,调用 scheduleRemoteFetch UserInfo dataChanged"
                    )
                    UserDataHelper.scheduleRemoteFetch(
                        DataIds.ID_USER_INFO,
                        FetchAction.dataChanged
                    )
                }
                false
                (activity as? Activity)?.overridePendingTransition(0, 0)
            }
        } else {
            //无须激活,直接进入目标页面
            LogUtils.d("LoginAccountMgr", "已开户,已激活,返回 - RESULT_OK")
            callback?.onNavComplete(Activity.RESULT_OK, null, null)
        }
    }

    /**
     * 跳转登录激活页面
     */
    fun gotoLoginActivePage(activity: Any?, fromPage: String, callback: NavCompleteCallback? = null){
        if (!isLogined()) {
            LogUtils.d("LoginAccountMgr", "未登录状态,激活流程不能调用,,请提前判断登录")
            return
        }
        launcherWebPageByUriKey(activity, ACTIVE_LOGIN_IDX, "", true,null) { _, resultData, _ ->
            val data = resultData?.getString(ValConfig.IT_ENTITY)
            val js = JsonUtils.getObject(data)
            var success = false
            if (js != null) {
                success = TextUtils.equals("1", JsonUtils.getString(js, "resultStates"))
            }
            LogUtils.d("LoginAccountMgr", "H5激活closeWeibWindow返回结果=$success, 给调用入口页面=$fromPage")
            callback?.onNavComplete(
                if (success) {
                    Activity.RESULT_OK
                } else {
                    Activity.RESULT_CANCELED
                },
                null,
                null
            )
            //激活成功,关闭登录页面,回传成功状态到启动EntryActivty入口处
            if (success) {
                LogUtils.d("LoginAccountMgr", "激活成功,调用 scheduleRemoteFetch UserInfo dataChanged")
                UserDataHelper.scheduleRemoteFetch(DataIds.ID_USER_INFO, FetchAction.dataChanged)
            }
            false
            (activity as? Activity)?.overridePendingTransition(0, 0)
        }
    }

    /**
     * 跳转到H5页面:
     */
    fun launcherWebPageByUriKey(
        activity: Any?,
        targetUrlKey: String,
        title: String?,
        needRefreshUserInfo:Boolean,
        urlParams: HashMap<String, String>?,
        callback: NavCompleteCallback? = null
    ) {
        var link = ParserUriZipResource.getUrlWithKey(targetUrlKey)
        if (!urlParams.isNullOrEmpty()) {
            link = UrlUtils.buildUrlRaw(link, urlParams)
        }
        Invoker.getInstance().navigation(IWebProvider::class.java).launchWebView(
            activity,
            NavHelper.obtainArg(title, ValConfig.IT_URL, link, ValConfig.IT_VALUE_6, needRefreshUserInfo), callback
        )
    }

    /**
     * 点击登录按钮, 若输入的手机号/证件号/邮箱不存在关联的香港客户号，未注册, 则弹出该提示框
     */
    fun showAccountUnRegistered(frag: Fragment?) {
        frag?:return
        GlobalHintDialog.getInstance(null, object : GlobalHintDialog.IGlobalHintDlgListener {
            override fun onClick(clickType: GlobalHintDialog.CLiCK_DLG_BTN_TYPE) {
                if (clickType == GlobalHintDialog.CLiCK_DLG_BTN_TYPE.CLICK_SURE) {
                    //点击切换到“验证码登录”页
                    (frag?.parentFragment as? FragLoginPage)?.setIndexByPos(0)
                }
            }
        })
            .setTitle("提示")
            .setMessage("账号不存在，请先注册。使用手机号验证码登录可注册新账号。")
            .setBtnCancelText("取消")
            .setBtnSureText("去注册")
            .show(frag.childFragmentManager, null)
    }

    /**
     * 点击“登录”, 证件号登录时, 需根据“所属国家/地区”+“证件类型”+“证件号”校验账号是否存在
     */
    fun showAccountNotExist(frag: Fragment?) {
        frag?:return
        GlobalHintDialog.getInstance(null, null)
            .setTitle("提示")
            .setMessage("登录失败，请检查所属国家/地区/证件类型/证件号是否正确，如有疑问请咨询您的专属服务人员。")
            .setBtnCancelText("我知道了")
            .show(frag.childFragmentManager, null)
    }



}