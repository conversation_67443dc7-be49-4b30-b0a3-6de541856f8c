package com.howbuy.global.login_impl

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.widget.TextView
import androidx.core.util.Consumer
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentManager
import com.alibaba.android.arouter.facade.annotation.Route
import com.alibaba.android.arouter.facade.callback.NavigationCallback
import com.google.gson.GsonUtils
import com.howbuy.account.UserDataHelper
import com.howbuy.account.remote.ResponseValue
import com.howbuy.android.arch.create
import com.howbuy.fund.base.config.ApkConfig
import com.howbuy.fund.base.config.SpConfig
import com.howbuy.fund.base.config.ValConfig
import com.howbuy.fund.base.dialog.GlobalHintDialog
import com.howbuy.fund.base.nav.NavCompleteCallback
import com.howbuy.fund.base.nav.NavHelper
import com.howbuy.fund.base.router.RouterHelper
import com.howbuy.fund.base.storage.CommonStorageUtils.getString
import com.howbuy.global.data_api.DataIds
import com.howbuy.global.data_api.apiUserInfo
import com.howbuy.global.login_api.ILoginPrivateAgreementDelegate
import com.howbuy.global.login_api.ILoginProvider
import com.howbuy.global.login_api.LoginCallback
import com.howbuy.global.login_api.LoginParams
import com.howbuy.global.login_api.LoginResult
import com.howbuy.global.login_api.LoginRouterPath
import com.howbuy.global.login_api.LoginType
import com.howbuy.global.login_api.LoginTypeInf
import com.howbuy.global.login_api.TempUserInfoForJs
import com.howbuy.global.login_api.constanst.LoginConstants
import com.howbuy.global.login_impl.dialog.DlgGestureHint
import com.howbuy.global.login_impl.password.AccountType
import com.howbuy.lib.aty.AtyMgr
import com.howbuy.lib.compont.GlobalApp
import com.howbuy.lib.utils.DensityUtils
import com.howbuy.lib.utils.LogUtils
import com.howbuy.lib.utils.NetworkUtils
import com.howbuy.lib.utils.SysUtils
import com.howbuy.login.LoginManagerImpl
import com.howbuy.router.proxy.Invoker

/**
 * description.
 * tao.liang
 * 2024/2/26
 */
@Route(path = LoginRouterPath.PATH_LOGIN_PROVIDER)
class LoginProvider : ILoginProvider {

    override fun getLoginInfo(fromMemory: Boolean): LoginTypeInf? {
        return LoginAccountMgr.getLoginInfoFrom(fromMemory)
    }

    override fun saveLoginInfo(
        hkCustNo: String,
        loginType: LoginType,
        accountType: String,
        acountNo: String,
        phoneAreaCode: String
    ) {
        LoginInfMgr.loginTypeInfo?.refreshUserInfo(
            hkCustNo,
            loginType,
            accountType,
            acountNo,
            phoneAreaCode
        )
        LoginAccountMgr.saveLoginInfo(LoginInfMgr.loginTypeInfo)
    }

    override fun getHkCustNo(): String? {
        return LoginInfMgr.loginTypeInfo?.hkCustNo
    }

    override fun isLogined(): Boolean {
        return LoginInfMgr.loginTypeInfo?.isLogined == true
    }

    override fun launcherToAtyMain(
        tabIndex: Int,
        bundle: Bundle?,
        navCompleteCallback: NavigationCallback?
    ) {
        LoginAccountMgr.launcherToAtyMain(tabIndex, bundle, navCompleteCallback)
    }

    override fun canLauncherActivePage(): Boolean {
        return LoginAccountMgr.canLauncherActivePage()
    }

    override fun gotoActivePage(
        activity: Any?,
        forceActivedH5PageLink: String?,
        fromPage: String,
        callback: NavCompleteCallback?
    ) {
        LoginAccountMgr.gotoActivePage(
            activity, forceActivedH5PageLink,
            fromPage,
            callback
        )
    }

    override fun gotoLoginActivePage(activity: Any?, fromPage: String, callback: NavCompleteCallback?) {
        LoginAccountMgr.gotoLoginActivePage(activity, fromPage, callback)
    }

    override fun getTargetClass(activityPath: String?): Class<*>? {
        return RouterHelper.getClassWithPath(activityPath)
    }

    override fun getTargetActivity(activityPath: String): Activity? {
        val clsMain = RouterHelper.getClassWithPath(activityPath)
        //Stack 倒叙遍历
        for (i in AtyMgr.getAtys().indices.reversed()) {
            val aty = AtyMgr.getAtys()[i]
            if (aty.javaClass == clsMain) {
                return aty
            }
        }
        return null
    }

    override fun login(params: LoginParams, fromPage: String?, callback: LoginCallback?) {
        //跳转登录页面,需要退出登录
        LoginAccountMgr.loginOut()
        val path = LoginRouterPath.PATH_USER_FRAG_LOGIN_BRIDGE
        create(
            params.context,
            path,
            getBundle(params, fromPage)
        )
            .listenResult { resultCode, intent ->
                //登录取消回调
                if (resultCode == Activity.RESULT_CANCELED) {
                    callback?.onComplete(LoginResult(false, null, true))
                } else {
                    callback?.onComplete(LoginResult(true, null, false, intent?.extras))
                }
                true
            }
            .execute()
    }

    override fun loginOut() {
        LoginAccountMgr.loginOut()
    }

    /**
     * 根据登录的类型，获取对应的bundle参数
     */
    private fun getBundle(params: LoginParams, fromPage: String?): Bundle {
        var bundle = params.params
        if (bundle == null) {
            bundle = Bundle()
        }
        params.type?.let {
            when (it) {
                LoginType.password -> {
                    bundle.putInt(ValConfig.IT_TYPE, 1)
                }

                LoginType.captcha -> {
                    bundle.putInt(ValConfig.IT_TYPE, 0)
                }

                else -> {
                    bundle.putInt(ValConfig.IT_TYPE, 0)
                }
            }
        }
        bundle.putString(ValConfig.IT_NAME, fromPage)
        bundle.putBoolean(ValConfig.IT_NEED_ACTIVE, params.needActive)
        return bundle
    }

    override fun getMainActivityParamskeyIndex(): String {
        return "TAB_INDEX"
    }

    override fun tradeUnActivedDlg() {

    }

    override fun needForceRelogin(): Boolean {
        return LoginAccountMgr.needForceReLogin
    }

    override fun setNeedForceRelogin(needRelogin: Boolean) {
        LoginAccountMgr.needForceReLogin = needRelogin
    }

    override fun hasFinger(context: Context): Boolean {
        return FingerHelper.hasFinger(context)
    }

    override fun showGestureSettingHintDialog(childFragmentManager: FragmentManager, callback: ((Int) -> Unit)?) {
        DlgGestureHint.showDialog(childFragmentManager, callback)
    }

    override fun launchFingerOrGestureLogin(
        context: Context,
        fingerResultConsumer: com.howbuy.gesture.consumer.FingerResultConsumer
    ): Boolean {
        return FingerHelper.launchFingerOrGestureLogin(context, fingerResultConsumer)
    }

    override fun executeGestureLogin(
        activity: Activity,
        fingerResultConsumer: com.howbuy.gesture.consumer.FingerResultConsumer
    ): Boolean {
        FingerHelper.executeGestureLogin(activity, fingerResultConsumer)
        return false
    }

    override fun launchModifyGestureSetting(activity: Activity?) {
        activity ?: return
        FingerHelper.launchModifyGestureSetting(activity) { _, _ -> }
    }

    override fun showCheckActiveStatusDlg(
        activity: Activity?,
        btnLeft: String,
        btnRight: String,
        title: String,
        msg: String,
        callback: NavCompleteCallback?
    ) {
        activity ?: return
        if (activity is FragmentActivity) {
            GlobalHintDialog.getInstance(null, object : GlobalHintDialog.IGlobalHintDlgListener {
                override fun onClick(clickType: GlobalHintDialog.CLiCK_DLG_BTN_TYPE) {
                    if (clickType == GlobalHintDialog.CLiCK_DLG_BTN_TYPE.CLICK_SURE) {
                        //通过Activity.RESULT_OK来标识,点击的是 确定按钮
                        callback?.onNavComplete(Activity.RESULT_OK, null, null)
                    } else {
                        //通过Activity.RESULT_CANCELED来标识,点击的是取消按钮/按钮按钮,点击屏幕消失
                        callback?.onNavComplete(Activity.RESULT_CANCELED, null, null)
                    }
                }
            })
                .setTitle(title)
                .setMessage(msg)
                .setBtnCancelText(btnLeft)
                .setBtnSureText(btnRight)
                .show(activity.supportFragmentManager, null)
        }
    }

    override fun showPhoneArea(activity: Activity, callback: NavCompleteCallback?) {
        LoginAccountMgr.launcherWebPageByUriKey(activity, LoginConstants.LOGIN_AREA_CHECKED, "", false, null, callback)
    }

    /**
     * 组装 H5 JS交互函数 jsParams() 需要的所有参数
     * @param userInfoForJs 如果为空,则取 用户管理中的缓存数据,如果不为空,强制将当前参数中的内容值给map字段
     */
    override fun getUserInfoParams(userInfoForJs: TempUserInfoForJs?): MutableMap<String, Any?> {
        val userMap = mutableMapOf<String, Any?>()
        //        hkCustNo：香港客户号(个人信息接口)
        userMap.put(
            "hkCustNo",
            if (userInfoForJs == null) {
                apiUserInfo().getHkCustNo()
            } else {
                userInfoForJs.hkCustNo
            }
        )

        //        hboneNo：服务端待定(个人信息接口)
        userMap.put(
            "hboneNo",
            if (userInfoForJs == null) {
                apiUserInfo().getHboneNo()
            } else {
                userInfoForJs.hboneNo
            }
        )

        // 登录是否激活  1:是 已经激活    0：否 没有激活
        userMap.put(
            "loginActivate", if (userInfoForJs == null) {
                if (apiUserInfo().loginAccountUnActive()) "0" else "1"
            } else {
                if (userInfoForJs.loginActivate == "0") "0" else "1"
            }
        )

        //交易是否激活   1:是 已经激活    0：否 没有激活
        userMap.put(
            "transactionActivation",
            if (userInfoForJs == null) {
                if (apiUserInfo().tradeAccountUnActive()) "0" else "1"
            } else {
                if (userInfoForJs.transactionActivation == "0") "0" else "1"
            }
        )

        //840:美元/ 156:人民币
        val amtTypeCode =
            getString(
                SpConfig.USER_SELECT_AMT_TYPE + "_" + if (userInfoForJs == null) {
                    apiUserInfo().getHkCustNo()
                } else {
                    userInfoForJs.hkCustNo
                }, "840"
            )
        userMap.put("currency", amtTypeCode)

        //        mobileMask：手机号掩码，无则传空(个人信息接口)
        userMap.put(
            "mobileMask",
            if (userInfoForJs == null) {
                apiUserInfo().mobileMask()
            } else {
                userInfoForJs.mobileMask
            }
        )

        //        mobileDigest：手机号摘要，无则传空(个人信息接口)
        userMap.put(
            "mobileDigest",
            if (userInfoForJs == null) {
                apiUserInfo().mobileDigest()
            } else {
                userInfoForJs.mobileDigest
            }
        )

        //        emailMask：邮箱掩码，无则传空(个人信息接口)
        userMap.put(
            "emailMask",
            if (userInfoForJs == null) {
                apiUserInfo().emailMask()
            } else {
                userInfoForJs.emailMask
            }
        )

        //        emailDigest：邮箱摘要，无则传空(个人信息接口)
        userMap.put(
            "emailDigest",
            if (userInfoForJs == null) {
                apiUserInfo().emailDigest()
            } else {
                userInfoForJs.emailDigest
            }
        )

        //        acountType: 证件类型描述（使用字段idTypeDesc）(个人信息接口)
        userMap.put(
            "acountType",
            if (userInfoForJs == null) {
                apiUserInfo().getidTypeDesc()
            } else {
                userInfoForJs.idTypeDesc
            }
        )

        //        idType: 证件类型代码(个人信息接口)
        userMap.put(
            "idType",
            if (userInfoForJs == null) {
                apiUserInfo().getIdType()
            } else {
                userInfoForJs.idType
            }
        )

        //        custName：用户姓名（使用字段custName）(个人信息接口)
        userMap.put(
            "custName",
            if (userInfoForJs == null) {
                apiUserInfo().getCustName()
            } else {
                userInfoForJs.custName
            }
        )

        //        birthday：生日（yyyyMMdd）(个人信息接口)
        userMap.put(
            "birthday",
            if (userInfoForJs == null) {
                apiUserInfo().getBirthday()
            } else {
                userInfoForJs.birthday
            }
        )

        //投资者类型: PRO/NORMAL
        userMap.put(
            "investorType",
            if (userInfoForJs == null) {
                apiUserInfo().investorType()
            } else {
                userInfoForJs.investorType
            }
        )


        //        loginType: 'phone'：验证码登录或大陆手机号登录(+86)，其他登录方式传空(本地用户选择的)
        val loginInfo = Invoker.getInstance().navigation(
            ILoginProvider::class.java
        ).getLoginInfo(false)

        //大陆手机号
        if (loginInfo != null) {
            val matherPhone = TextUtils.equals(loginInfo.accountType, AccountType.PHONE.code) &&
                    TextUtils.equals(loginInfo.phoneAreaCode, "+86")
            if (loginInfo.loginType == LoginType.captcha || matherPhone) {
                userMap["loginType"] = "phone"
            } else {
                userMap["loginType"] = ""
            }
            if (TextUtils.equals(loginInfo.accountType, AccountType.CARD.code)) {
                //只有当是证件号登录时,才会赋值 acountNo：明文的证件号(本地用户输入的)
                userMap["acountNo"] = loginInfo.acountNo
            } else {
                userMap["acountNo"] = ""
            }
            //登录方式(验证码登录时, 传 "1", 非验证码登录: 非1)
            userMap["captchaLogin"] = if (loginInfo.loginType == LoginType.captcha) "1" else "0"
        } else {
            userMap["loginType"] = ""
            userMap["acountNo"] = ""
            userMap["captchaLogin"] = "0"
        }


        //        version：app版本号
        userMap["version"] = getString(SpConfig.SFfirstVersion, "")

        //        deviceId：设备号
        userMap["deviceId"] = GlobalApp.getApp().publicParams["deviceId"]

        //        token：设备唯一标识
        userMap["token"] = getString(SpConfig.SFfirstUUid, "")

        //        tokenId：设备唯一标识
        userMap["tokenId"] = getString(SpConfig.SFfirstUUid, "")

        //        productId：产品代码
        userMap["productId"] = GlobalApp.getApp().publicParams["productId"]

        //        channelId：渠道号
        userMap["channelId"] = ApkConfig.getChannelId()

        //        parPhoneModel：主机型
        userMap["parPhoneModel"] = getString(SpConfig.SFfirstParPhoneModel, "")

        //        subPhoneModel：子机型
        userMap["subPhoneModel"] = getString(SpConfig.SFfirstSubPhoneModel, "")

        //       设备屏幕信息,传dp单位
        userMap["deviceWidth"] =
            DensityUtils.px2dp(SysUtils.getWidth(GlobalApp.getApp()).toFloat())
        userMap["deviceHeight"] =
            DensityUtils.px2dp(SysUtils.getHeight(GlobalApp.getApp()).toFloat())
        userMap["statusBarHeight"] =
            DensityUtils.px2dp(SysUtils.getStatusBarHeight(GlobalApp.getApp()).toFloat())

        //        wifi,  网络类型
        userMap["wifi"] =
            if (SysUtils.getNetType(GlobalApp.getApp()) <= 1) "none" else NetworkUtils.getNetwork(
                GlobalApp.getApp()
            )
        LogUtils.d("JsFunctionaction", "pre size:" + userMap.size)
        LogUtils.d("JsFunctionaction", "pre data:" + GsonUtils.toJson(userMap))

        return userMap
    }

    override fun loginAgreementCheck(
        view: TextView,
        consumer: Consumer<Boolean>,
        analyticsCallback: Consumer<String>?
    ): ILoginPrivateAgreementDelegate {
        return LoginPrivateAgreementDelegate(view, consumer, analyticsCallback).create()
    }

    override fun afterLoginSuccess(fragment: Fragment) {
        //登录成功之后的操作
        //监听请求用户信息接口返回的数据状态,处理激活流程
        UserDataHelper.getDataManager(DataIds.ID_USER_INFO)
            .forceFetchWithCallback(object : Consumer<ResponseValue<Any>> {
                override fun accept(t: ResponseValue<Any>?) {
                    if (t?.data == null || t.error != null) {
                        LoginAccountMgr.loginOut()
                        if (t?.error != null) {
                            LogUtils.pop("用户信息获取失败")
                        }
                        return
                    }
                    LogUtils.d(
                        "LoginAccountMgr",
                        "登录后,调用用户信息结果成功:" + t.data.toString()
                    )
                    if (com.howbuy.global.data_api.isLogined()) {
                        //用户信息状态处理
                        LogUtils.d(
                            "LoginAccountMgr",
                            "login status=" + com.howbuy.global.data_api.isLogined() + " , 可以继续后续流程处理..."
                        )
//                        先弹框提示
                        showSetFingerOrGestureDialogHint(fragment)
                    } else {
                        LogUtils.d(
                            "LoginAccountMgr",
                            "login status=" + com.howbuy.global.data_api.isLogined() + " , 无法继续后续流程处理..."
                        )
                    }
                }
            })
    }


    private fun showSetFingerOrGestureDialogHint(fragment: Fragment) {
        Invoker.getInstance().navigation(ILoginProvider::class.java)
            .showGestureSettingHintDialog(fragment.childFragmentManager) {
                if (it != DlgGestureHint.EMPTY) {
                    //跳转到手势密码/指纹设置页面
                    FingerHelper.launcherFingerOrGesture(
                        fragment.activity ?: AtyMgr.getFirstExistAty(), it == DlgGestureHint.FINGER
                    ) { _: Int, _: Intent? ->
                        handLogicAfterLoginSuccess(fragment)
                    }
                } else {
                    handLogicAfterLoginSuccess(fragment)
                }
            }
    }

    /**
     * 登录成功后的业务逻辑处理
     * 先判断外部传入的IT_NEED_ACTIVE，是否需要后续激活跳转
     * 触发登录状态: oginManagerImpl.getInstance().publishLoginState(true)
     *    a. 未开户, 可调用
     *    b. 无须激活,可调用
     *    c. 要激活, 且激活成功, 可调用
     *    其它情况,无须调用
     */
    private fun handLogicAfterLoginSuccess(fragment: Fragment) {
        //用户数据
        val finishOpenAccount = apiUserInfo().finishOpenAccount()
        LogUtils.d(
            "LoginAccountMgr",
            "now FragLoginPage login sueccess, user finishOpenAccount status=$finishOpenAccount"
        )
        if (!finishOpenAccount) {
            LogUtils.d("LoginAccountMgr", "未开户,无须激活,定位我的页面")
            //a. 若未开户，则登录后默认定位在“我的”TAB页
            val paramsKeyIndex = Invoker.getInstance().navigation(ILoginProvider::class.java)
                .getMainActivityParamskeyIndex()
            val bundle = NavHelper.obtainArg("", paramsKeyIndex, 1)
            NavHelper.success(fragment.activity, bundle)
            //发送已登录通知
            LoginManagerImpl.getInstance().publishLoginState(true)
        } else {
            /**
             * b. 已开户状态（必须是已开户状态finishOpenAccount=true）
             * 默认激活流程与登录流程绑定在一起， 即： 登录成功后，做激活流程处理；
             * 但是存在某些场景，只需要登录，不需要激活，则通过 needActive字段来判断。
             */
            LogUtils.d("LoginAccountMgr", "已开户,须校验激活状态")
            if (LoginAccountMgr.canLauncherActivePage()) {
                Invoker.getInstance()
                    .navigation(ILoginProvider::class.java).gotoActivePage(
                        fragment.activity, null, "FragLoginPage"
                    ) { resultCode, _, _ ->
                        LogUtils.d(
                            "LoginAccountMgr",
                            "FragLoginPage收到H5激活操作callack结果=$resultCode"
                        )
                        if (resultCode == Activity.RESULT_OK) {
                            //通过bundle 返回ValConfig.IY_FROM=true 标记为是从激活流程操作返回的结果
                            NavHelper.success(
                                fragment.activity,
                                NavHelper.obtainArg("", ValConfig.IT_FROM, true)
                            )
                            LogUtils.d("LoginAccountMgr", "激活成功, 回到入口页面")
                            //发送已登录通知
                            LoginManagerImpl.getInstance().publishLoginState(true)
                        } else {
                            LoginAccountMgr.loginOut()
                            NavHelper.fail(fragment.activity, null)
                            LogUtils.d("LoginAccountMgr", "激活失败, 回到入口页面")
                        }
                    }
            } else {
                LogUtils.d("LoginAccountMgr", "已开户,已激活,返回 - RESULT_OK")
                NavHelper.success(fragment.activity, null)
                //发送已登录通知
                LoginManagerImpl.getInstance().publishLoginState(true)
            }
        }
    }

    override fun init(context: Context?) {

    }
}