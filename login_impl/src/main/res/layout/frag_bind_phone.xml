<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_content"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical"
    android:clipChildren="false">

    <TextView
        android:text="绑定手机号"

        android:layout_marginTop="23dp"
        android:textSize="29sp"
        android:textColor="@color/cl_2a3050"
        android:layout_marginLeft="24dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:layout_marginLeft="24dp"
        android:layout_marginRight="15dp"
        android:text="未注册的手机号验证后自动登录并创建账号"
        android:textColor="@color/cl_4b4d61"
        android:textSize="14sp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:layout_marginLeft="@dimen/margin_15"
        android:layout_marginRight="15dp"
        android:background="@drawable/fd_bg_item_all_frame"
        android:gravity="center_vertical"
        android:minHeight="55dp"
        android:orientation="horizontal"
        android:paddingLeft="12dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="手机号(+86)"
            android:textColor="#ff2a3050"
            android:textSize="16sp" />

        <com.howbuy.component.widgets.ClearableEdittext
            android:id="@+id/input_phone"
            android:layout_width="match_parent"
            android:layout_height="55dp"
            android:layout_marginStart="10dp"
            android:layout_weight="1"
            android:background="@null"
            android:hint="请输入大陆手机号"
            android:imeOptions="actionNext"
            android:inputType="number"
            android:maxLength="11"
            android:maxLines="1"
            android:textColor="#ff2a3050"
            android:textColorHint="#BBBDCB"
            android:textSize="18sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:layout_marginLeft="@dimen/margin_15"
        android:layout_marginRight="15dp"
        android:background="@drawable/fd_bg_item_all_frame"
        android:gravity="center_vertical"
        android:minHeight="55dp"
        android:orientation="horizontal"
        android:paddingLeft="12dp">

        <com.howbuy.component.widgets.ClearableEdittext
            android:id="@+id/cet_auth_code"
            android:layout_width="wrap_content"
            android:layout_height="55dp"
            android:layout_weight="1"
            android:background="@null"
            android:hint="请输入6位验证码"
            android:imeOptions="actionNext"
            android:inputType="number"
            android:maxLength="6"
            android:textColor="#ff2a3050"
            android:textColorHint="#ffbbbdcb"
            android:textSize="18sp" />

        <View
            android:layout_width="1dp"
            android:layout_height="30dp"
            android:layout_gravity="center"
            android:background="#E5E5E5" />

        <FrameLayout
            android:layout_width="wrap_content"
            android:layout_height="55dp">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_auth_code_sender"
                android:layout_width="120dp"
                android:layout_height="55dp"
                android:clickable="true"
                android:enabled="true"
                android:gravity="center"
                android:lines="1"
                android:minWidth="120dp"
                android:paddingLeft="15dp"
                android:paddingRight="10dp"
                android:text="获取验证码"
                android:textColor="@color/tv_send_verify_code_enable"
                android:textSize="15sp"
                app:autoSizeMaxTextSize="16sp"
                app:autoSizeMinTextSize="12sp"
                app:autoSizeTextType="uniform" />

            <View
                android:id="@+id/view_click_verify"
                android:layout_width="120dp"

                android:layout_height="wrap_content" />

        </FrameLayout>

    </LinearLayout>


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">


        <TextView
            android:id="@+id/tv_login"
            android:layout_width="match_parent"
            android:layout_below="@id/cb_agreement"
            android:layout_height="44dp"
            android:layout_marginTop="54dp"
            android:layout_marginLeft="@dimen/margin_15"
            android:layout_marginRight="15dp"
            android:background="@drawable/btn_login_bg_selector"
            android:enabled="false"
            android:gravity="center"
            android:layout_marginBottom="20dp"
            android:text="登录"
            android:textColor="@color/white"
            android:textSize="18sp" />

        <CheckBox
            android:id="@+id/cb_agreement"
            style="@style/login_checkbox_protocol"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:checked="false"
            android:gravity="center_vertical"
            android:paddingLeft="6dp"
            android:layout_marginTop="11dp"
            android:text="我已阅读并同意"
            android:textColor="#2A3050"
            android:textSize="12sp"
            tools:checked="true" />

        <com.howbuy.fund.base.widget.BubbleView
            android:id="@+id/lay_bind_hint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_above="@+id/cb_agreement"
            android:layout_marginLeft="2dp"
            android:paddingStart="5dp"
            android:paddingTop="1dp"
            android:layout_marginBottom="-3dp"
            android:paddingEnd="5dp"
            android:paddingBottom="2dp"
            android:visibility="gone"
            app:bubbleColor="#4B4E61"
            app:bubbleIndicatorDirection="bottom"
            app:bubbleIndicatorHeight="5dp"
            app:bubbleIndicatorLocation="16dp"
            app:bubbleRadius="5dp"
            tools:visibility="visible">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="请勾选"
                android:textColor="#ffffffff"
                android:textSize="13sp" />

        </com.howbuy.fund.base.widget.BubbleView>
    </RelativeLayout>


</LinearLayout>
